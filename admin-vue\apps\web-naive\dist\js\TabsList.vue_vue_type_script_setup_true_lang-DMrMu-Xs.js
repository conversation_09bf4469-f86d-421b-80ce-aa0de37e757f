var f=Object.getOwnPropertySymbols;var b=Object.prototype.hasOwnProperty,v=Object.prototype.propertyIsEnumerable;var r=(s,a)=>{var t={};for(var e in s)b.call(s,e)&&a.indexOf(e)<0&&(t[e]=s[e]);if(s!=null&&f)for(var e of f(s))a.indexOf(e)<0&&v.call(s,e)&&(t[e]=s[e]);return t};import{S as h,T as B,U as C,W as y}from"./bootstrap-B_sue86n.js";import{d as c,A as d,l as i,B as u,v as p,M as P,N as $,s as n,c as m,O as _,t as g}from"../jse/index-index-UaL0SrHU.js";const M=c({__name:"Tabs",props:{defaultValue:{},orientation:{},dir:{},activationMode:{},modelValue:{},asChild:{type:Boolean},as:{}},emits:["update:modelValue"],setup(s,{emit:a}){const l=h(s,a);return(o,x)=>(i(),d(n(B),P($(n(l))),{default:u(()=>[p(o.$slots,"default")]),_:3},16))}}),S=c({__name:"TabsContent",props:{value:{},forceMount:{type:Boolean},asChild:{type:Boolean},as:{},class:{}},setup(s){const a=s,t=m(()=>{const o=a,{class:e}=o;return r(o,["class"])});return(e,l)=>(i(),d(n(C),_({class:n(g)("ring-offset-background focus-visible:ring-ring mt-2 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-offset-2",a.class)},t.value),{default:u(()=>[p(e.$slots,"default")]),_:3},16,["class"]))}}),V=c({__name:"TabsList",props:{loop:{type:Boolean},asChild:{type:Boolean},as:{},class:{}},setup(s){const a=s,t=m(()=>{const o=a,{class:e}=o;return r(o,["class"])});return(e,l)=>(i(),d(n(y),_(t.value,{class:n(g)("bg-muted text-muted-foreground inline-flex h-9 items-center justify-center rounded-lg p-1",a.class)}),{default:u(()=>[p(e.$slots,"default")]),_:3},16,["class"]))}});export{M as _,V as a,S as b};
