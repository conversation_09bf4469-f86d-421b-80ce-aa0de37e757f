<?php /*a:3:{s:81:"D:\qianhouduankaifabao\shangchengquan\shangcheng\app\home\she_zhen_set\index.html";i:1752768928;s:73:"D:\qianhouduankaifabao\shangchengquan\shangcheng\app\home\public\css.html";i:1745486434;s:72:"D:\qianhouduankaifabao\shangchengquan\shangcheng\app\home\public\js.html";i:1745486434;}*/ ?>
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>智能诊疗设置</title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0, user-scalable=0">
    <link rel="stylesheet" type="text/css" href="/static/admin/layui/css/layui.css?v=20200516" media="all">
<link rel="stylesheet" type="text/css" href="/static/admin/layui/css/modules/formSelects-v4.css?v=20200516" media="all">
<link rel="stylesheet" type="text/css" href="/static/admin/css/admin.css?v=20210826" media="all">
<link rel="stylesheet" type="text/css" href="/static/admin/css/font-awesome.min.css?v=20200516" media="all">
<link rel="stylesheet" type="text/css" href="/static/admin/webuploader/webuploader.css?v=<?php echo time(); ?>" media="all">
<link rel="stylesheet" type="text/css" href="/static/imgsrc/designer.css?v=20220803" media="all">
<link rel="stylesheet" type="text/css" href="/static/fonts/iconfont.css?v=20201218" media="all">
</head>
<body>
    <div class="layui-fluid">
        <div class="layui-row layui-col-space15">
            <div class="layui-col-md12">
                <div class="layui-card">
                    <div class="layui-card-header">
                        <i class="fa fa-cog"></i> 智能诊疗设置
                        <i class="layui-icon layui-icon-close" style="font-size:18px;font-weight:bold;cursor:pointer" onclick="closeself()"></i>
                    </div>
                    <div class="layui-card-body" pad15>
                        <form class="layui-form" id="configForm">
                            <div class="layui-tab" lay-filter="settab">
                                <ul class="layui-tab-title">
                                    <li class="layui-this" lay-id="1">基础配置</li>
                                    <li lay-id="2">显示设置</li>
                                    <li lay-id="3">阿里云配置</li>
                                    <li lay-id="4">数据统计</li>
                                    <li lay-id="5">商品推荐</li>
                                </ul>
                                <div class="layui-tab-content">
                                    <!-- 基础配置 -->
                                    <div class="layui-tab-item layui-show">
                                        <div class="layui-form-item">
                                            <label class="layui-form-label">舌诊功能</label>
                                            <div class="layui-input-block">
                                                <input type="radio" name="info[is_open]" value="1" <?php if(!$set['id'] || $set['is_open']==1): ?>checked<?php endif; ?> title="开启" />
                                                <input type="radio" name="info[is_open]" value="0" <?php if($set['id'] && $set['is_open']==0): ?>checked<?php endif; ?> title="关闭" />
                                                <div class="layui-form-mid layui-word-aux">关闭后用户无法使用舌诊功能</div>
                                            </div>
                                        </div>

                                        <div class="layui-form-item">
                                            <label class="layui-form-label">面诊功能</label>
                                            <div class="layui-input-block">
                                                <input type="radio" name="info[face_diagnosis_enable]" value="1" <?php if(!$set['id'] || $set['face_diagnosis_enable']==1): ?>checked<?php endif; ?> title="开启" />
                                                <input type="radio" name="info[face_diagnosis_enable]" value="0" <?php if($set['id'] && $set['face_diagnosis_enable']==0): ?>checked<?php endif; ?> title="关闭" />
                                                <div class="layui-form-mid layui-word-aux">开启后用户可以使用面诊功能</div>
                                            </div>
                                        </div>

                                        <div class="layui-form-item">
                                            <label class="layui-form-label">综合诊疗功能</label>
                                            <div class="layui-input-block">
                                                <input type="radio" name="info[comprehensive_diagnosis_enable]" value="1" <?php if(!$set['id'] || $set['comprehensive_diagnosis_enable']==1): ?>checked<?php endif; ?> title="开启" />
                                                <input type="radio" name="info[comprehensive_diagnosis_enable]" value="0" <?php if($set['id'] && $set['comprehensive_diagnosis_enable']==0): ?>checked<?php endif; ?> title="关闭" />
                                                <div class="layui-form-mid layui-word-aux">开启后用户可以使用综合诊疗功能（舌诊+面诊+舌下脉络）</div>
                                            </div>
                                        </div>

                                        <div class="layui-form-item">
                                            <label class="layui-form-label">舌诊价格</label>
                                            <div class="layui-input-block">
                                                <input type="number" name="info[single_price]" value="<?php echo (isset($set['single_price']) && ($set['single_price'] !== '')?$set['single_price']:'9.9'); ?>" step="0.01" min="0" placeholder="请输入舌诊单次价格" class="layui-input" style="width: 200px;">
                                                <div class="layui-form-mid layui-word-aux">元/次</div>
                                            </div>
                                        </div>

                                        <div class="layui-form-item">
                                            <label class="layui-form-label">面诊价格</label>
                                            <div class="layui-input-block">
                                                <input type="number" name="info[face_price]" value="<?php echo (isset($set['face_price']) && ($set['face_price'] !== '')?$set['face_price']:'12.9'); ?>" step="0.01" min="0" placeholder="请输入面诊单次价格" class="layui-input" style="width: 200px;">
                                                <div class="layui-form-mid layui-word-aux">元/次</div>
                                            </div>
                                        </div>

                                        <div class="layui-form-item">
                                            <label class="layui-form-label">综合诊疗价格</label>
                                            <div class="layui-input-block">
                                                <input type="number" name="info[comprehensive_price]" value="<?php echo (isset($set['comprehensive_price']) && ($set['comprehensive_price'] !== '')?$set['comprehensive_price']:'19.9'); ?>" step="0.01" min="0" placeholder="请输入综合诊疗单次价格" class="layui-input" style="width: 200px;">
                                                <div class="layui-form-mid layui-word-aux">元/次</div>
                                            </div>
                                        </div>
                                        
                                        <div class="layui-form-item">
                                            <label class="layui-form-label">舌诊免费等级</label>
                                            <div class="layui-input-block">
                                                <div id="levelCheckbox">
                                                    <?php if(is_array($levelList) || $levelList instanceof \think\Collection || $levelList instanceof \think\Paginator): $i = 0; $__LIST__ = $levelList;if( count($__LIST__)==0 ) : echo "" ;else: foreach($__LIST__ as $key=>$level): $mod = ($i % 2 );++$i;?>
                                                    <input type="checkbox" name="info[free_level][]" value="<?php echo $level['id']; ?>" title="<?php echo $level['name']; if($level['isdefault'] == 1): ?>（默认登录会员）<?php endif; ?>" <?php if(in_array($level['id'], $freeLevelArray)): ?>checked<?php endif; ?>>
                                                    <?php endforeach; endif; else: echo "" ;endif; ?>
                                                </div>
                                                <div class="layui-form-mid layui-word-aux">选择可免费使用舌诊功能的会员等级</div>
                                            </div>
                                        </div>

                                        <div class="layui-form-item">
                                            <label class="layui-form-label">面诊免费等级</label>
                                            <div class="layui-input-block">
                                                <div id="faceLevelCheckbox">
                                                    <?php if(is_array($levelList) || $levelList instanceof \think\Collection || $levelList instanceof \think\Paginator): $i = 0; $__LIST__ = $levelList;if( count($__LIST__)==0 ) : echo "" ;else: foreach($__LIST__ as $key=>$level): $mod = ($i % 2 );++$i;?>
                                                    <input type="checkbox" name="info[face_free_level][]" value="<?php echo $level['id']; ?>" title="<?php echo $level['name']; if($level['isdefault'] == 1): ?>（默认登录会员）<?php endif; ?>" <?php if(in_array($level['id'], $faceFreeLevelArray)): ?>checked<?php endif; ?>>
                                                    <?php endforeach; endif; else: echo "" ;endif; ?>
                                                </div>
                                                <div class="layui-form-mid layui-word-aux">选择可免费使用面诊功能的会员等级</div>
                                            </div>
                                        </div>

                                        <div class="layui-form-item">
                                            <label class="layui-form-label">综合诊疗免费等级</label>
                                            <div class="layui-input-block">
                                                <div id="comprehensiveLevelCheckbox">
                                                    <?php if(is_array($levelList) || $levelList instanceof \think\Collection || $levelList instanceof \think\Paginator): $i = 0; $__LIST__ = $levelList;if( count($__LIST__)==0 ) : echo "" ;else: foreach($__LIST__ as $key=>$level): $mod = ($i % 2 );++$i;?>
                                                    <input type="checkbox" name="info[comprehensive_free_level][]" value="<?php echo $level['id']; ?>" title="<?php echo $level['name']; if($level['isdefault'] == 1): ?>（默认登录会员）<?php endif; ?>" <?php if(in_array($level['id'], $comprehensiveFreeLevelArray)): ?>checked<?php endif; ?>>
                                                    <?php endforeach; endif; else: echo "" ;endif; ?>
                                                </div>
                                                <div class="layui-form-mid layui-word-aux">选择可免费使用综合诊疗功能的会员等级</div>
                                            </div>
                                        </div>

                                        <div class="layui-form-item">
                                            <label class="layui-form-label">舌诊每日免费次数：</label>
                                            <div class="layui-input-inline" style="width:200px">
                                                <input type="text" name="info[daily_free_count]" lay-verify="number" class="layui-input" value="<?php echo (isset($set['daily_free_count']) && ($set['daily_free_count'] !== '')?$set['daily_free_count']:'1'); ?>">
                                            </div>
                                            <div class="layui-form-mid layui-word-aux">每个用户每天可免费使用舌诊的次数</div>
                                        </div>

                                        <div class="layui-form-item">
                                            <label class="layui-form-label">面诊每日免费次数：</label>
                                            <div class="layui-input-inline" style="width:200px">
                                                <input type="text" name="info[face_daily_free_count]" lay-verify="number" class="layui-input" value="<?php echo (isset($set['face_daily_free_count']) && ($set['face_daily_free_count'] !== '')?$set['face_daily_free_count']:'1'); ?>">
                                            </div>
                                            <div class="layui-form-mid layui-word-aux">每个用户每天可免费使用面诊的次数</div>
                                        </div>

                                        <div class="layui-form-item">
                                            <label class="layui-form-label">综合诊疗每日免费次数：</label>
                                            <div class="layui-input-inline" style="width:200px">
                                                <input type="text" name="info[comprehensive_daily_free_count]" lay-verify="number" class="layui-input" value="<?php echo (isset($set['comprehensive_daily_free_count']) && ($set['comprehensive_daily_free_count'] !== '')?$set['comprehensive_daily_free_count']:'1'); ?>">
                                            </div>
                                            <div class="layui-form-mid layui-word-aux">每个用户每天可免费使用综合诊疗的次数</div>
                                        </div>
                                        
                                        <div class="layui-form-item">
                                            <label class="layui-form-label">功能描述：</label>
                                            <div class="layui-input-inline" style="width:500px">
                                                <textarea name="info[description]" class="layui-textarea" style="height:100px"><?php echo (isset($set['description']) && ($set['description'] !== '')?$set['description']:'通过AI智能分析舌头图片，为您提供专业的中医体质分析和健康建议'); ?></textarea>
                                            </div>
                                        </div>
                                        
                                        <div class="layui-form-item">
                                            <label class="layui-form-label">充值页面路径：</label>
                                            <div class="layui-input-inline" style="width:400px">
                                                <input type="text" name="info[recharge_url]" class="layui-input" value="<?php echo (isset($set['recharge_url']) && ($set['recharge_url'] !== '')?$set['recharge_url']:'/pagesA/member/chongzhi'); ?>" placeholder="请输入充值页面路径或URL">
                                            </div>
                                            <div class="layui-form-mid layui-word-aux">用户余额不足时跳转的充值页面路径</div>
                                        </div>
                                    </div>
                                    
                                    <!-- 显示设置 -->
                                    <div class="layui-tab-item">
                                        <div class="layui-card">
                                            <div class="layui-card-header">
                                                前端显示设置
                                                <span style="float:right;color:#FF5722;font-size:12px">
                                                    控制舌诊结果页面显示的内容模块
                                                </span>
                                            </div>
                                            <div class="layui-card-body">
                                                <div class="layui-form-item">
                                                    <div class="layui-input-block">
                                                        <div class="layui-card" style="background:#f8f9fa;margin-bottom:15px">
                                                            <div class="layui-card-body">
                                                                <h4 style="color:#1E9FFF;margin-bottom:10px">
                                                                    <i class="layui-icon layui-icon-tips"></i> 显示设置说明
                                                                </h4>
                                                                <p style="color:#666;line-height:1.6;margin:0">
                                                                    • 可以控制舌诊结果页面中各个模块的显示状态<br>
                                                                    • 关闭的模块将不会在前端用户界面中显示<br>
                                                                    • 这些设置不会影响数据的生成和存储，只影响前端展示<br>
                                                                    • 建议根据用户体验需求来配置显示内容
                                                                </p>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                                
                                                <div class="layui-form-item">
                                                    <label class="layui-form-label">显示评分</label>
                                                    <div class="layui-input-block">
                                                        <input type="radio" name="info[show_score]" value="1" <?php if(!$set['id'] || $set['show_score']==1): ?>checked<?php endif; ?> title="显示" />
                                                        <input type="radio" name="info[show_score]" value="0" <?php if($set['id'] && $set['show_score']==0): ?>checked<?php endif; ?> title="隐藏" />
                                                        <div class="layui-form-mid layui-word-aux">控制是否在结果页面显示体质评分</div>
                                                    </div>
                                                </div>
                                                
                                                <div class="layui-form-item">
                                                    <label class="layui-form-label">显示评分分值</label>
                                                    <div class="layui-input-block">
                                                        <input type="radio" name="info[show_score_value]" value="1" <?php if(!$set['id'] || $set['show_score_value']==1): ?>checked<?php endif; ?> title="显示" />
                                                        <input type="radio" name="info[show_score_value]" value="0" <?php if($set['id'] && $set['show_score_value']==0): ?>checked<?php endif; ?> title="隐藏" />
                                                        <div class="layui-form-mid layui-word-aux">控制是否在评分模块中显示具体分值数字</div>
                                                    </div>
                                                </div>
                                                
                                                <div class="layui-form-item">
                                                    <label class="layui-form-label">显示体征</label>
                                                    <div class="layui-input-block">
                                                        <input type="radio" name="info[show_symptoms]" value="1" <?php if(!$set['id'] || $set['show_symptoms']==1): ?>checked<?php endif; ?> title="显示" />
                                                        <input type="radio" name="info[show_symptoms]" value="0" <?php if($set['id'] && $set['show_symptoms']==0): ?>checked<?php endif; ?> title="隐藏" />
                                                        <div class="layui-form-mid layui-word-aux">控制是否在结果页面显示典型体征症状</div>
                                                    </div>
                                                </div>
                                                
                                                <div class="layui-form-item">
                                                    <label class="layui-form-label">显示舌象分析</label>
                                                    <div class="layui-input-block">
                                                        <input type="radio" name="info[show_tongue_analysis]" value="1" <?php if(!$set['id'] || $set['show_tongue_analysis']==1): ?>checked<?php endif; ?> title="显示" />
                                                        <input type="radio" name="info[show_tongue_analysis]" value="0" <?php if($set['id'] && $set['show_tongue_analysis']==0): ?>checked<?php endif; ?> title="隐藏" />
                                                        <div class="layui-form-mid layui-word-aux">控制是否在结果页面显示舌象特征分析</div>
                                                    </div>
                                                </div>
                                                
                                                <div class="layui-form-item">
                                                    <label class="layui-form-label">显示调理建议</label>
                                                    <div class="layui-input-block">
                                                        <input type="radio" name="info[show_care_advice]" value="1" <?php if(!$set['id'] || $set['show_care_advice']==1): ?>checked<?php endif; ?> title="显示" />
                                                        <input type="radio" name="info[show_care_advice]" value="0" <?php if($set['id'] && $set['show_care_advice']==0): ?>checked<?php endif; ?> title="隐藏" />
                                                        <div class="layui-form-mid layui-word-aux">控制是否在结果页面显示健康调理建议</div>
                                                    </div>
                                                </div>
                                                
                                                <div class="layui-form-item">
                                                    <label class="layui-form-label">显示商品推荐</label>
                                                    <div class="layui-input-block">
                                                        <input type="radio" name="info[show_product_recommend]" value="1" <?php if(!$set['id'] || $set['show_product_recommend']==1): ?>checked<?php endif; ?> title="显示" />
                                                        <input type="radio" name="info[show_product_recommend]" value="0" <?php if($set['id'] && $set['show_product_recommend']==0): ?>checked<?php endif; ?> title="隐藏" />
                                                        <div class="layui-form-mid layui-word-aux">控制是否在结果页面显示相关商品推荐</div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    
                                    <!-- 阿里云配置 -->
                                    <div class="layui-tab-item">
                                        <div class="layui-card">
                                            <div class="layui-card-header">
                                                阿里云API配置
                                                <span style="float:right;color:#FF5722;font-size:12px">
                                                    配置自己的阿里云后将使用您的AppCode，否则使用系统默认配置
                                                </span>
                                            </div>
                                            <div class="layui-card-body">
                                                <div class="layui-form-item">
                                                    <div class="layui-input-block">
                                                        <div class="layui-card" style="background:#f8f9fa;margin-bottom:15px">
                                                            <div class="layui-card-body">
                                                                <h4 style="color:#1E9FFF;margin-bottom:10px">
                                                                    <i class="layui-icon layui-icon-tips"></i> 配置说明
                                                                </h4>
                                                                <p style="color:#666;line-height:1.6;margin:0">
                                                                    • 如果您有自己的阿里云账号，可以配置自己的AccessKey、SecretKey和AppCode<br>
                                                                    • 配置后系统将优先使用您的阿里云服务进行舌诊分析<br>
                                                                    • 如果不配置，系统将使用默认的阿里云配置（可能有调用次数限制）<br>
                                                                    • 建议配置自己的阿里云以获得更好的服务体验
                                                                </p>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                                
                                                <div class="layui-form-item">
                                                    <label class="layui-form-label">AccessKey <span style="color:red">*</span></label>
                                                    <div class="layui-input-block">
                                                        <input type="text" name="info[aliyun_access_key]" value="<?php echo (isset($set['aliyun_access_key']) && ($set['aliyun_access_key'] !== '')?$set['aliyun_access_key']:''); ?>" placeholder="请输入阿里云AccessKey ID" class="layui-input">
                                                        <div class="layui-form-mid layui-word-aux">在阿里云控制台获取AccessKey ID</div>
                                                    </div>
                                                </div>
                                                
                                                <div class="layui-form-item">
                                                    <label class="layui-form-label">SecretKey <span style="color:red">*</span></label>
                                                    <div class="layui-input-block">
                                                        <input type="password" name="info[aliyun_secret_key]" value="<?php echo (isset($set['aliyun_secret_key']) && ($set['aliyun_secret_key'] !== '')?$set['aliyun_secret_key']:''); ?>" placeholder="请输入阿里云AccessKey Secret" class="layui-input">
                                                        <div class="layui-form-mid layui-word-aux">在阿里云控制台获取AccessKey Secret</div>
                                                    </div>
                                                </div>
                                                
                                                <div class="layui-form-item">
                                                    <label class="layui-form-label">API端点</label>
                                                    <div class="layui-input-block">
                                                        <input type="text" name="info[aliyun_endpoint]" value="<?php echo (isset($set['aliyun_endpoint']) && ($set['aliyun_endpoint'] !== '')?$set['aliyun_endpoint']:'https://imagerecog.cn-shanghai.aliyuncs.com'); ?>" placeholder="请输入API端点地址" class="layui-input">
                                                        <div class="layui-form-mid layui-word-aux">默认上海地域端点，一般无需修改</div>
                                                    </div>
                                                </div>
                                                
                                                <div class="layui-form-item">
                                                    <label class="layui-form-label">应用编码 <span style="color:red">*</span></label>
                                                    <div class="layui-input-block">
                                                        <input type="text" name="info[aliyun_app_code]" value="<?php echo (isset($set['aliyun_app_code']) && ($set['aliyun_app_code'] !== '')?$set['aliyun_app_code']:''); ?>" placeholder="请输入阿里云市场应用的AppCode" class="layui-input">
                                                        <div class="layui-form-mid layui-word-aux">在阿里云云市场购买舌诊API服务后获得的AppCode</div>
                                                    </div>
                                                </div>
                                                
                                                <div class="layui-form-item">
                                                    <div class="layui-input-block">
                                                        <button type="button" class="layui-btn layui-btn-normal" onclick="testApiConnection()">
                                                            <i class="layui-icon layui-icon-ok"></i> 测试API连接
                                                        </button>
                                                        <span style="margin-left:15px;color:#666">
                                                            配置状态：
                                                            <?php if(!empty($set['aliyun_access_key']) && !empty($set['aliyun_secret_key']) && !empty($set['aliyun_app_code'])): ?>
                                                                <span style="color:#52c41a"><i class="layui-icon layui-icon-ok"></i> 已配置自定义阿里云</span>
                                                            <?php else: ?>
                                                                <span style="color:#faad14"><i class="layui-icon layui-icon-close"></i> 使用系统默认配置</span>
                                                            <?php endif; ?>
                                                        </span>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    
                                    <!-- 数据统计 -->
                                    <div class="layui-tab-item">
                                        <div class="layui-row layui-col-space15">
                                            <div class="layui-col-md3">
                                                <div class="layui-card">
                                                    <div class="layui-card-header">当前平台面诊次数</div>
                                                    <div class="layui-card-body" style="text-align:center">
                                                        <div style="font-size:32px;color:#722ed1;font-weight:bold"><?php echo (isset($statistics['current_mianzhen_num']) && ($statistics['current_mianzhen_num'] !== '')?$statistics['current_mianzhen_num']:'0'); ?></div>
                                                        <div style="color:#666">次</div>
                                                        <div style="margin-top:8px;font-size:12px;color:#999">
                                                            <i class="layui-icon layui-icon-tips"></i> 平台可用面诊次数余额
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="layui-col-md3">
                                                <div class="layui-card">
                                                    <div class="layui-card-header">今日分析次数</div>
                                                    <div class="layui-card-body" style="text-align:center">
                                                        <div style="font-size:32px;color:#1E9FFF;font-weight:bold"><?php echo (isset($statistics['today_count']) && ($statistics['today_count'] !== '')?$statistics['today_count']:'0'); ?></div>
                                                        <div style="color:#666">次</div>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="layui-col-md3">
                                                <div class="layui-card">
                                                    <div class="layui-card-header">今日收入</div>
                                                    <div class="layui-card-body" style="text-align:center">
                                                        <div style="font-size:32px;color:#52c41a;font-weight:bold">¥<?php echo (isset($statistics['today_income']) && ($statistics['today_income'] !== '')?$statistics['today_income']:'0'); ?></div>
                                                        <div style="color:#666">元</div>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="layui-col-md3">
                                                <div class="layui-card">
                                                    <div class="layui-card-header">本月分析次数</div>
                                                    <div class="layui-card-body" style="text-align:center">
                                                        <div style="font-size:32px;color:#faad14;font-weight:bold"><?php echo (isset($statistics['month_count']) && ($statistics['month_count'] !== '')?$statistics['month_count']:'0'); ?></div>
                                                        <div style="color:#666">次</div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        
                                        <div class="layui-row layui-col-space15" style="margin-top:15px">
                                            <div class="layui-col-md3">
                                                <div class="layui-card">
                                                    <div class="layui-card-header">本月收入</div>
                                                    <div class="layui-card-body" style="text-align:center">
                                                        <div style="font-size:28px;color:#f5222d;font-weight:bold">¥<?php echo (isset($statistics['month_income']) && ($statistics['month_income'] !== '')?$statistics['month_income']:'0'); ?></div>
                                                        <div style="color:#666">元</div>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="layui-col-md3">
                                                <div class="layui-card">
                                                    <div class="layui-card-header">总分析次数</div>
                                                    <div class="layui-card-body" style="text-align:center">
                                                        <div style="font-size:28px;color:#722ed1;font-weight:bold"><?php echo (isset($statistics['total_count']) && ($statistics['total_count'] !== '')?$statistics['total_count']:'0'); ?></div>
                                                        <div style="color:#666">次</div>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="layui-col-md3">
                                                <div class="layui-card">
                                                    <div class="layui-card-header">总收入</div>
                                                    <div class="layui-card-body" style="text-align:center">
                                                        <div style="font-size:28px;color:#13c2c2;font-weight:bold">¥<?php echo (isset($statistics['total_income']) && ($statistics['total_income'] !== '')?$statistics['total_income']:'0'); ?></div>
                                                        <div style="color:#666">元</div>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="layui-col-md3">
                                                <div class="layui-card">
                                                    <div class="layui-card-header">免费次数</div>
                                                    <div class="layui-card-body" style="text-align:center">
                                                        <div style="font-size:28px;color:#52c41a;font-weight:bold"><?php echo (isset($statistics['free_count']) && ($statistics['free_count'] !== '')?$statistics['free_count']:'0'); ?></div>
                                                        <div style="color:#666">次</div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        
                                        <div class="layui-row layui-col-space15" style="margin-top:15px">
                                            <div class="layui-col-md3">
                                                <div class="layui-card">
                                                    <div class="layui-card-header">付费次数</div>
                                                    <div class="layui-card-body" style="text-align:center">
                                                        <div style="font-size:28px;color:#fa541c;font-weight:bold"><?php echo (isset($statistics['paid_count']) && ($statistics['paid_count'] !== '')?$statistics['paid_count']:'0'); ?></div>
                                                        <div style="color:#666">次</div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        
                                        <div class="layui-card" style="margin-top:15px">
                                            <div class="layui-card-header">实时刷新统计</div>
                                            <div class="layui-card-body">
                                                <button type="button" class="layui-btn layui-btn-normal" onclick="refreshStatistics()">
                                                    <i class="layui-icon layui-icon-refresh"></i> 刷新统计数据
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                    
                                    <!-- 商品推荐设置 -->
                                    <div class="layui-tab-item">
                                        <div class="layui-card">
                                            <div class="layui-card-header">
                                                推荐商品配置
                                                <span style="float:right;color:#FF5722;font-size:12px">
                                                    为不同体质类型配置推荐商品，展示在舌诊结果页面
                                                </span>
                                            </div>
                                            <div class="layui-card-body">
                                                <div class="layui-form-item">
                                                    <div class="layui-input-block">
                                                        <div class="layui-card" style="background:#f8f9fa;margin-bottom:15px">
                                                            <div class="layui-card-body">
                                                                <h4 style="color:#1E9FFF;margin-bottom:10px">
                                                                    <i class="layui-icon layui-icon-tips"></i> 配置说明
                                                                </h4>
                                                                <p style="color:#666;line-height:1.6;margin:0">
                                                                    • 可为每种体质类型配置多个推荐商品<br>
                                                                    • 系统会根据用户的舌诊结果自动推荐相应的商品<br>
                                                                    • 推荐商品会按照排序值从高到低展示<br>
                                                                    • 可以推荐商城商品或者课程
                                                                </p>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                                
                                                <div class="layui-form-item">
                                                    <label class="layui-form-label">推荐功能</label>
                                                    <div class="layui-input-block">
                                                        <input type="radio" name="info[is_recommend_open]" value="1" <?php if(!$set['id'] || $set['is_recommend_open']==1): ?>checked<?php endif; ?> title="开启" />
                                                        <input type="radio" name="info[is_recommend_open]" value="0" <?php if($set['id'] && $set['is_recommend_open']==0): ?>checked<?php endif; ?> title="关闭" />
                                                        <div class="layui-form-mid layui-word-aux">关闭后用户将不会看到推荐商品</div>
                                                    </div>
                                                </div>
                                                
                                                <div class="layui-form-item">
                                                    <label class="layui-form-label">推荐标题</label>
                                                    <div class="layui-input-block">
                                                        <input type="text" name="info[recommend_title]" value="<?php echo (isset($set['recommend_title']) && ($set['recommend_title'] !== '')?$set['recommend_title']:'根据您的舌诊结果，为您推荐以下产品'); ?>" placeholder="请输入推荐商品展示标题" class="layui-input" style="width: 400px;">
                                                    </div>
                                                </div>
                                                
                                                <div class="layui-form-item">
                                                    <label class="layui-form-label">显示数量</label>
                                                    <div class="layui-input-inline" style="width:150px">
                                                        <input type="number" name="info[recommend_count]" value="<?php echo (isset($set['recommend_count']) && ($set['recommend_count'] !== '')?$set['recommend_count']:'3'); ?>" min="1" max="10" placeholder="请输入显示数量" class="layui-input">
                                                    </div>
                                                    <div class="layui-form-mid layui-word-aux">每种体质类型最多展示的商品数量，建议3-6个</div>
                                                </div>
                                                
                                                <div class="layui-form-item">
                                                    <div class="layui-input-block">
                                                        <button type="button" class="layui-btn layui-btn-normal" onclick="openRecommendManager()">
                                                            <i class="layui-icon layui-icon-edit"></i> 管理推荐商品
                                                        </button>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="layui-form-item">
                                <div class="layui-input-block">
                                    <button class="layui-btn layui-btn-danger" lay-submit lay-filter="saveBtn">保存设置</button>
                                    <button type="button" class="layui-btn layui-btn-primary" onclick="resetForm()">重置</button>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script type="text/javascript" src="/static/admin/layui/layui.all.js?v=20210222"></script>
<script type="text/javascript" src="/static/admin/layui/lay/modules/formSelects-v4.js"></script>
<script type="text/javascript" src="/static/admin/js/jquery-ui.min.js?v=20200228"></script>
<script type="text/javascript" src="/static/admin/ueditor/ueditor.js?v=20220707"></script>
<script type="text/javascript" src="/static/admin/ueditor/135editor.js?v=20200228"></script>
<script type="text/javascript" src="/static/admin/webuploader/webuploader.js?v=20200620"></script>
<script type="text/javascript" src="/static/admin/js/qrcode.min.js?v=20200228"></script>
<script type="text/javascript" src="/static/admin/js/dianda.js?v=2022"></script>
    <script>
        layui.use(['form', 'layer', 'element'], function(){
            var form = layui.form;
            var layer = layui.layer;
            var element = layui.element;
            
            // 2025-01-13 16:30:00,100-INFO-[frontend][form_submit_001] 表单提交处理 - 增加显示设置
            form.on('submit(saveBtn)', function(data){
                var loadIndex = layer.load(2);
                $.post('<?php echo url("save"); ?>', data.field, function(res){
                    layer.close(loadIndex);
                    if(res.code == 1){
                        layer.msg(res.msg, {icon: 1});
                    }else{
                        layer.msg(res.msg, {icon: 2});
                    }
                }, 'json');
                return false;
            });
            
            // 2025-01-13 16:30:00,101-INFO-[frontend][test_api_001] API测试功能
            window.testApiConnection = function(){
                var formData = $('#configForm').serialize();
                var loadIndex = layer.load(2);
                $.post('<?php echo url("testApi"); ?>', formData, function(res){
                    layer.close(loadIndex);
                    if(res.code == 1){
                        layer.msg(res.msg, {icon: 1});
                    }else{
                        layer.msg(res.msg, {icon: 2});
                    }
                }, 'json');
            };
            
            // 2025-01-13 16:30:00,102-INFO-[frontend][refresh_statistics_001] 刷新统计数据功能
            window.refreshStatistics = function(){
                var loadIndex = layer.load(2);
                $.get('<?php echo url("getCurrentUsage"); ?>', function(res){
                    layer.close(loadIndex);
                    if(res.code == 1 && res.data){
                        // 刷新页面以显示最新数据
                        layer.msg('统计数据已刷新', {icon: 1}, function(){
                            location.reload();
                        });
                    }else{
                        layer.msg(res.msg || '刷新失败', {icon: 2});
                    }
                }, 'json').fail(function(){
                    layer.close(loadIndex);
                    layer.msg('网络错误，刷新失败', {icon: 2});
                });
            };
            
            // 2025-01-13 16:30:00,103-INFO-[frontend][reset_form_001] 重置表单功能
            window.resetForm = function(){
                layer.confirm('确定要重置表单吗？', {icon: 3, title:'提示'}, function(index){
                    document.getElementById('configForm').reset();
                    form.render();
                    layer.close(index);
                    layer.msg('表单已重置', {icon: 1});
                });
            };
            
            // 2025-01-13 16:30:00,104-INFO-[frontend][open_recommend_manager_001] 打开推荐商品管理页面
            window.openRecommendManager = function(){
                layer.open({
                    type: 2,
                    title: '推荐商品管理',
                    shade: 0.6,
                    area: ['90%', '90%'],
                    content: '<?php echo url("SheZhen/recommendManager"); ?>'
                });
            };
        });
    </script>
</body>
</html> 