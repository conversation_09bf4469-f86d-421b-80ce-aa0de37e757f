<template>
	<view class="comprehensive-diagnosis-container">
		<!-- 头部导航 -->
		<view class="header">
			<view class="nav-bar">
				<view class="nav-left" @tap="goBack">
					<image src="/static/img/back.png" class="back-icon"/>
				</view>
				<view class="nav-title">综合诊疗</view>
				<view class="nav-right"></view>
			</view>
		</view>

		<!-- 主要内容区域 -->
		<view class="main-content">
			<!-- 功能介绍 -->
			<view class="intro-section">
				<view class="intro-card">
					<view class="intro-icon">
						<image :src="pre_url+'/static/img/comprehensive-diagnosis-icon.png'" class="icon"/>
					</view>
					<view class="intro-text">
						<view class="intro-title">综合诊疗分析</view>
						<view class="intro-desc">通过舌诊、面诊、舌下脉络综合分析，全面了解您的健康状况</view>
					</view>
				</view>
			</view>

			<!-- 拍照区域 -->
			<view class="photo-section">
				<!-- 舌头照片 -->
				<view class="photo-card">
					<view class="photo-header">
						<view class="photo-title">舌头照片</view>
						<view class="photo-required">必选</view>
					</view>
					<view class="photo-tips">
						<view class="tip-item">• 伸出舌头，保持自然状态</view>
						<view class="tip-item">• 光线充足，舌头清晰可见</view>
					</view>

					<view class="photo-area" @tap="takeTonguePhoto">
						<view v-if="!tongueImageUrl" class="photo-placeholder">
							<image :src="pre_url+'/static/img/tongue-icon.png'" class="camera-icon"/>
							<view class="placeholder-text">点击拍摄舌头照片</view>
						</view>
						<image v-else :src="tongueImageUrl" class="preview-image" mode="aspectFit"/>
					</view>

					<view class="photo-actions">
						<view class="action-btn retake-btn" @tap="takeTonguePhoto" v-if="tongueImageUrl">
							<image :src="pre_url+'/static/img/retake-icon.png'" class="btn-icon"/>
							<text>重新拍摄</text>
						</view>
						<view class="action-btn upload-btn" @tap="uploadTonguePhoto">
							<image :src="pre_url+'/static/img/upload-icon.png'" class="btn-icon"/>
							<text>从相册选择</text>
						</view>
					</view>
				</view>

				<!-- 面部照片 -->
				<view class="photo-card">
					<view class="photo-header">
						<view class="photo-title">面部照片</view>
						<view class="photo-optional">可选</view>
					</view>
					<view class="photo-tips">
						<view class="tip-item">• 正面拍摄，表情自然</view>
						<view class="tip-item">• 光线充足，避免阴影</view>
					</view>

					<view class="photo-area" @tap="takeFacePhoto">
						<view v-if="!faceImageUrl" class="photo-placeholder">
							<image :src="pre_url+'/static/img/face-icon.png'" class="camera-icon"/>
							<view class="placeholder-text">点击拍摄面部照片</view>
						</view>
						<image v-else :src="faceImageUrl" class="preview-image" mode="aspectFit"/>
					</view>

					<view class="photo-actions">
						<view class="action-btn retake-btn" @tap="takeFacePhoto" v-if="faceImageUrl">
							<image :src="pre_url+'/static/img/retake-icon.png'" class="btn-icon"/>
							<text>重新拍摄</text>
						</view>
						<view class="action-btn upload-btn" @tap="uploadFacePhoto">
							<image :src="pre_url+'/static/img/upload-icon.png'" class="btn-icon"/>
							<text>从相册选择</text>
						</view>
					</view>
				</view>

				<!-- 舌下脉络照片 -->
				<view class="photo-card">
					<view class="photo-header">
						<view class="photo-title">舌下脉络照片</view>
						<view class="photo-optional">可选</view>
					</view>
					<view class="photo-tips">
						<view class="tip-item">• 抬起舌头，露出舌下部分</view>
						<view class="tip-item">• 拍摄舌下血管清晰可见</view>
					</view>

					<view class="photo-area" @tap="takeSublingualPhoto">
						<view v-if="!sublingualImageUrl" class="photo-placeholder">
							<image :src="pre_url+'/static/img/sublingual-icon.png'" class="camera-icon"/>
							<view class="placeholder-text">点击拍摄舌下脉络照片</view>
						</view>
						<image v-else :src="sublingualImageUrl" class="preview-image" mode="aspectFit"/>
					</view>

					<view class="photo-actions">
						<view class="action-btn retake-btn" @tap="takeSublingualPhoto" v-if="sublingualImageUrl">
							<image :src="pre_url+'/static/img/retake-icon.png'" class="btn-icon"/>
							<text>重新拍摄</text>
						</view>
						<view class="action-btn upload-btn" @tap="uploadSublingualPhoto">
							<image :src="pre_url+'/static/img/upload-icon.png'" class="btn-icon"/>
							<text>从相册选择</text>
						</view>
					</view>
				</view>
			</view>

			<!-- 价格信息 -->
			<view class="price-section" v-if="configData">
				<view class="price-card">
					<view class="price-info">
						<view class="price-label">综合诊疗价格</view>
						<view class="price-value" :style="{color:t('color1')}">
							<text v-if="configData.can_use_free">免费</text>
							<text v-else>¥{{configData.price}}</text>
						</view>
					</view>
					<view class="free-info" v-if="configData.is_vip">
						<view class="free-text">今日免费次数：{{configData.free_times - configData.today_used}}/{{configData.free_times}}</view>
					</view>
				</view>
			</view>

			<!-- 开始分析按钮 -->
			<view class="analyze-section">
				<view class="analyze-btn"
					  :style="{background:'linear-gradient(90deg,'+t('color1')+' 0%,rgba('+t('color1rgb')+',0.8) 100%)'}"
					  @tap="startAnalysis"
					  :class="{disabled: !tongueImageUrl || analyzing}">
					<text v-if="analyzing">分析中...</text>
					<text v-else>开始综合诊疗分析</text>
				</view>
			</view>
		</view>

		<!-- 加载提示 -->
		<view class="loading-overlay" v-if="analyzing">
			<view class="loading-content">
				<view class="loading-spinner"></view>
				<view class="loading-text">正在进行综合分析...</view>
			</view>
		</view>
	</view>
</template>

<script>
export default {
	data() {
		return {
			tongueImageUrl: '', // 舌头图片URL
			faceImageUrl: '', // 面部图片URL
			sublingualImageUrl: '', // 舌下脉络图片URL
			configData: null, // 配置数据
			analyzing: false, // 是否正在分析
			diagnosisType: 3, // 综合诊疗类型
			pre_url: '' // 域名前缀
		}
	},
	onLoad() {
		// 初始化云资源前缀URL
		const app = getApp();
		this.pre_url = app.globalData.pre_url || '';

		this.getConfig();
	},
	methods: {
		// 返回上一页
		goBack() {
			uni.navigateBack();
		},

		// 获取配置信息
		getConfig() {
			const app = getApp();

			app.post('ApiSheZhen/getConfig', {
				diagnosis_type: this.diagnosisType
			}, (response) => {
				if (response && response.code === 1) {
					console.log('综合诊疗配置获取成功:', response.data);
					this.configData = response.data;
				} else {
					console.error('获取配置失败:', response);
					uni.showToast({
						title: response.msg || '获取配置失败',
						icon: 'none'
					});
				}
			});
		},

		// 拍摄舌头照片
		takeTonguePhoto() {
			console.log('舌头拍照按钮被点击');

			const app = getApp();
			if (app && app.chooseImage) {
				app.chooseImage((imageUrls) => {
					console.log('舌头图片拍摄并上传成功:', imageUrls);

					if (imageUrls && imageUrls.length > 0) {
						this.tongueImageUrl = imageUrls[0];
						uni.showToast({
							title: '拍摄成功',
							icon: 'success',
							duration: 2000
						});
					}
				}, 1);
			} else {
				uni.chooseImage({
					count: 1,
					sizeType: ['original', 'compressed'],
					sourceType: ['camera'],
					success: (res) => {
						this.tongueImageUrl = res.tempFilePaths[0];
						uni.showToast({
							title: '拍摄成功',
							icon: 'success'
						});
					},
					fail: (err) => {
						console.error('拍摄失败:', err);
						uni.showToast({
							title: '拍摄失败',
							icon: 'none'
						});
					}
				});
			}
		},

		// 从相册选择舌头照片
		uploadTonguePhoto() {
			console.log('舌头选择图片按钮被点击');

			const app = getApp();
			if (app && app.chooseImage) {
				app.chooseImage((imageUrls) => {
					console.log('舌头图片选择并上传成功:', imageUrls);

					if (imageUrls && imageUrls.length > 0) {
						this.tongueImageUrl = imageUrls[0];
						uni.showToast({
							title: '选择成功',
							icon: 'success',
							duration: 2000
						});
					}
				}, 1);
			} else {
				uni.chooseImage({
					count: 1,
					sizeType: ['original', 'compressed'],
					sourceType: ['album'],
					success: (res) => {
						this.tongueImageUrl = res.tempFilePaths[0];
						uni.showToast({
							title: '选择成功',
							icon: 'success'
						});
					},
					fail: (err) => {
						console.error('选择失败:', err);
						uni.showToast({
							title: '选择失败',
							icon: 'none'
						});
					}
				});
			}
		},

		// 拍摄面部照片
		takeFacePhoto() {
			console.log('面部拍照按钮被点击');

			const app = getApp();
			if (app && app.chooseImage) {
				app.chooseImage((imageUrls) => {
					console.log('面部图片拍摄并上传成功:', imageUrls);

					if (imageUrls && imageUrls.length > 0) {
						this.faceImageUrl = imageUrls[0];
						uni.showToast({
							title: '拍摄成功',
							icon: 'success',
							duration: 2000
						});
					}
				}, 1);
			} else {
				uni.chooseImage({
					count: 1,
					sizeType: ['original', 'compressed'],
					sourceType: ['camera'],
					success: (res) => {
						this.faceImageUrl = res.tempFilePaths[0];
						uni.showToast({
							title: '拍摄成功',
							icon: 'success'
						});
					},
					fail: (err) => {
						console.error('拍摄失败:', err);
						uni.showToast({
							title: '拍摄失败',
							icon: 'none'
						});
					}
				});
			}
		},

		// 从相册选择面部照片
		uploadFacePhoto() {
			console.log('面部选择图片按钮被点击');

			const app = getApp();
			if (app && app.chooseImage) {
				app.chooseImage((imageUrls) => {
					console.log('面部图片选择并上传成功:', imageUrls);

					if (imageUrls && imageUrls.length > 0) {
						this.faceImageUrl = imageUrls[0];
						uni.showToast({
							title: '选择成功',
							icon: 'success',
							duration: 2000
						});
					}
				}, 1);
			} else {
				uni.chooseImage({
					count: 1,
					sizeType: ['original', 'compressed'],
					sourceType: ['album'],
					success: (res) => {
						this.faceImageUrl = res.tempFilePaths[0];
						uni.showToast({
							title: '选择成功',
							icon: 'success'
						});
					},
					fail: (err) => {
						console.error('选择失败:', err);
						uni.showToast({
							title: '选择失败',
							icon: 'none'
						});
					}
				});
			}
		},

		// 拍摄舌下脉络照片
		takeSublingualPhoto() {
			console.log('舌下脉络拍照按钮被点击');

			const app = getApp();
			if (app && app.chooseImage) {
				app.chooseImage((imageUrls) => {
					console.log('舌下脉络图片拍摄并上传成功:', imageUrls);

					if (imageUrls && imageUrls.length > 0) {
						this.sublingualImageUrl = imageUrls[0];
						uni.showToast({
							title: '拍摄成功',
							icon: 'success',
							duration: 2000
						});
					}
				}, 1);
			} else {
				uni.chooseImage({
					count: 1,
					sizeType: ['original', 'compressed'],
					sourceType: ['camera'],
					success: (res) => {
						this.sublingualImageUrl = res.tempFilePaths[0];
						uni.showToast({
							title: '拍摄成功',
							icon: 'success'
						});
					},
					fail: (err) => {
						console.error('拍摄失败:', err);
						uni.showToast({
							title: '拍摄失败',
							icon: 'none'
						});
					}
				});
			}
		},

		// 从相册选择舌下脉络照片
		uploadSublingualPhoto() {
			console.log('舌下脉络选择图片按钮被点击');

			const app = getApp();
			if (app && app.chooseImage) {
				app.chooseImage((imageUrls) => {
					console.log('舌下脉络图片选择并上传成功:', imageUrls);

					if (imageUrls && imageUrls.length > 0) {
						this.sublingualImageUrl = imageUrls[0];
						uni.showToast({
							title: '选择成功',
							icon: 'success',
							duration: 2000
						});
					}
				}, 1);
			} else {
				uni.chooseImage({
					count: 1,
					sizeType: ['original', 'compressed'],
					sourceType: ['album'],
					success: (res) => {
						this.sublingualImageUrl = res.tempFilePaths[0];
						uni.showToast({
							title: '选择成功',
							icon: 'success'
						});
					},
					fail: (err) => {
						console.error('选择失败:', err);
						uni.showToast({
							title: '选择失败',
							icon: 'none'
						});
					}
				});
			}
		},



		// 开始分析
		startAnalysis() {
			if (!this.tongueImageUrl) {
				uni.showToast({
					title: '请先拍摄舌头照片',
					icon: 'none'
				});
				return;
			}

			if (this.analyzing) {
				return;
			}

			this.analyzing = true;
			const app = getApp();

			app.post('ApiSheZhen/analyze', {
				diagnosis_type: this.diagnosisType,
				tongue_image_url: this.tongueImageUrl,
				face_image_url: this.faceImageUrl,
				sublingual_image_url: this.sublingualImageUrl,
				use_free: this.configData && this.configData.can_use_free ? 1 : 0
			}, (response) => {
				this.analyzing = false;

				if (response && response.code === 1) {
					// 跳转到结果页面
					uni.navigateTo({
						url: `/pagesB/diagnosis/comprehensive/result?recordId=${response.data.record_id}`
					});
				} else {
					console.error('分析失败:', response);
					uni.showToast({
						title: response.msg || '分析失败，请重试',
						icon: 'none'
					});
				}
			});
		}
	}
}
</script>

<style>
.comprehensive-diagnosis-container {
	min-height: 100vh;
	background: #f5f5f5;
}

.header {
	background: #fff;
	border-bottom: 1px solid #eee;
}

.nav-bar {
	display: flex;
	align-items: center;
	justify-content: space-between;
	height: 88rpx;
	padding: 0 30rpx;
}

.nav-left {
	width: 60rpx;
	height: 60rpx;
	display: flex;
	align-items: center;
	justify-content: center;
}

.back-icon {
	width: 40rpx;
	height: 40rpx;
}

.nav-title {
	font-size: 36rpx;
	font-weight: 600;
	color: #333;
}

.nav-right {
	width: 60rpx;
}

.main-content {
	padding: 30rpx;
}

.intro-section {
	margin-bottom: 30rpx;
}

.intro-card {
	background: #fff;
	border-radius: 20rpx;
	padding: 40rpx;
	display: flex;
	align-items: center;
	box-shadow: 0 4rpx 20rpx rgba(0,0,0,0.05);
}

.intro-icon {
	margin-right: 30rpx;
}

.intro-icon .icon {
	width: 80rpx;
	height: 80rpx;
}

.intro-title {
	font-size: 32rpx;
	font-weight: 600;
	color: #333;
	margin-bottom: 10rpx;
}

.intro-desc {
	font-size: 28rpx;
	color: #666;
	line-height: 1.5;
}

.photo-section {
	margin-bottom: 30rpx;
}

.photo-card {
	background: #fff;
	border-radius: 20rpx;
	padding: 40rpx;
	margin-bottom: 30rpx;
	box-shadow: 0 4rpx 20rpx rgba(0,0,0,0.05);
}

.photo-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 20rpx;
}

.photo-title {
	font-size: 32rpx;
	font-weight: 600;
	color: #333;
}

.photo-required {
	background: #ff4757;
	color: #fff;
	font-size: 22rpx;
	padding: 6rpx 12rpx;
	border-radius: 12rpx;
}

.photo-optional {
	background: #5f27cd;
	color: #fff;
	font-size: 22rpx;
	padding: 6rpx 12rpx;
	border-radius: 12rpx;
}

.photo-tips {
	margin-bottom: 30rpx;
}

.tip-item {
	font-size: 26rpx;
	color: #666;
	margin-bottom: 10rpx;
	line-height: 1.5;
}

.photo-area {
	width: 100%;
	height: 300rpx;
	border: 2rpx dashed #ddd;
	border-radius: 20rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	margin-bottom: 30rpx;
	overflow: hidden;
}

.photo-placeholder {
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
}

.camera-icon {
	width: 60rpx;
	height: 60rpx;
	margin-bottom: 15rpx;
	opacity: 0.6;
}

.placeholder-text {
	font-size: 26rpx;
	color: #999;
}

.preview-image {
	width: 100%;
	height: 100%;
}

.photo-actions {
	display: flex;
	justify-content: space-around;
}

.action-btn {
	display: flex;
	flex-direction: column;
	align-items: center;
	padding: 20rpx;
	border-radius: 15rpx;
	background: #f8f8f8;
	min-width: 120rpx;
}

.btn-icon {
	width: 40rpx;
	height: 40rpx;
	margin-bottom: 10rpx;
}

.action-btn text {
	font-size: 24rpx;
	color: #666;
}

.price-section {
	margin-bottom: 30rpx;
}

.price-card {
	background: #fff;
	border-radius: 20rpx;
	padding: 40rpx;
	box-shadow: 0 4rpx 20rpx rgba(0,0,0,0.05);
}

.price-info {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 20rpx;
}

.price-label {
	font-size: 30rpx;
	color: #333;
}

.price-value {
	font-size: 36rpx;
	font-weight: 600;
}

.free-info {
	text-align: center;
}

.free-text {
	font-size: 26rpx;
	color: #666;
}

.analyze-section {
	margin-bottom: 30rpx;
}

.analyze-btn {
	width: 100%;
	height: 88rpx;
	border-radius: 44rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	color: #fff;
	font-size: 32rpx;
	font-weight: 600;
}

.analyze-btn.disabled {
	opacity: 0.6;
}

.loading-overlay {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background: rgba(0,0,0,0.5);
	display: flex;
	align-items: center;
	justify-content: center;
	z-index: 9999;
}

.loading-content {
	background: #fff;
	border-radius: 20rpx;
	padding: 60rpx;
	display: flex;
	flex-direction: column;
	align-items: center;
}

.loading-spinner {
	width: 60rpx;
	height: 60rpx;
	border: 4rpx solid #f3f3f3;
	border-top: 4rpx solid #007aff;
	border-radius: 50%;
	animation: spin 1s linear infinite;
	margin-bottom: 30rpx;
}

@keyframes spin {
	0% { transform: rotate(0deg); }
	100% { transform: rotate(360deg); }
}

.loading-text {
	font-size: 28rpx;
	color: #333;
}
</style>