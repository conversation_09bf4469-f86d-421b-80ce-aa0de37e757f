var T=Object.defineProperty;var L=Object.getOwnPropertySymbols;var $=Object.prototype.hasOwnProperty,z=Object.prototype.propertyIsEnumerable;var S=(o,a,e)=>a in o?T(o,a,{enumerable:!0,configurable:!0,writable:!0,value:e}):o[a]=e,x=(o,a)=>{for(var e in a||(a={}))$.call(a,e)&&S(o,e,a[e]);if(L)for(var e of L(a))z.call(a,e)&&S(o,e,a[e]);return o};var w=(o,a,e)=>new Promise((i,r)=>{var u=l=>{try{f(e.next(l))}catch(t){r(t)}},p=l=>{try{f(e.throw(l))}catch(t){r(t)}},f=l=>l.done?i(l.value):Promise.resolve(l.value).then(u,p);f((e=e.apply(o,a)).next())});import{M as V}from"./index-C-lrPiV1.js";import{b as W,ae as O,$ as b,af as U,ag as q,ah as H,ai as Z,M as D,V as A}from"./bootstrap-B_sue86n.js";import{_ as G}from"./login.vue_vue_type_script_setup_true_lang-Bo2fjmX3.js";import{g as M,Q as j,e as J,z as Q,_ as F,d as E,R as K,S as X,w as P,c as k,k as Y,l as R,C as m,B as g,s as n,v as ee,T as B,J as C,A as ae}from"../jse/index-index-UaL0SrHU.js";import{f as te,N as oe,i as se,_ as ne}from"./layout.vue_vue_type_script_setup_true_lang-juJTdT6m.js";import{u as re}from"./use-modal-DH4BF1xL.js";import{_ as le}from"./avatar.vue_vue_type_script_setup_true_lang-BjMx0Zs0.js";import"./auth-title-Z_muH2-J.js";import"./theme-toggle.vue_vue_type_script_setup_true_lang-0F8smxyW.js";import"./use-preferences-D9nCK1i-.js";import"./TabsList.vue_vue_type_script_setup_true_lang-DMrMu-Xs.js";import"./rotate-cw-weNjtwdP.js";const ie=W("book-open-text",[["path",{d:"M12 7v14",key:"1akyts"}],["path",{d:"M16 12h2",key:"7q9ll5"}],["path",{d:"M16 8h2",key:"msurwy"}],["path",{d:"M3 18a1 1 0 0 1-1-1V4a1 1 0 0 1 1-1h5a4 4 0 0 1 4 4 4 4 0 0 1 4-4h5a1 1 0 0 1 1 1v13a1 1 0 0 1-1 1h-6a3 3 0 0 0-3 3 3 3 0 0 0-3-3z",key:"ruj8y"}],["path",{d:"M6 12h2",key:"32wvfc"}],["path",{d:"M6 8h2",key:"30oboj"}]]),d=M(),N=M(!1),_=M({advancedStyle:{colorStops:[{color:"gray",offset:0},{color:"gray",offset:1}],type:"linear"},content:"",contentType:"multi-line-text",globalAlpha:.25,gridLayoutOptions:{cols:2,gap:[20,20],matrix:[[1,0],[0,1]],rows:2},height:200,layout:"grid",rotate:30,width:160});function ue(){function o(i){return w(this,null,function*(){var u;const{Watermark:r}=yield F(()=>w(null,null,function*(){const{Watermark:p}=yield import("./index.esm-Ofvdfudw.js");return{Watermark:p}}),[]);_.value=x(x({},_.value),i),d.value=new r(_.value),yield(u=d.value)==null?void 0:u.create()})}function a(i){return w(this,null,function*(){var r;d.value?(yield Q(),yield(r=d.value)==null?void 0:r.changeOptions(x(x({},_.value),i))):yield o(i)})}function e(){d.value&&(d.value.destroy(),d.value=void 0)}return N.value||(N.value=!0,j(()=>{e()})),{destroyWatermark:e,updateWatermark:a,watermark:J(d)}}const ce=E({name:"LoginExpiredModal",__name:"login-expired-modal",props:K({avatar:{default:""},zIndex:{default:0},codeLoginPath:{},forgetPasswordPath:{},loading:{type:Boolean},qrCodeLoginPath:{},registerPath:{},showCodeLogin:{type:Boolean},showForgetPassword:{type:Boolean},showQrcodeLogin:{type:Boolean},showRegister:{type:Boolean},showRememberMe:{type:Boolean},showThirdPartyLogin:{type:Boolean},subTitle:{},title:{},submitButtonText:{}},{open:{type:Boolean},openModifiers:{}}),emits:["update:open"],setup(o){const a=o,e=X(o,"open"),[i,r]=re();P(()=>e.value,t=>{r.setState({isOpen:t})});const u=k(()=>a.zIndex||l()),p=["ant-message","loading"];function f(t){return p.some(h=>t.classList.contains(h))}function l(){let t=0;return[...document.querySelectorAll("*")].forEach(y=>{const s=window.getComputedStyle(y).getPropertyValue("z-index");s&&!Number.isNaN(Number.parseInt(s))&&!f(y)&&(t=Math.max(t,Number.parseInt(s)))}),t+1}return(t,h)=>(R(),Y("div",null,[m(n(i),{closable:!1,"close-on-click-modal":!1,"close-on-press-escape":!1,footer:!1,"fullscreen-button":!1,header:!1,"z-index":u.value,class:"border-none px-10 py-6 text-center shadow-xl sm:w-[600px] sm:rounded-2xl md:h-[unset]"},{default:g(()=>[m(n(le),{src:t.avatar,class:"mx-auto mb-6 size-20"},null,8,["src"]),m(n(O),{"show-forget-password":!1,"show-register":!1,"show-remember-me":!1,"sub-title":n(b)("authentication.loginAgainSubTitle"),title:n(b)("authentication.loginAgainTitle")},{default:g(()=>[ee(t.$slots,"default")]),_:3},8,["sub-title","title"])]),_:3},8,["z-index"])]))}}),Me=E({__name:"basic",setup(o){const a=M([{avatar:"https://avatar.vercel.sh/vercel.svg?text=VB",date:"3小时前",isRead:!0,message:"描述信息描述信息描述信息",title:"收到了 14 份新周报"},{avatar:"https://avatar.vercel.sh/1",date:"刚刚",isRead:!1,message:"描述信息描述信息描述信息",title:"朱偏右 回复了你"},{avatar:"https://avatar.vercel.sh/1",date:"2024-01-01",isRead:!1,message:"描述信息描述信息描述信息",title:"曲丽丽 评论了你"},{avatar:"https://avatar.vercel.sh/satori",date:"1天前",isRead:!1,message:"描述信息描述信息描述信息",title:"代办提醒"}]),e=U(),i=q(),r=H(),{destroyWatermark:u,updateWatermark:p}=ue(),f=k(()=>a.value.some(s=>!s.isRead)),l=k(()=>[{handler:()=>{B(D,{target:"_blank"})},icon:ie,text:b("ui.widgets.document")},{handler:()=>{B(A,{target:"_blank"})},icon:V,text:"GitHub"},{handler:()=>{B(`${A}/issues`,{target:"_blank"})},icon:Z,text:b("ui.widgets.qa")}]),t=k(()=>{var s,c;return(c=(s=e.userInfo)==null?void 0:s.avatar)!=null?c:C.app.defaultAvatar});function h(){return w(this,null,function*(){yield i.logout(!1)})}function y(){a.value=[]}function I(){a.value.forEach(s=>s.isRead=!0)}return P(()=>C.app.watermark,s=>w(null,null,function*(){var c,v;s?yield p({content:`${(c=e.userInfo)==null?void 0:c.username} - ${(v=e.userInfo)==null?void 0:v.realName}`}):u()}),{immediate:!0}),(s,c)=>(R(),ae(n(ne),{onClearPreferencesAndLogout:h},{"user-dropdown":g(()=>{var v;return[m(n(se),{avatar:t.value,menus:l.value,text:(v=n(e).userInfo)==null?void 0:v.realName,description:"<EMAIL>","tag-text":"Pro",onLogout:h},null,8,["avatar","menus","text"])]}),notification:g(()=>[m(n(oe),{dot:f.value,notifications:a.value,onClear:y,onMakeAll:I},null,8,["dot","notifications"])]),extra:g(()=>[m(n(ce),{open:n(r).loginExpired,"onUpdate:open":c[0]||(c[0]=v=>n(r).loginExpired=v),avatar:t.value},{default:g(()=>[m(G)]),_:1},8,["open","avatar"])]),"lock-screen":g(()=>[m(n(te),{avatar:t.value,onToLogin:h},null,8,["avatar"])]),_:1}))}});export{Me as default};
