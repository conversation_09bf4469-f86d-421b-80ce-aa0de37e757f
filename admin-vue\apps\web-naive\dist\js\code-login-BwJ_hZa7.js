var T=(m,u,n)=>new Promise((l,t)=>{var c=a=>{try{s(n.next(a))}catch(r){t(r)}},b=a=>{try{s(n.throw(a))}catch(r){t(r)}},s=a=>a.done?l(a.value):Promise.resolve(a.value).then(c,b);s((n=n.apply(m,u)).next())});import{E as w,P as x,$ as e,F as v,Q as B}from"./bootstrap-B_sue86n.js";import{T as L}from"./auth-title-Z_muH2-J.js";import{d as k,r as N,c as $,k as V,l as S,C as f,B as p,v as _,I as h,x as g,s as i,n as P,q as y,g as A,A as E}from"../jse/index-index-UaL0SrHU.js";const F={class:"text-muted-foreground"},D=k({name:"AuthenticationCodeLogin",__name:"code-login",props:{formSchema:{},loading:{type:Boolean,default:!1},loginPath:{default:"/auth/login"},title:{default:""},subTitle:{default:""},submitButtonText:{default:""}},emits:["submit"],setup(m,{expose:u,emit:n}){const l=m,t=n,c=w(),[b,s]=x(N({commonConfig:{hideLabel:!0,hideRequiredMark:!0},schema:$(()=>l.formSchema),showDefaultActions:!1}));function a(){return T(this,null,function*(){const{valid:o}=yield s.validate(),d=yield s.getValues();o&&t("submit",d)})}function r(){c.push(l.loginPath)}return u({getFormApi:()=>s}),(o,d)=>(S(),V("div",null,[f(L,null,{desc:p(()=>[P("span",F,[_(o.$slots,"subTitle",{},()=>[h(g(o.subTitle||i(e)("authentication.codeSubtitle")),1)])])]),default:p(()=>[_(o.$slots,"title",{},()=>[h(g(o.title||i(e)("authentication.welcomeBack"))+" 📲 ",1)])]),_:3}),f(i(b)),f(i(v),{class:y([{"cursor-wait":o.loading},"w-full"]),loading:o.loading,onClick:a},{default:p(()=>[_(o.$slots,"submitButtonText",{},()=>[h(g(o.submitButtonText||i(e)("common.login")),1)])]),_:3},8,["class","loading"]),f(i(v),{class:"mt-4 w-full",variant:"outline",onClick:d[0]||(d[0]=I=>r())},{default:p(()=>[h(g(i(e)("common.back")),1)]),_:1})]))}}),C=6,H=k({name:"CodeLogin",__name:"code-login",setup(m){const u=A(!1),n=$(()=>[{component:"VbenInput",componentProps:{placeholder:e("authentication.mobile")},fieldName:"phoneNumber",label:e("authentication.mobile"),rules:B().min(1,{message:e("authentication.mobileTip")}).refine(t=>/^\d{11}$/.test(t),{message:e("authentication.mobileErrortip")})},{component:"VbenPinInput",componentProps:{codeLength:C,createText:t=>t>0?e("authentication.sendText",[t]):e("authentication.sendCode"),placeholder:e("authentication.code")},fieldName:"code",label:e("authentication.code"),rules:B().length(C,{message:e("authentication.codeTip",[C])})}]);function l(t){return T(this,null,function*(){console.log(t)})}return(t,c)=>(S(),E(i(D),{"form-schema":n.value,loading:u.value,onSubmit:l},null,8,["form-schema","loading"]))}});export{H as default};
