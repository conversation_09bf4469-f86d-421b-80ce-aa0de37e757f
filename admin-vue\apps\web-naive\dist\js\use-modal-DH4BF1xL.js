var Je=Object.defineProperty,Qe=Object.defineProperties;var Ze=Object.getOwnPropertyDescriptors;var ae=Object.getOwnPropertySymbols;var Ce=Object.prototype.hasOwnProperty,Be=Object.prototype.propertyIsEnumerable;var de=(s,t,o)=>t in s?Je(s,t,{enumerable:!0,configurable:!0,writable:!0,value:o}):s[t]=o,h=(s,t)=>{for(var o in t||(t={}))Ce.call(t,o)&&de(s,o,t[o]);if(ae)for(var o of ae(t))Be.call(t,o)&&de(s,o,t[o]);return s},V=(s,t)=>Qe(s,Ze(t));var K=(s,t)=>{var o={};for(var a in s)Ce.call(s,a)&&t.indexOf(a)<0&&(o[a]=s[a]);if(s!=null&&ae)for(var a of ae(s))t.indexOf(a)<0&&Be.call(s,a)&&(o[a]=s[a]);return o};var U=(s,t,o)=>de(s,typeof t!="symbol"?t+"":t,o);var W=(s,t,o)=>new Promise((a,l)=>{var d=m=>{try{B(o.next(m))}catch(i){l(i)}},p=m=>{try{B(o.throw(m))}catch(i){l(i)}},B=m=>m.done?a(m.value):Promise.resolve(m.value).then(d,p);B((o=o.apply(s,t)).next())});import{d as $,A as y,l as f,s as e,M as Ge,N as et,B as v,v as C,b as De,k as ue,c as L,g as D,C as X,m as w,y as tt,q as F,t as A,O as ce,r as Ae,j as ot,V as st,f as at,ac as ie,ad as nt,w as lt,X as rt,n as dt,I as J,x as Q,H as ke,z as pe,P as $e,ae as it,af as ut,h as we}from"../jse/index-index-UaL0SrHU.js";import{b as fe,S as Te,aO as ct,aP as pt,aQ as ft,aN as mt,aR as ht,aS as yt,ap as Ee,aT as vt,aU as gt,aV as bt,aW as Ct,aX as Bt,aY as kt,aZ as wt,a_ as _t,a$ as Ot,b0 as Mt,aK as xt,F as _e,b1 as Dt,b2 as At}from"./bootstrap-B_sue86n.js";const $t=fe("expand",[["path",{d:"m15 15 6 6",key:"1s409w"}],["path",{d:"m15 9 6-6",key:"ko1vev"}],["path",{d:"M21 16v5h-5",key:"1ck2sf"}],["path",{d:"M21 8V3h-5",key:"1qoq8a"}],["path",{d:"M3 16v5h5",key:"1t08am"}],["path",{d:"m3 21 6-6",key:"wwnumi"}],["path",{d:"M3 8V3h5",key:"1ln10m"}],["path",{d:"M9 9 3 3",key:"v551iv"}]]);const Tt=fe("shrink",[["path",{d:"m15 15 6 6m-6-6v4.8m0-4.8h4.8",key:"17vawe"}],["path",{d:"M9 19.8V15m0 0H4.2M9 15l-6 6",key:"chjx8e"}],["path",{d:"M15 4.2V9m0 0h4.8M15 9l6-6",key:"lav6yq"}],["path",{d:"M9 4.2V9m0 0H4.2M9 9 3 3",key:"1pxi2q"}]]);const Et=fe("x",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]]),St=$({__name:"Dialog",props:{open:{type:Boolean},defaultOpen:{type:Boolean},modal:{type:Boolean}},emits:["update:open"],setup(s,{emit:t}){const l=Te(s,t);return(d,p)=>(f(),y(e(ct),Ge(et(e(l))),{default:v(()=>[C(d.$slots,"default")]),_:3},16))}}),It=["data-dismissable-modal"],Pt=$({__name:"DialogOverlay",setup(s){pt();const t=De("DISMISSABLE_MODAL_ID");return(o,a)=>(f(),ue("div",{"data-dismissable-modal":e(t),class:"bg-overlay z-popup inset-0"},null,8,It))}}),Lt=$({__name:"DialogContent",props:{forceMount:{type:Boolean},trapFocus:{type:Boolean},disableOutsidePointerEvents:{type:Boolean},asChild:{type:Boolean},as:{},appendTo:{default:"body"},class:{},closeClass:{},closeDisabled:{type:Boolean,default:!1},modal:{type:Boolean},open:{type:Boolean},overlayBlur:{},showClose:{type:Boolean,default:!0},zIndex:{}},emits:["escapeKeyDown","pointerDownOutside","focusOutside","interactOutside","openAutoFocus","closeAutoFocus","close","closed","opened"],setup(s,{expose:t,emit:o}){const a=s,l=o,d=L(()=>{const O=a,{class:r,modal:u,open:g,showClose:k}=O;return K(O,["class","modal","open","showClose"])});function p(){return a.appendTo==="body"||a.appendTo===document.body||!a.appendTo}const B=L(()=>p()?"fixed":"absolute"),m=Te(d,l),i=D(null);function c(r){var u;r.target===((u=i.value)==null?void 0:u.$el)&&(a.open?l("opened"):l("closed"))}return t({getContentRef:()=>i.value}),(r,u)=>(f(),y(e(ft),{to:r.appendTo},{default:v(()=>[X(mt,{name:"fade"},{default:v(()=>[r.open&&r.modal?(f(),y(Pt,{key:0,style:tt(V(h({},r.zIndex?{zIndex:r.zIndex}:{}),{position:B.value,backdropFilter:r.overlayBlur&&r.overlayBlur>0?`blur(${r.overlayBlur}px)`:"none"})),onClick:u[0]||(u[0]=()=>l("close"))},null,8,["style"])):w("",!0)]),_:1}),X(e(yt),ce({ref_key:"contentRef",ref:i,style:V(h({},r.zIndex?{zIndex:r.zIndex}:{}),{position:B.value}),onAnimationend:c},e(m),{class:e(A)("z-popup bg-background data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-top-[48%] w-full p-6 shadow-lg outline-none sm:rounded-xl",a.class)}),{default:v(()=>[C(r.$slots,"default"),r.showClose?(f(),y(e(ht),{key:0,disabled:r.closeDisabled,class:F(e(A)("data-[state=open]:bg-accent data-[state=open]:text-muted-foreground hover:bg-accent hover:text-accent-foreground text-foreground/80 flex-center absolute right-3 top-3 h-6 w-6 rounded-full px-1 text-lg opacity-70 transition-opacity hover:opacity-100 focus:outline-none disabled:pointer-events-none",a.closeClass)),onClick:u[1]||(u[1]=()=>l("close"))},{default:v(()=>[X(e(Et),{class:"h-4 w-4"})]),_:1},8,["disabled","class"])):w("",!0)]),_:3},16,["style","class"])]),_:3},8,["to"]))}}),Oe=$({__name:"DialogDescription",props:{asChild:{type:Boolean},as:{},class:{}},setup(s){const t=s,o=L(()=>{const p=t,{class:l}=p;return K(p,["class"])}),a=Ee(o);return(l,d)=>(f(),y(e(vt),ce(e(a),{class:e(A)("text-muted-foreground text-sm",t.class)}),{default:v(()=>[C(l.$slots,"default")]),_:3},16,["class"]))}}),zt=$({__name:"DialogFooter",props:{class:{}},setup(s){const t=s;return(o,a)=>(f(),ue("div",{class:F(e(A)("flex flex-row flex-col-reverse justify-end gap-x-2",t.class))},[C(o.$slots,"default")],2))}}),Vt=$({__name:"DialogHeader",props:{class:{}},setup(s){const t=s;return(o,a)=>(f(),ue("div",{class:F(e(A)("flex flex-col gap-y-1.5 text-center sm:text-left",t.class))},[C(o.$slots,"default")],2))}}),Me=$({__name:"DialogTitle",props:{asChild:{type:Boolean},as:{},class:{}},setup(s){const t=s,o=L(()=>{const p=t,{class:l}=p;return K(p,["class"])}),a=Ee(o);return(l,d)=>(f(),y(e(gt),ce(e(a),{class:e(A)("text-lg font-semibold leading-none tracking-tight",t.class)}),{default:v(()=>[C(l.$slots,"default")]),_:3},16,["class"]))}});function Ft(s,t,o,a){const l=Ae({offsetX:0,offsetY:0}),d=D(!1),p=c=>{const r=c.clientX,u=c.clientY;if(!s.value)return;const g=s.value.getBoundingClientRect(),{offsetX:k,offsetY:_}=l,O=g.left,R=g.top,Y=g.width,Z=g.height;let E=null;if(a!=null&&a.value){const M=document.querySelector(a.value);M&&(E=M.getBoundingClientRect())}let H,j,q,S;if(E)q=E.left-O+k,H=E.right-O-Y+k,S=E.top-R+_,j=E.bottom-R-Z+_;else{const M=document.documentElement,I=M.clientWidth,x=M.clientHeight;q=-O+k,S=-R+_,H=I-O-Y+k,j=x-R-Z+_}const G=M=>{let I=k+M.clientX-r,x=_+M.clientY-u;I=Math.min(Math.max(I,q),H),x=Math.min(Math.max(x,S),j),l.offsetX=I,l.offsetY=x,s.value&&(s.value.style.transform=`translate(${I}px, ${x}px)`,d.value=!0)},ee=()=>{d.value=!1,document.removeEventListener("mousemove",G),document.removeEventListener("mouseup",ee)};document.addEventListener("mousemove",G),document.addEventListener("mouseup",ee)},B=()=>{const c=ie(t);c&&s.value&&c.addEventListener("mousedown",p)},m=()=>{const c=ie(t);c&&s.value&&c.removeEventListener("mousedown",p)},i=()=>{l.offsetX=0,l.offsetY=0;const c=ie(s);c&&(c.style.transform="none")};return ot(()=>{st(()=>{o.value?B():m()})}),at(()=>{m()}),{dragging:d,resetPosition:i,transform:l}}const Rt=$({__name:"modal",props:{modalApi:{default:void 0},appendToMain:{type:Boolean,default:!1},bordered:{type:Boolean},cancelText:{},centered:{type:Boolean},class:{},closable:{type:Boolean},closeOnClickModal:{type:Boolean},closeOnPressEscape:{type:Boolean},confirmDisabled:{type:Boolean},confirmLoading:{type:Boolean},confirmText:{},contentClass:{},description:{},destroyOnClose:{type:Boolean,default:!1},draggable:{type:Boolean},footer:{type:Boolean},footerClass:{},fullscreen:{type:Boolean},fullscreenButton:{type:Boolean},header:{type:Boolean},headerClass:{},loading:{type:Boolean},modal:{type:Boolean},openAutoFocus:{type:Boolean},overlayBlur:{},showCancelButton:{type:Boolean},showConfirmButton:{type:Boolean},submitting:{type:Boolean},title:{},titleTooltip:{},zIndex:{}},setup(s){var se,be;const t=s,o=bt.getComponents(),a=D(),l=D(),d=D(),p=D(),B=D(),m=nt();$e("DISMISSABLE_MODAL_ID",m);const{$t:i}=Ct(),{isMobile:c}=Bt(),r=(be=(se=t.modalApi)==null?void 0:se.useStore)==null?void 0:be.call(se),{appendToMain:u,bordered:g,cancelText:k,centered:_,class:O,closable:R,closeOnClickModal:Y,closeOnPressEscape:Z,confirmDisabled:E,confirmLoading:H,confirmText:j,contentClass:q,description:S,destroyOnClose:G,draggable:ee,footer:M,footerClass:I,fullscreen:x,fullscreenButton:Se,header:ne,headerClass:Ie,loading:me,modal:Pe,openAutoFocus:Le,overlayBlur:ze,showCancelButton:Ve,showConfirmButton:Fe,submitting:T,title:te,titleTooltip:he,zIndex:Re}=kt(t,r),oe=L(()=>x.value&&ne.value||c.value),ye=L(()=>ee.value&&!oe.value&&ne.value),ve=L(()=>u.value?`#${wt}>div:not(.absolute)>div`:void 0),{dragging:Ne,transform:Xe}=Ft(d,p,ye,ve),le=D(!1),re=D(!0);lt(()=>{var n;return(n=r==null?void 0:r.value)==null?void 0:n.isOpen},n=>W(null,null,function*(){if(n){if(re.value=!1,le.value||(le.value=!0),yield pe(),!a.value)return;const b=a.value.getContentRef();d.value=b.$el;const{offsetX:N,offsetY:z}=Xe;d.value.style.transform=`translate(${N}px, ${z}px)`}}),{immediate:!0}),rt(()=>{var n;u.value||(n=t.modalApi)==null||n.close()});function Ye(){var n;(n=t.modalApi)==null||n.setState(b=>V(h({},b),{fullscreen:!x.value}))}function He(n){(!Y.value||T.value)&&(n.preventDefault(),n.stopPropagation())}function je(n){(!Z.value||T.value)&&n.preventDefault()}function qe(n){Le.value||n==null||n.preventDefault()}function Ke(n){const b=n.target,N=b==null?void 0:b.dataset.dismissableModal;(!Y.value||N!==m||T.value)&&(n.preventDefault(),n.stopPropagation())}function ge(n){n.preventDefault(),n.stopPropagation()}const Ue=L(()=>!e(G)&&e(le));function We(){var n;re.value=!0,(n=t.modalApi)==null||n.onClosed()}return(n,b)=>{var N;return f(),y(e(St),{modal:!1,open:(N=e(r))==null?void 0:N.isOpen,"onUpdate:open":b[3]||(b[3]=()=>{var z;return e(T)||(z=n.modalApi)==null?void 0:z.close()})},{default:v(()=>{var z;return[X(e(Lt),{ref_key:"contentRef",ref:a,"append-to":ve.value,class:F(e(A)("left-0 right-0 top-[10vh] mx-auto flex max-h-[80%] w-[520px] flex-col p-0",oe.value?"sm:rounded-none":"sm:rounded-[var(--radius)]",e(O),{"border-border border":e(g),"shadow-3xl":!e(g),"left-0 top-0 size-full max-h-full !translate-x-0 !translate-y-0":oe.value,"top-1/2 !-translate-y-1/2":e(_)&&!oe.value,"duration-300":!e(Ne),hidden:re.value})),"force-mount":Ue.value,modal:e(Pe),open:(z=e(r))==null?void 0:z.isOpen,"show-close":e(R),"z-index":e(Re),"overlay-blur":e(ze),"close-class":"top-3",onCloseAutoFocus:ge,onClosed:We,"close-disabled":e(T),onEscapeKeyDown:je,onFocusOutside:ge,onInteractOutside:He,onOpenAutoFocus:qe,onOpened:b[2]||(b[2]=()=>{var P;return(P=n.modalApi)==null?void 0:P.onOpened()}),onPointerDownOutside:Ke},{default:v(()=>[X(e(Vt),{ref_key:"headerRef",ref:p,class:F(e(A)("px-5 py-4",{"border-b":e(g),hidden:!e(ne),"cursor-move select-none":ye.value},e(Ie)))},{default:v(()=>[e(te)?(f(),y(e(Me),{key:0,class:"text-left"},{default:v(()=>[C(n.$slots,"title",{},()=>[J(Q(e(te))+" ",1),e(he)?C(n.$slots,"titleTooltip",{key:0},()=>[X(e(_t),{"trigger-class":"pb-1"},{default:v(()=>[J(Q(e(he)),1)]),_:1})]):w("",!0)])]),_:3})):w("",!0),e(S)?(f(),y(e(Oe),{key:1},{default:v(()=>[C(n.$slots,"description",{},()=>[J(Q(e(S)),1)])]),_:3})):w("",!0),!e(te)||!e(S)?(f(),y(e(Ot),{key:2},{default:v(()=>[e(te)?w("",!0):(f(),y(e(Me),{key:0})),e(S)?w("",!0):(f(),y(e(Oe),{key:1}))]),_:1})):w("",!0)]),_:3},8,["class"]),dt("div",{ref_key:"wrapperRef",ref:l,class:F(e(A)("relative min-h-40 flex-1 overflow-y-auto p-3",e(q),{"pointer-events-none":e(me)||e(T)}))},[C(n.$slots,"default")],2),e(me)||e(T)?(f(),y(e(Mt),{key:0,spinning:""})):w("",!0),e(Se)?(f(),y(e(xt),{key:1,class:"hover:bg-accent hover:text-accent-foreground text-foreground/80 flex-center absolute right-10 top-3 hidden size-6 rounded-full px-1 text-lg opacity-70 transition-opacity hover:opacity-100 focus:outline-none disabled:pointer-events-none sm:block",onClick:Ye},{default:v(()=>[e(x)?(f(),y(e(Tt),{key:0,class:"size-3.5"})):(f(),y(e($t),{key:1,class:"size-3.5"}))]),_:1})):w("",!0),e(M)?(f(),y(e(zt),{key:2,ref_key:"footerRef",ref:B,class:F(e(A)("flex-row items-center justify-end p-2",{"border-t":e(g)},e(I)))},{default:v(()=>[C(n.$slots,"prepend-footer"),C(n.$slots,"footer",{},()=>[e(Ve)?(f(),y(ke(e(o).DefaultButton||e(_e)),{key:0,variant:"ghost",disabled:e(T),onClick:b[0]||(b[0]=()=>{var P;return(P=n.modalApi)==null?void 0:P.onCancel()})},{default:v(()=>[C(n.$slots,"cancelText",{},()=>[J(Q(e(k)||e(i)("cancel")),1)])]),_:3},8,["disabled"])):w("",!0),C(n.$slots,"center-footer"),e(Fe)?(f(),y(ke(e(o).PrimaryButton||e(_e)),{key:1,disabled:e(E),loading:e(H)||e(T),onClick:b[1]||(b[1]=()=>{var P;return(P=n.modalApi)==null?void 0:P.onConfirm()})},{default:v(()=>[C(n.$slots,"confirmText",{},()=>[J(Q(e(j)||e(i)("confirm")),1)])]),_:3},8,["disabled","loading"])):w("",!0)]),C(n.$slots,"append-footer")]),_:3},8,["class"])):w("",!0)]),_:3},8,["append-to","class","force-mount","modal","open","show-close","z-index","overlay-blur","close-disabled"])]}),_:3},8,["open"])}}});class Nt{constructor(t={}){U(this,"sharedData",{payload:{}});U(this,"store");U(this,"api");U(this,"state");const r=t,{connectedComponent:o,onBeforeClose:a,onCancel:l,onClosed:d,onConfirm:p,onOpenChange:B,onOpened:m}=r,i=K(r,["connectedComponent","onBeforeClose","onCancel","onClosed","onConfirm","onOpenChange","onOpened"]),c={bordered:!0,centered:!1,class:"",closeOnClickModal:!0,closeOnPressEscape:!0,confirmDisabled:!1,confirmLoading:!1,contentClass:"",destroyOnClose:!0,draggable:!1,footer:!0,footerClass:"",fullscreen:!1,fullscreenButton:!0,header:!0,headerClass:"",isOpen:!1,loading:!1,modal:!0,openAutoFocus:!1,showCancelButton:!0,showConfirmButton:!0,title:""};this.store=new Dt(h(h({},c),i),{onUpdate:()=>{var g,k,_;const u=this.store.state;(u==null?void 0:u.isOpen)===((g=this.state)==null?void 0:g.isOpen)?this.state=u:(this.state=u,(_=(k=this.api).onOpenChange)==null||_.call(k,!!(u!=null&&u.isOpen)))}}),this.state=this.store.state,this.api={onBeforeClose:a,onCancel:l,onClosed:d,onConfirm:p,onOpenChange:B,onOpened:m},it(this)}close(){return W(this,null,function*(){var o,a,l;((l=yield(a=(o=this.api).onBeforeClose)==null?void 0:a.call(o))!=null?l:!0)&&this.store.setState(d=>V(h({},d),{isOpen:!1,submitting:!1}))})}getData(){var t,o;return(o=(t=this.sharedData)==null?void 0:t.payload)!=null?o:{}}lock(t=!0){return this.setState({submitting:t})}onCancel(){var t,o;this.api.onCancel?(o=(t=this.api).onCancel)==null||o.call(t):this.close()}onClosed(){var t,o;this.state.isOpen||(o=(t=this.api).onClosed)==null||o.call(t)}onConfirm(){var t,o;(o=(t=this.api).onConfirm)==null||o.call(t)}onOpened(){var t,o;this.state.isOpen&&((o=(t=this.api).onOpened)==null||o.call(t))}open(){this.store.setState(t=>V(h({},t),{isOpen:!0}))}setData(t){return this.sharedData.payload=t,this}setState(t){return ut(t)?this.store.setState(t):this.store.setState(o=>h(h({},o),t)),this}unlock(){return this.lock(!1)}}const xe=Symbol("VBEN_MODAL_INJECT"),Xt={};function Kt(s={}){var m;const{connectedComponent:t}=s;if(t){const i=Ae({}),c=D(!0);return[$((u,{attrs:g,slots:k})=>($e(xe,{extendApi(O){Object.setPrototypeOf(i,O)},options:s,reCreateModal(){return W(this,null,function*(){c.value=!1,yield pe(),c.value=!0})}}),Yt(i,h(h(h({},u),g),k)),()=>we(c.value?t:"div",h(h({},u),g),k)),{name:"VbenParentModal",inheritAttrs:!1}),i]}const o=De(xe,{}),a=h(h(h({},Xt),o.options),s);a.onOpenChange=i=>{var c,r,u;(c=s.onOpenChange)==null||c.call(s,i),(u=(r=o.options)==null?void 0:r.onOpenChange)==null||u.call(r,i)};const l=a.onClosed;a.onClosed=()=>{var i;l==null||l(),a.destroyOnClose&&((i=o.reCreateModal)==null||i.call(o))};const d=new Nt(a),p=d;p.useStore=i=>At(d.store,i);const B=$((i,{attrs:c,slots:r})=>()=>we(Rt,V(h(h({},i),c),{modalApi:p}),r),{name:"VbenModal",inheritAttrs:!1});return(m=o.extendApi)==null||m.call(o,p),[B,p]}function Yt(s,t){return W(this,null,function*(){var l;if(!t||Object.keys(t).length===0)return;yield pe();const o=(l=s==null?void 0:s.store)==null?void 0:l.state;if(!o)return;const a=new Set(Object.keys(o));for(const d of Object.keys(t))a.has(d)&&!["class"].includes(d)&&console.warn(`[Vben Modal]: When 'connectedComponent' exists, do not set props or slots '${d}', which will increase complexity. If you need to modify the props of Modal, please use useVbenModal or api.`)})}export{Et as X,Kt as u};
