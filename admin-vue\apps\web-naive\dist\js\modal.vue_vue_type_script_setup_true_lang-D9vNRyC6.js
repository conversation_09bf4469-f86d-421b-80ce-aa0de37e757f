var p=(c,t,e)=>new Promise((r,l)=>{var n=o=>{try{s(e.next(o))}catch(m){l(m)}},a=o=>{try{s(e.throw(o))}catch(m){l(m)}},s=o=>o.done?r(o.value):Promise.resolve(o.value).then(n,a);s((e=e.apply(c,t)).next())});import{u}from"./bootstrap-B_sue86n.js";import{d,A as f,l as _,B as b,C as h,s as i}from"../jse/index-index-UaL0SrHU.js";import{u as C}from"./use-modal-DH4BF1xL.js";const N=d({name:"FormModelDemo",__name:"modal",setup(c){const[t,e]=u({schema:[{component:"Input",componentProps:{placeholder:"请输入"},fieldName:"field1",label:"字段1",rules:"required"},{component:"Input",componentProps:{placeholder:"请输入"},fieldName:"field2",label:"字段2",rules:"required"},{component:"Select",componentProps:{options:[{label:"选项1",value:"1"},{label:"选项2",value:"2"}],placeholder:"请输入"},fieldName:"field3",label:"字段3",rules:"required"}],showDefaultActions:!1}),[r,l]=C({fullscreenButton:!1,onCancel(){l.close()},onConfirm:()=>p(null,null,function*(){yield e.validateAndSubmitForm()}),onOpenChange(n){if(n){const{values:a}=l.getData();a&&e.setValues(a)}},title:"内嵌表单示例"});return(n,a)=>(_(),f(i(r),null,{default:b(()=>[h(i(t))]),_:1}))}});export{N as _};
