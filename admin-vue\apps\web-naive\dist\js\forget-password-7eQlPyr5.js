var T=(c,l,a)=>new Promise((i,s)=>{var d=e=>{try{u(a.next(e))}catch(m){s(m)}},r=e=>{try{u(a.throw(e))}catch(m){s(m)}},u=e=>e.done?i(e.value):Promise.resolve(e.value).then(d,r);u((a=a.apply(c,l)).next())});import{P as S,E as P,$ as n,F as v,Q as $}from"./bootstrap-B_sue86n.js";import{T as C}from"./auth-title-Z_muH2-J.js";import{d as _,r as V,c as k,k as F,l as B,C as p,n as x,B as g,v as w,I as h,x as b,s as o,q as y,g as A,A as N}from"../jse/index-index-UaL0SrHU.js";const E=_({name:"ForgetPassword",__name:"forget-password",props:{formSchema:{},loading:{type:Boolean,default:!1},loginPath:{default:"/auth/login"},title:{default:""},subTitle:{default:""},submitButtonText:{default:""}},emits:["submit"],setup(c,{expose:l,emit:a}){const i=c,s=a,[d,r]=S(V({commonConfig:{hideLabel:!0,hideRequiredMark:!0},schema:k(()=>i.formSchema),showDefaultActions:!1})),u=P();function e(){return T(this,null,function*(){const{valid:t}=yield r.validate(),f=yield r.getValues();t&&s("submit",f)})}function m(){u.push(i.loginPath)}return l({getFormApi:()=>r}),(t,f)=>(B(),F("div",null,[p(C,null,{desc:g(()=>[w(t.$slots,"subTitle",{},()=>[h(b(t.subTitle||o(n)("authentication.forgetPasswordSubtitle")),1)])]),default:g(()=>[w(t.$slots,"title",{},()=>[h(b(t.title||o(n)("authentication.forgetPassword"))+" 🤦🏻‍♂️ ",1)])]),_:3}),p(o(d)),x("div",null,[p(o(v),{class:y([{"cursor-wait":t.loading},"mt-2 w-full"]),"aria-label":"submit",onClick:e},{default:g(()=>[w(t.$slots,"submitButtonText",{},()=>[h(b(t.submitButtonText||o(n)("authentication.sendResetLink")),1)])]),_:3},8,["class"]),p(o(v),{class:"mt-4 w-full",variant:"outline",onClick:f[0]||(f[0]=L=>m())},{default:g(()=>[h(b(o(n)("common.back")),1)]),_:1})])]))}}),z=_({name:"ForgetPassword",__name:"forget-password",setup(c){const l=A(!1),a=k(()=>[{component:"VbenInput",componentProps:{placeholder:"<EMAIL>"},fieldName:"email",label:n("authentication.email"),rules:$().min(1,{message:n("authentication.emailTip")}).email(n("authentication.emailValidErrorTip"))}]);function i(s){console.log("reset email:",s)}return(s,d)=>(B(),N(o(E),{"form-schema":a.value,loading:l.value,onSubmit:i},null,8,["form-schema","loading"]))}});export{z as default};
