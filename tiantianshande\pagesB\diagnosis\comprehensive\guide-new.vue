<template>
	<view class="comprehensive-guide-container">
		<!-- 顶部标题区域 -->
		<view class="header-section">
			<image class="header-image" :src="pre_url+'/static/img/comprehensive-diagnosis-icon.png'" mode="aspectFit"></image>
			<text class="header-title">AI综合诊疗</text>
			<text class="header-subtitle">舌诊+面诊+舌下脉络，全方位健康分析</text>
		</view>

		<!-- 功能介绍区域 -->
		<view class="intro-section">
			<view class="intro-card">
				<view class="card-header">
					<text class="card-icon">🔍</text>
					<text class="card-title">什么是综合诊疗？</text>
				</view>
				<text class="card-content">
					综合诊疗是结合舌诊、面诊、舌下脉络诊断的全面中医诊疗方法，通过多维度分析，提供更准确的健康评估和调理建议。
				</text>
			</view>

			<view class="intro-card">
				<view class="card-header">
					<text class="card-icon">🎯</text>
					<text class="card-title">AI综合诊疗功能</text>
				</view>
				<text class="card-content">
					运用先进的人工智能技术，结合传统中医理论，对舌部、面部、舌下脉络进行综合分析，为您提供全面的健康报告。
				</text>
			</view>
		</view>

		<!-- 诊疗项目区域 -->
		<view class="diagnosis-types">
			<text class="section-title">诊疗项目</text>
			<view class="types-grid">
				<view class="type-item" v-for="(type, index) in diagnosisTypes" :key="index">
					<view class="type-icon">{{ type.icon }}</view>
					<text class="type-name">{{ type.name }}</text>
					<text class="type-desc">{{ type.desc }}</text>
				</view>
			</view>
		</view>

		<!-- 使用指南区域 -->
		<view class="guide-section">
			<text class="section-title">拍摄指南</text>
			<view class="guide-steps">
				<view class="step-item" v-for="(step, index) in guideSteps" :key="index">
					<view class="step-number">{{ index + 1 }}</view>
					<view class="step-content">
						<text class="step-title">{{ step.title }}</text>
						<text class="step-desc">{{ step.desc }}</text>
					</view>
				</view>
			</view>
		</view>

		<!-- 注意事项区域 -->
		<view class="notice-section">
			<text class="section-title">注意事项</text>
			<view class="notice-list">
				<view class="notice-item" v-for="(notice, index) in noticeList" :key="index">
					<text class="notice-icon">⚠️</text>
					<text class="notice-text">{{ notice }}</text>
				</view>
			</view>
		</view>

		<!-- 底部操作区域 -->
		<view class="action-section">
			<button class="start-btn" :style="{background:'linear-gradient(90deg,'+t('color1')+' 0%,rgba('+t('color1rgb')+',0.8) 100%)'}" @click="startDiagnosis">
				<text class="btn-text">开始综合诊疗</text>
			</button>
			<text class="disclaimer">
				*本功能仅供健康参考，不能替代专业医生诊断
			</text>
		</view>
	</view>
</template>

<script>
export default {
	name: 'ComprehensiveGuide',
	data() {
		return {
			// 诊疗类型数据
			diagnosisTypes: [
				{
					icon: '👅',
					name: '舌诊',
					desc: '观察舌质舌苔，了解脏腑状况'
				},
				{
					icon: '😊',
					name: '面诊',
					desc: '分析面部特征，判断气血状态'
				},
				{
					icon: '🩸',
					name: '舌下脉络',
					desc: '检查血液循环，评估瘀血情况'
				}
			],
			// 拍摄指南步骤数据
			guideSteps: [
				{
					title: '准备环境',
					desc: '选择光线充足的环境，避免强光直射或过暗'
				},
				{
					title: '舌诊拍摄',
					desc: '伸出舌头，保持自然状态，舌体完整清晰'
				},
				{
					title: '面诊拍摄',
					desc: '正面拍摄，表情自然，避免化妆或遮挡'
				},
				{
					title: '舌下脉络',
					desc: '轻抬舌尖，露出舌下脉络，保持稳定'
				}
			],
			// 注意事项列表
			noticeList: [
				'请在自然状态下进行拍摄，避免浓妆',
				'确保口腔清洁，拍摄前漱口',
				'避免在强烈阳光直射下拍摄',
				'如有口腔疾病请先咨询医生',
				'孕妇、儿童使用前请咨询专业医生',
				'建议空腹或饭后2小时进行拍摄'
			]
		}
	},
	methods: {
		// 开始诊断
		startDiagnosis() {
			console.log('2025-07-17 INFO-[comprehensive-guide] 开始综合诊疗');
			uni.navigateTo({
				url: '/pagesB/diagnosis/comprehensive/index'
			});
		},
		
		// 返回上一页
		goBack() {
			uni.navigateBack();
		}
	}
}
</script>

<style>
.comprehensive-guide-container {
	min-height: 100vh;
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	padding: 40rpx 30rpx;
	color: #fff;
}

.header-section {
	text-align: center;
	margin-bottom: 60rpx;
	padding-top: 60rpx;
}

.header-image {
	width: 120rpx;
	height: 120rpx;
	margin-bottom: 30rpx;
}

.header-title {
	font-size: 48rpx;
	font-weight: 600;
	display: block;
	margin-bottom: 20rpx;
}

.header-subtitle {
	font-size: 28rpx;
	opacity: 0.8;
	line-height: 1.5;
}

.intro-section {
	margin-bottom: 60rpx;
}

.intro-card {
	background: rgba(255, 255, 255, 0.1);
	border-radius: 20rpx;
	padding: 40rpx;
	margin-bottom: 30rpx;
	backdrop-filter: blur(10rpx);
}

.card-header {
	display: flex;
	align-items: center;
	margin-bottom: 20rpx;
}

.card-icon {
	font-size: 36rpx;
	margin-right: 20rpx;
}

.card-title {
	font-size: 32rpx;
	font-weight: 600;
}

.card-content {
	font-size: 28rpx;
	line-height: 1.6;
	opacity: 0.9;
}

.diagnosis-types {
	margin-bottom: 60rpx;
}

.section-title {
	font-size: 36rpx;
	font-weight: 600;
	margin-bottom: 40rpx;
	display: block;
}

.types-grid {
	display: flex;
	gap: 20rpx;
}

.type-item {
	flex: 1;
	background: rgba(255, 255, 255, 0.1);
	border-radius: 20rpx;
	padding: 30rpx 20rpx;
	text-align: center;
	backdrop-filter: blur(10rpx);
}

.type-icon {
	font-size: 48rpx;
	display: block;
	margin-bottom: 20rpx;
}

.type-name {
	font-size: 28rpx;
	font-weight: 600;
	display: block;
	margin-bottom: 10rpx;
}

.type-desc {
	font-size: 22rpx;
	opacity: 0.8;
	line-height: 1.4;
}

.guide-section {
	margin-bottom: 60rpx;
}

.guide-steps {
	background: rgba(255, 255, 255, 0.1);
	border-radius: 20rpx;
	padding: 40rpx;
	backdrop-filter: blur(10rpx);
}

.step-item {
	display: flex;
	align-items: flex-start;
	margin-bottom: 40rpx;
}

.step-item:last-child {
	margin-bottom: 0;
}

.step-number {
	width: 60rpx;
	height: 60rpx;
	background: rgba(255, 255, 255, 0.2);
	border-radius: 50%;
	display: flex;
	align-items: center;
	justify-content: center;
	font-size: 28rpx;
	font-weight: 600;
	margin-right: 30rpx;
	flex-shrink: 0;
}

.step-content {
	flex: 1;
}

.step-title {
	font-size: 30rpx;
	font-weight: 600;
	display: block;
	margin-bottom: 10rpx;
}

.step-desc {
	font-size: 26rpx;
	opacity: 0.8;
	line-height: 1.5;
}

.notice-section {
	margin-bottom: 80rpx;
}

.notice-list {
	background: rgba(255, 255, 255, 0.1);
	border-radius: 20rpx;
	padding: 40rpx;
	backdrop-filter: blur(10rpx);
}

.notice-item {
	display: flex;
	align-items: flex-start;
	margin-bottom: 20rpx;
}

.notice-item:last-child {
	margin-bottom: 0;
}

.notice-icon {
	font-size: 28rpx;
	margin-right: 20rpx;
	flex-shrink: 0;
}

.notice-text {
	font-size: 26rpx;
	opacity: 0.9;
	line-height: 1.5;
	flex: 1;
}

.action-section {
	text-align: center;
}

.start-btn {
	width: 100%;
	height: 88rpx;
	border-radius: 44rpx;
	border: none;
	color: #fff;
	font-size: 32rpx;
	font-weight: 600;
	margin-bottom: 30rpx;
	box-shadow: 0 8rpx 30rpx rgba(0, 0, 0, 0.2);
}

.btn-text {
	color: #fff;
}

.disclaimer {
	font-size: 24rpx;
	opacity: 0.7;
	line-height: 1.5;
}
</style>
