import{u as t,_ as n}from"./use-echarts-e0qZouDC.js";import{d as o,g as s,j as r,A as i,l,s as c}from"../jse/index-index-UaL0SrHU.js";const _=o({__name:"analytics-visits-source",setup(m){const e=s(),{renderEcharts:a}=t(e);return r(()=>{a({legend:{bottom:"2%",left:"center"},series:[{animationDelay(){return Math.random()*100},animationEasing:"exponentialInOut",animationType:"scale",avoidLabelOverlap:!1,color:["#5ab1ef","#b6a2de","#67e0e3","#2ec7c9"],data:[{name:"搜索引擎",value:1048},{name:"直接访问",value:735},{name:"邮件营销",value:580},{name:"联盟广告",value:484}],emphasis:{label:{fontSize:"12",fontWeight:"bold",show:!0}},itemStyle:{borderRadius:10,borderWidth:2},label:{position:"center",show:!1},labelLine:{show:!1},name:"访问来源",radius:["40%","65%"],type:"pie"}],tooltip:{trigger:"item"}})}),(f,u)=>(l(),i(c(n),{ref_key:"chartRef",ref:e},null,512))}});export{_};
