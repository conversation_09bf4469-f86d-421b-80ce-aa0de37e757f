# 舌诊接口兼容性修复总结

## 问题描述

用户在使用舌诊分析功能时遇到错误：
```json
{
  "code": 0,
  "msg": "舌诊需要提供舌头图片"
}
```

## 问题分析

通过分析请求参数和后端代码发现：

### 前端请求参数
```javascript
{
  image_url: "https://kuaifengimg.azheteng.cn/upload/62/20250718/fed0d7bd030b57e056de3dba260e6e36.jpg",
  order_id: "47",
  use_free: 1
}
```

### 后端期望参数
```php
$tongueImageUrl = input('post.tongue_image_url'); // 舌头图片
$faceImageUrl = input('post.face_image_url'); // 面部图片
$sublingualImageUrl = input('post.sublingual_image_url'); // 舌下脉络图片
```

**根本原因**：前端传递的是旧版本参数 `image_url`，但后端已升级为支持多种诊疗类型，期望的是 `tongue_image_url` 参数。

## 修复方案

### 1. 兼容性处理

在 `shangchengquan/shangcheng/app/controller/ApiSheZhen.php` 的 `analyze` 方法中添加兼容性处理：

```php
// 2025-07-17 兼容性处理：支持旧版本的image_url参数
$legacyImageUrl = input('post.image_url'); // 兼容旧版本参数
if ($legacyImageUrl && !$tongueImageUrl) {
    $tongueImageUrl = $legacyImageUrl; // 将旧参数映射为舌头图片
    \think\facade\Log::info('2025-07-17 INFO-[ApiSheZhen][analyze_legacy] 使用兼容模式，将image_url映射为tongue_image_url: ' . $legacyImageUrl);
}
```

### 2. 操作日志增强

添加详细的操作日志记录：

```php
// 图片验证失败日志
\app\common\System::plog('舌诊分析失败：' . $validationResult['msg']);

// 功能未开启日志
\app\common\System::plog('舌诊分析失败：诊疗功能未开启');

// 分析成功日志
\app\common\System::plog('舌诊分析成功，订单号：' . $orderNo . '，记录ID：' . $recordId);

// 异常处理日志
\app\common\System::plog('舌诊分析异常：' . $e->getMessage());
```

## 修复效果

### 修复前
- 前端传递 `image_url` 参数
- 后端无法识别，导致验证失败
- 返回错误：`舌诊需要提供舌头图片`

### 修复后
- 自动检测并映射 `image_url` 到 `tongue_image_url`
- 保持向后兼容性
- 新旧版本接口都能正常工作
- 详细的操作日志便于问题排查

## 测试验证

创建了测试脚本 `shangchengquan/shangcheng/test_shezhen_fix.php` 来验证修复效果：

### 测试结果
- ✓ 兼容模式：将image_url映射为tongue_image_url
- ✓ 验证通过：舌诊图片已提供
- ✓ 保持了向后兼容性，不影响新版本接口

## 技术细节

### 支持的诊疗类型
1. **舌诊** (diagnosis_type=1)：需要 `tongue_image_url`
2. **面诊** (diagnosis_type=2)：需要 `face_image_url`
3. **综合诊疗** (diagnosis_type=3)：至少需要一张图片

### 图片数据映射
```php
$imageData = [];
if ($tongueImageUrl) $imageData['tf_image'] = $tongueImageUrl;
if ($faceImageUrl) $imageData['ff_image'] = $faceImageUrl;
if ($sublingualImageUrl) $imageData['tb_image'] = $sublingualImageUrl;
```

### 验证逻辑
使用 `\app\common\SheZhen::validateImagesByDiagnosisType()` 方法根据诊疗类型验证所需图片。

## 影响范围

- **前端**：无需修改，继续使用 `image_url` 参数
- **后端**：增加兼容性处理，支持新旧两种参数格式
- **数据库**：无影响
- **API接口**：保持向后兼容

## 后续建议

1. **前端升级**：建议前端逐步迁移到新的参数格式
2. **文档更新**：更新API文档说明新的参数格式
3. **监控日志**：关注兼容模式的使用情况
4. **版本规划**：在未来版本中可考虑移除兼容性代码

## 相关文件

- `shangchengquan/shangcheng/app/controller/ApiSheZhen.php` - 主要修复文件
- `shangchengquan/shangcheng/app/common/SheZhen.php` - 验证逻辑
- `shangchengquan/shangcheng/test_shezhen_fix.php` - 测试脚本
- `docs/舌诊接口调用规范.md` - 接口规范文档

## 修复时间

2025-07-17 完成修复和测试验证
