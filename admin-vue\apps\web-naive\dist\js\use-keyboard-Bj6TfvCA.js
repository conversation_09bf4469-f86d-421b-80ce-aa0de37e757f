import{o as r,h as y,a as c}from"./bootstrap-B_sue86n.js";import{r as m,e as h,o as w,f as b,w as v}from"../jse/index-index-UaL0SrHU.js";function j(p={},n){const t=m({ctrl:!1,command:!1,win:!1,shift:!1,tab:!1}),{keydown:u,keyup:i}=p,s=e=>{switch(e.key){case"Control":t.ctrl=!0;break;case"Meta":t.command=!0,t.win=!0;break;case"Shift":t.shift=!0;break;case"Tab":t.tab=!0;break}u!==void 0&&Object.keys(u).forEach(o=>{if(o!==e.key)return;const a=u[o];if(typeof a=="function")a(e);else{const{stop:d=!1,prevent:l=!1}=a;d&&e.stopPropagation(),l&&e.preventDefault(),a.handler(e)}})},f=e=>{switch(e.key){case"Control":t.ctrl=!1;break;case"Meta":t.command=!1,t.win=!1;break;case"Shift":t.shift=!1;break;case"Tab":t.tab=!1;break}i!==void 0&&Object.keys(i).forEach(o=>{if(o!==e.key)return;const a=i[o];if(typeof a=="function")a(e);else{const{stop:d=!1,prevent:l=!1}=a;d&&e.stopPropagation(),l&&e.preventDefault(),a.handler(e)}})},k=()=>{(n===void 0||n.value)&&(r("keydown",document,s),r("keyup",document,f)),n!==void 0&&v(n,e=>{e?(r("keydown",document,s),r("keyup",document,f)):(c("keydown",document,s),c("keyup",document,f))})};return y()?(w(k),b(()=>{(n===void 0||n.value)&&(c("keydown",document,s),c("keyup",document,f))})):k(),h(t)}export{j as u};
