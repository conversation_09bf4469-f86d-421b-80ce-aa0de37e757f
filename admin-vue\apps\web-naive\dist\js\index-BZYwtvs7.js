import{d as Qe,N as Dt}from"./Tooltip-BZwoD6vN.js";import{p as ri}from"./Tooltip-BZwoD6vN.js";import{bB as et,a6 as te,b4 as tt,m as ae,a7 as $e,q as N,g as m,bC as Ue,ba as Mt,i as jt,a4 as A,aF as Ut,aG as Ft,aN as Ce,bD as Nt,bE as Et,a as fe,l as ue,n as ze,aH as At,o as Re,bF as Le,a3 as rt,bG as ot,bH as it,bI as nt,bJ as lt,k as M,bK as _t,z as Fe,b7 as Ne,j as Q,s as Ht,bL as me,b6 as at,bM as Ee,I as ge,N as Wt,Z as Vt,bp as Zt,bN as qt,v as Xt,y as Ae}from"./bootstrap-B_sue86n.js";import{h as t,d as q,F as ve,a4 as _e,E as T,g as W,w as Gt,f as st,b as oe,c as $,y as Yt,P as Ie,U as Kt,j as He,V as Oe,am as Jt,z as Qt}from"../jse/index-index-UaL0SrHU.js";import{f as ne}from"./Popover-ulf1mwTf.js";import{u as er}from"./use-locale-zaiRAV2Y.js";import{b as tr}from"./Follower-C2co6Kvh.js";import{E as rr}from"./Eye-tfCY-2yO.js";import{A as or}from"./Add-GRFR-Jxi.js";import{u as ir}from"./use-merged-state-lZNesr9e.js";function nr(e,o,r,i){for(var n=-1,a=e==null?0:e.length;++n<a;)r=o(r,e[n],n,e);return r}function lr(e){return function(o){return e==null?void 0:e[o]}}var ar={À:"A",Á:"A",Â:"A",Ã:"A",Ä:"A",Å:"A",à:"a",á:"a",â:"a",ã:"a",ä:"a",å:"a",Ç:"C",ç:"c",Ð:"D",ð:"d",È:"E",É:"E",Ê:"E",Ë:"E",è:"e",é:"e",ê:"e",ë:"e",Ì:"I",Í:"I",Î:"I",Ï:"I",ì:"i",í:"i",î:"i",ï:"i",Ñ:"N",ñ:"n",Ò:"O",Ó:"O",Ô:"O",Õ:"O",Ö:"O",Ø:"O",ò:"o",ó:"o",ô:"o",õ:"o",ö:"o",ø:"o",Ù:"U",Ú:"U",Û:"U",Ü:"U",ù:"u",ú:"u",û:"u",ü:"u",Ý:"Y",ý:"y",ÿ:"y",Æ:"Ae",æ:"ae",Þ:"Th",þ:"th",ß:"ss",Ā:"A",Ă:"A",Ą:"A",ā:"a",ă:"a",ą:"a",Ć:"C",Ĉ:"C",Ċ:"C",Č:"C",ć:"c",ĉ:"c",ċ:"c",č:"c",Ď:"D",Đ:"D",ď:"d",đ:"d",Ē:"E",Ĕ:"E",Ė:"E",Ę:"E",Ě:"E",ē:"e",ĕ:"e",ė:"e",ę:"e",ě:"e",Ĝ:"G",Ğ:"G",Ġ:"G",Ģ:"G",ĝ:"g",ğ:"g",ġ:"g",ģ:"g",Ĥ:"H",Ħ:"H",ĥ:"h",ħ:"h",Ĩ:"I",Ī:"I",Ĭ:"I",Į:"I",İ:"I",ĩ:"i",ī:"i",ĭ:"i",į:"i",ı:"i",Ĵ:"J",ĵ:"j",Ķ:"K",ķ:"k",ĸ:"k",Ĺ:"L",Ļ:"L",Ľ:"L",Ŀ:"L",Ł:"L",ĺ:"l",ļ:"l",ľ:"l",ŀ:"l",ł:"l",Ń:"N",Ņ:"N",Ň:"N",Ŋ:"N",ń:"n",ņ:"n",ň:"n",ŋ:"n",Ō:"O",Ŏ:"O",Ő:"O",ō:"o",ŏ:"o",ő:"o",Ŕ:"R",Ŗ:"R",Ř:"R",ŕ:"r",ŗ:"r",ř:"r",Ś:"S",Ŝ:"S",Ş:"S",Š:"S",ś:"s",ŝ:"s",ş:"s",š:"s",Ţ:"T",Ť:"T",Ŧ:"T",ţ:"t",ť:"t",ŧ:"t",Ũ:"U",Ū:"U",Ŭ:"U",Ů:"U",Ű:"U",Ų:"U",ũ:"u",ū:"u",ŭ:"u",ů:"u",ű:"u",ų:"u",Ŵ:"W",ŵ:"w",Ŷ:"Y",ŷ:"y",Ÿ:"Y",Ź:"Z",Ż:"Z",Ž:"Z",ź:"z",ż:"z",ž:"z",Ĳ:"IJ",ĳ:"ij",Œ:"Oe",œ:"oe",ŉ:"'n",ſ:"s"},sr=lr(ar),dr=/[\xc0-\xd6\xd8-\xf6\xf8-\xff\u0100-\u017f]/g,ur="\\u0300-\\u036f",cr="\\ufe20-\\ufe2f",fr="\\u20d0-\\u20ff",gr=ur+cr+fr,hr="["+gr+"]",pr=RegExp(hr,"g");function vr(e){return e=et(e),e&&e.replace(dr,sr).replace(pr,"")}var mr=/[^\x00-\x2f\x3a-\x40\x5b-\x60\x7b-\x7f]+/g;function br(e){return e.match(mr)||[]}var wr=/[a-z][A-Z]|[A-Z]{2}[a-z]|[0-9][a-zA-Z]|[a-zA-Z][0-9]|[^a-zA-Z0-9 ]/;function xr(e){return wr.test(e)}var dt="\\ud800-\\udfff",yr="\\u0300-\\u036f",Cr="\\ufe20-\\ufe2f",Rr="\\u20d0-\\u20ff",Sr=yr+Cr+Rr,ut="\\u2700-\\u27bf",ct="a-z\\xdf-\\xf6\\xf8-\\xff",kr="\\xac\\xb1\\xd7\\xf7",Pr="\\x00-\\x2f\\x3a-\\x40\\x5b-\\x60\\x7b-\\xbf",Lr="\\u2000-\\u206f",Or=" \\t\\x0b\\f\\xa0\\ufeff\\n\\r\\u2028\\u2029\\u1680\\u180e\\u2000\\u2001\\u2002\\u2003\\u2004\\u2005\\u2006\\u2007\\u2008\\u2009\\u200a\\u202f\\u205f\\u3000",ft="A-Z\\xc0-\\xd6\\xd8-\\xde",Tr="\\ufe0e\\ufe0f",gt=kr+Pr+Lr+Or,ht="['’]",We="["+gt+"]",$r="["+Sr+"]",pt="\\d+",zr="["+ut+"]",vt="["+ct+"]",mt="[^"+dt+gt+pt+ut+ct+ft+"]",Ir="\\ud83c[\\udffb-\\udfff]",Br="(?:"+$r+"|"+Ir+")",Dr="[^"+dt+"]",bt="(?:\\ud83c[\\udde6-\\uddff]){2}",wt="[\\ud800-\\udbff][\\udc00-\\udfff]",le="["+ft+"]",Mr="\\u200d",Ve="(?:"+vt+"|"+mt+")",jr="(?:"+le+"|"+mt+")",Ze="(?:"+ht+"(?:d|ll|m|re|s|t|ve))?",qe="(?:"+ht+"(?:D|LL|M|RE|S|T|VE))?",xt=Br+"?",yt="["+Tr+"]?",Ur="(?:"+Mr+"(?:"+[Dr,bt,wt].join("|")+")"+yt+xt+")*",Fr="\\d*(?:1st|2nd|3rd|(?![123])\\dth)(?=\\b|[A-Z_])",Nr="\\d*(?:1ST|2ND|3RD|(?![123])\\dTH)(?=\\b|[a-z_])",Er=yt+xt+Ur,Ar="(?:"+[zr,bt,wt].join("|")+")"+Er,_r=RegExp([le+"?"+vt+"+"+Ze+"(?="+[We,le,"$"].join("|")+")",jr+"+"+qe+"(?="+[We,le+Ve,"$"].join("|")+")",le+"?"+Ve+"+"+Ze,le+"+"+qe,Nr,Fr,pt,Ar].join("|"),"g");function Hr(e){return e.match(_r)||[]}function Wr(e,o,r){return e=et(e),o=o,o===void 0?xr(e)?Hr(e):br(e):e.match(o)||[]}var Vr="['’]",Zr=RegExp(Vr,"g");function qr(e){return function(o){return nr(Wr(vr(o).replace(Zr,"")),e,"")}}var Xr=qr(function(e,o,r){return e+(r?"-":"")+o.toLowerCase()});const Gr=te("attach",()=>t("svg",{viewBox:"0 0 16 16",version:"1.1",xmlns:"http://www.w3.org/2000/svg"},t("g",{stroke:"none","stroke-width":"1",fill:"none","fill-rule":"evenodd"},t("g",{fill:"currentColor","fill-rule":"nonzero"},t("path",{d:"M3.25735931,8.70710678 L7.85355339,4.1109127 C8.82986412,3.13460197 10.4127766,3.13460197 11.3890873,4.1109127 C12.365398,5.08722343 12.365398,6.67013588 11.3890873,7.64644661 L6.08578644,12.9497475 C5.69526215,13.3402718 5.06209717,13.3402718 4.67157288,12.9497475 C4.28104858,12.5592232 4.28104858,11.9260582 4.67157288,11.5355339 L9.97487373,6.23223305 C10.1701359,6.0369709 10.1701359,5.72038841 9.97487373,5.52512627 C9.77961159,5.32986412 9.4630291,5.32986412 9.26776695,5.52512627 L3.96446609,10.8284271 C3.18341751,11.6094757 3.18341751,12.8758057 3.96446609,13.6568542 C4.74551468,14.4379028 6.01184464,14.4379028 6.79289322,13.6568542 L12.0961941,8.35355339 C13.4630291,6.98671837 13.4630291,4.77064094 12.0961941,3.40380592 C10.7293591,2.0369709 8.51328163,2.0369709 7.14644661,3.40380592 L2.55025253,8 C2.35499039,8.19526215 2.35499039,8.51184464 2.55025253,8.70710678 C2.74551468,8.90236893 3.06209717,8.90236893 3.25735931,8.70710678 Z"}))))),Yr=te("cancel",()=>t("svg",{viewBox:"0 0 16 16",version:"1.1",xmlns:"http://www.w3.org/2000/svg"},t("g",{stroke:"none","stroke-width":"1",fill:"none","fill-rule":"evenodd"},t("g",{fill:"currentColor","fill-rule":"nonzero"},t("path",{d:"M2.58859116,2.7156945 L2.64644661,2.64644661 C2.82001296,2.47288026 3.08943736,2.45359511 3.2843055,2.58859116 L3.35355339,2.64644661 L8,7.293 L12.6464466,2.64644661 C12.8417088,2.45118446 13.1582912,2.45118446 13.3535534,2.64644661 C13.5488155,2.84170876 13.5488155,3.15829124 13.3535534,3.35355339 L8.707,8 L13.3535534,12.6464466 C13.5271197,12.820013 13.5464049,13.0894374 13.4114088,13.2843055 L13.3535534,13.3535534 C13.179987,13.5271197 12.9105626,13.5464049 12.7156945,13.4114088 L12.6464466,13.3535534 L8,8.707 L3.35355339,13.3535534 C3.15829124,13.5488155 2.84170876,13.5488155 2.64644661,13.3535534 C2.45118446,13.1582912 2.45118446,12.8417088 2.64644661,12.6464466 L7.293,8 L2.64644661,3.35355339 C2.47288026,3.17998704 2.45359511,2.91056264 2.58859116,2.7156945 L2.64644661,2.64644661 L2.58859116,2.7156945 Z"}))))),Ct=te("download",()=>t("svg",{viewBox:"0 0 16 16",version:"1.1",xmlns:"http://www.w3.org/2000/svg"},t("g",{stroke:"none","stroke-width":"1",fill:"none","fill-rule":"evenodd"},t("g",{fill:"currentColor","fill-rule":"nonzero"},t("path",{d:"M3.5,13 L12.5,13 C12.7761424,13 13,13.2238576 13,13.5 C13,13.7454599 12.8231248,13.9496084 12.5898756,13.9919443 L12.5,14 L3.5,14 C3.22385763,14 3,13.7761424 3,13.5 C3,13.2545401 3.17687516,13.0503916 3.41012437,13.0080557 L3.5,13 L12.5,13 L3.5,13 Z M7.91012437,1.00805567 L8,1 C8.24545989,1 8.44960837,1.17687516 8.49194433,1.41012437 L8.5,1.5 L8.5,10.292 L11.1819805,7.6109127 C11.3555469,7.43734635 11.6249713,7.4180612 11.8198394,7.55305725 L11.8890873,7.6109127 C12.0626536,7.78447906 12.0819388,8.05390346 11.9469427,8.2487716 L11.8890873,8.31801948 L8.35355339,11.8535534 C8.17998704,12.0271197 7.91056264,12.0464049 7.7156945,11.9114088 L7.64644661,11.8535534 L4.1109127,8.31801948 C3.91565056,8.12275734 3.91565056,7.80617485 4.1109127,7.6109127 C4.28447906,7.43734635 4.55390346,7.4180612 4.7487716,7.55305725 L4.81801948,7.6109127 L7.5,10.292 L7.5,1.5 C7.5,1.25454011 7.67687516,1.05039163 7.91012437,1.00805567 L8,1 L7.91012437,1.00805567 Z"}))))),Kr=q({name:"ResizeSmall",render(){return t("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 20 20"},t("g",{fill:"none"},t("path",{d:"M5.5 4A1.5 1.5 0 0 0 4 5.5v1a.5.5 0 0 1-1 0v-1A2.5 2.5 0 0 1 5.5 3h1a.5.5 0 0 1 0 1h-1zM16 5.5A1.5 1.5 0 0 0 14.5 4h-1a.5.5 0 0 1 0-1h1A2.5 2.5 0 0 1 17 5.5v1a.5.5 0 0 1-1 0v-1zm0 9a1.5 1.5 0 0 1-1.5 1.5h-1a.5.5 0 0 0 0 1h1a2.5 2.5 0 0 0 2.5-2.5v-1a.5.5 0 0 0-1 0v1zm-12 0A1.5 1.5 0 0 0 5.5 16h1.25a.5.5 0 0 1 0 1H5.5A2.5 2.5 0 0 1 3 14.5v-1.25a.5.5 0 0 1 1 0v1.25zM8.5 7A1.5 1.5 0 0 0 7 8.5v3A1.5 1.5 0 0 0 8.5 13h3a1.5 1.5 0 0 0 1.5-1.5v-3A1.5 1.5 0 0 0 11.5 7h-3zM8 8.5a.5.5 0 0 1 .5-.5h3a.5.5 0 0 1 .5.5v3a.5.5 0 0 1-.5.5h-3a.5.5 0 0 1-.5-.5v-3z",fill:"currentColor"})))}}),Jr=te("retry",()=>t("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 512 512"},t("path",{d:"M320,146s24.36-12-64-12A160,160,0,1,0,416,294",style:"fill: none; stroke: currentcolor; stroke-linecap: round; stroke-miterlimit: 10; stroke-width: 32px;"}),t("polyline",{points:"256 58 336 138 256 218",style:"fill: none; stroke: currentcolor; stroke-linecap: round; stroke-linejoin: round; stroke-width: 32px;"}))),Qr=te("rotateClockwise",()=>t("svg",{viewBox:"0 0 20 20",fill:"none",xmlns:"http://www.w3.org/2000/svg"},t("path",{d:"M3 10C3 6.13401 6.13401 3 10 3C13.866 3 17 6.13401 17 10C17 12.7916 15.3658 15.2026 13 16.3265V14.5C13 14.2239 12.7761 14 12.5 14C12.2239 14 12 14.2239 12 14.5V17.5C12 17.7761 12.2239 18 12.5 18H15.5C15.7761 18 16 17.7761 16 17.5C16 17.2239 15.7761 17 15.5 17H13.8758C16.3346 15.6357 18 13.0128 18 10C18 5.58172 14.4183 2 10 2C5.58172 2 2 5.58172 2 10C2 10.2761 2.22386 10.5 2.5 10.5C2.77614 10.5 3 10.2761 3 10Z",fill:"currentColor"}),t("path",{d:"M10 12C11.1046 12 12 11.1046 12 10C12 8.89543 11.1046 8 10 8C8.89543 8 8 8.89543 8 10C8 11.1046 8.89543 12 10 12ZM10 11C9.44772 11 9 10.5523 9 10C9 9.44772 9.44772 9 10 9C10.5523 9 11 9.44772 11 10C11 10.5523 10.5523 11 10 11Z",fill:"currentColor"}))),eo=te("rotateClockwise",()=>t("svg",{viewBox:"0 0 20 20",fill:"none",xmlns:"http://www.w3.org/2000/svg"},t("path",{d:"M17 10C17 6.13401 13.866 3 10 3C6.13401 3 3 6.13401 3 10C3 12.7916 4.63419 15.2026 7 16.3265V14.5C7 14.2239 7.22386 14 7.5 14C7.77614 14 8 14.2239 8 14.5V17.5C8 17.7761 7.77614 18 7.5 18H4.5C4.22386 18 4 17.7761 4 17.5C4 17.2239 4.22386 17 4.5 17H6.12422C3.66539 15.6357 2 13.0128 2 10C2 5.58172 5.58172 2 10 2C14.4183 2 18 5.58172 18 10C18 10.2761 17.7761 10.5 17.5 10.5C17.2239 10.5 17 10.2761 17 10Z",fill:"currentColor"}),t("path",{d:"M10 12C8.89543 12 8 11.1046 8 10C8 8.89543 8.89543 8 10 8C11.1046 8 12 8.89543 12 10C12 11.1046 11.1046 12 10 12ZM10 11C10.5523 11 11 10.5523 11 10C11 9.44772 10.5523 9 10 9C9.44772 9 9 9.44772 9 10C9 10.5523 9.44772 11 10 11Z",fill:"currentColor"}))),to=te("trash",()=>t("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 512 512"},t("path",{d:"M432,144,403.33,419.74A32,32,0,0,1,371.55,448H140.46a32,32,0,0,1-31.78-28.26L80,144",style:"fill: none; stroke: currentcolor; stroke-linecap: round; stroke-linejoin: round; stroke-width: 32px;"}),t("rect",{x:"32",y:"64",width:"448",height:"80",rx:"16",ry:"16",style:"fill: none; stroke: currentcolor; stroke-linecap: round; stroke-linejoin: round; stroke-width: 32px;"}),t("line",{x1:"312",y1:"240",x2:"200",y2:"352",style:"fill: none; stroke: currentcolor; stroke-linecap: round; stroke-linejoin: round; stroke-width: 32px;"}),t("line",{x1:"312",y1:"352",x2:"200",y2:"240",style:"fill: none; stroke: currentcolor; stroke-linecap: round; stroke-linejoin: round; stroke-width: 32px;"}))),ro=te("zoomIn",()=>t("svg",{viewBox:"0 0 20 20",fill:"none",xmlns:"http://www.w3.org/2000/svg"},t("path",{d:"M11.5 8.5C11.5 8.22386 11.2761 8 11 8H9V6C9 5.72386 8.77614 5.5 8.5 5.5C8.22386 5.5 8 5.72386 8 6V8H6C5.72386 8 5.5 8.22386 5.5 8.5C5.5 8.77614 5.72386 9 6 9H8V11C8 11.2761 8.22386 11.5 8.5 11.5C8.77614 11.5 9 11.2761 9 11V9H11C11.2761 9 11.5 8.77614 11.5 8.5Z",fill:"currentColor"}),t("path",{d:"M8.5 3C11.5376 3 14 5.46243 14 8.5C14 9.83879 13.5217 11.0659 12.7266 12.0196L16.8536 16.1464C17.0488 16.3417 17.0488 16.6583 16.8536 16.8536C16.68 17.0271 16.4106 17.0464 16.2157 16.9114L16.1464 16.8536L12.0196 12.7266C11.0659 13.5217 9.83879 14 8.5 14C5.46243 14 3 11.5376 3 8.5C3 5.46243 5.46243 3 8.5 3ZM8.5 4C6.01472 4 4 6.01472 4 8.5C4 10.9853 6.01472 13 8.5 13C10.9853 13 13 10.9853 13 8.5C13 6.01472 10.9853 4 8.5 4Z",fill:"currentColor"}))),oo=te("zoomOut",()=>t("svg",{viewBox:"0 0 20 20",fill:"none",xmlns:"http://www.w3.org/2000/svg"},t("path",{d:"M11 8C11.2761 8 11.5 8.22386 11.5 8.5C11.5 8.77614 11.2761 9 11 9H6C5.72386 9 5.5 8.77614 5.5 8.5C5.5 8.22386 5.72386 8 6 8H11Z",fill:"currentColor"}),t("path",{d:"M14 8.5C14 5.46243 11.5376 3 8.5 3C5.46243 3 3 5.46243 3 8.5C3 11.5376 5.46243 14 8.5 14C9.83879 14 11.0659 13.5217 12.0196 12.7266L16.1464 16.8536L16.2157 16.9114C16.4106 17.0464 16.68 17.0271 16.8536 16.8536C17.0488 16.6583 17.0488 16.3417 16.8536 16.1464L12.7266 12.0196C13.5217 11.0659 14 9.83879 14 8.5ZM4 8.5C4 6.01472 6.01472 4 8.5 4C10.9853 4 13 6.01472 13 8.5C13 10.9853 10.9853 13 8.5 13C6.01472 13 4 10.9853 4 8.5Z",fill:"currentColor"}))),io=tt&&"loading"in document.createElement("img");function no(e={}){var o;const{root:r=null}=e;return{hash:`${e.rootMargin||"0px 0px 0px 0px"}-${Array.isArray(e.threshold)?e.threshold.join(","):(o=e.threshold)!==null&&o!==void 0?o:"0"}`,options:Object.assign(Object.assign({},e),{root:(typeof r=="string"?document.querySelector(r):r)||document.documentElement})}}const Se=new WeakMap,ke=new WeakMap,Pe=new WeakMap,lo=(e,o,r)=>{if(!e)return()=>{};const i=no(o),{root:n}=i.options;let a;const u=Se.get(n);u?a=u:(a=new Map,Se.set(n,a));let c,d;a.has(i.hash)?(d=a.get(i.hash),d[1].has(e)||(c=d[0],d[1].add(e),c.observe(e))):(c=new IntersectionObserver(f=>{f.forEach(b=>{if(b.isIntersecting){const y=ke.get(b.target),g=Pe.get(b.target);y&&y(),g&&(g.value=!0)}})},i.options),c.observe(e),d=[c,new Set([e])],a.set(i.hash,d));let l=!1;const s=()=>{l||(ke.delete(e),Pe.delete(e),l=!0,d[1].has(e)&&(d[0].unobserve(e),d[1].delete(e)),d[1].size<=0&&a.delete(i.hash),a.size||Se.delete(n))};return ke.set(e,s),Pe.set(e,r),s};function ao(){return t("svg",{viewBox:"0 0 20 20",fill:"none",xmlns:"http://www.w3.org/2000/svg"},t("path",{d:"M6 5C5.75454 5 5.55039 5.17688 5.50806 5.41012L5.5 5.5V14.5C5.5 14.7761 5.72386 15 6 15C6.24546 15 6.44961 14.8231 6.49194 14.5899L6.5 14.5V5.5C6.5 5.22386 6.27614 5 6 5ZM13.8536 5.14645C13.68 4.97288 13.4106 4.9536 13.2157 5.08859L13.1464 5.14645L8.64645 9.64645C8.47288 9.82001 8.4536 10.0894 8.58859 10.2843L8.64645 10.3536L13.1464 14.8536C13.3417 15.0488 13.6583 15.0488 13.8536 14.8536C14.0271 14.68 14.0464 14.4106 13.9114 14.2157L13.8536 14.1464L9.70711 10L13.8536 5.85355C14.0488 5.65829 14.0488 5.34171 13.8536 5.14645Z",fill:"currentColor"}))}function so(){return t("svg",{viewBox:"0 0 20 20",fill:"none",xmlns:"http://www.w3.org/2000/svg"},t("path",{d:"M13.5 5C13.7455 5 13.9496 5.17688 13.9919 5.41012L14 5.5V14.5C14 14.7761 13.7761 15 13.5 15C13.2545 15 13.0504 14.8231 13.0081 14.5899L13 14.5V5.5C13 5.22386 13.2239 5 13.5 5ZM5.64645 5.14645C5.82001 4.97288 6.08944 4.9536 6.28431 5.08859L6.35355 5.14645L10.8536 9.64645C11.0271 9.82001 11.0464 10.0894 10.9114 10.2843L10.8536 10.3536L6.35355 14.8536C6.15829 15.0488 5.84171 15.0488 5.64645 14.8536C5.47288 14.68 5.4536 14.4106 5.58859 14.2157L5.64645 14.1464L9.79289 10L5.64645 5.85355C5.45118 5.65829 5.45118 5.34171 5.64645 5.14645Z",fill:"currentColor"}))}function uo(){return t("svg",{viewBox:"0 0 20 20",fill:"none",xmlns:"http://www.w3.org/2000/svg"},t("path",{d:"M4.089 4.216l.057-.07a.5.5 0 0 1 .638-.057l.07.057L10 9.293l5.146-5.147a.5.5 0 0 1 .638-.057l.07.057a.5.5 0 0 1 .057.638l-.057.07L10.707 10l5.147 5.146a.5.5 0 0 1 .057.638l-.057.07a.5.5 0 0 1-.638.057l-.07-.057L10 10.707l-5.146 5.147a.5.5 0 0 1-.638.057l-.07-.057a.5.5 0 0 1-.057-.638l.057-.07L9.293 10L4.146 4.854a.5.5 0 0 1-.057-.638l.057-.07l-.057.07z",fill:"currentColor"}))}const Be=Object.assign(Object.assign({},ae.props),{onPreviewPrev:Function,onPreviewNext:Function,showToolbar:{type:Boolean,default:!0},showToolbarTooltip:Boolean,renderToolbar:Function}),Rt=$e("n-image"),co=N([N("body >",[m("image-container","position: fixed;")]),m("image-preview-container",`
 position: fixed;
 left: 0;
 right: 0;
 top: 0;
 bottom: 0;
 display: flex;
 `),m("image-preview-overlay",`
 z-index: -1;
 position: absolute;
 left: 0;
 right: 0;
 top: 0;
 bottom: 0;
 background: rgba(0, 0, 0, .3);
 `,[Ue()]),m("image-preview-toolbar",`
 z-index: 1;
 position: absolute;
 left: 50%;
 transform: translateX(-50%);
 border-radius: var(--n-toolbar-border-radius);
 height: 48px;
 bottom: 40px;
 padding: 0 12px;
 background: var(--n-toolbar-color);
 box-shadow: var(--n-toolbar-box-shadow);
 color: var(--n-toolbar-icon-color);
 transition: color .3s var(--n-bezier);
 display: flex;
 align-items: center;
 `,[m("base-icon",`
 padding: 0 8px;
 font-size: 28px;
 cursor: pointer;
 `),Ue()]),m("image-preview-wrapper",`
 position: absolute;
 left: 0;
 right: 0;
 top: 0;
 bottom: 0;
 display: flex;
 pointer-events: none;
 `,[Mt()]),m("image-preview",`
 user-select: none;
 -webkit-user-select: none;
 pointer-events: all;
 margin: auto;
 max-height: calc(100vh - 32px);
 max-width: calc(100vw - 32px);
 transition: transform .3s var(--n-bezier);
 `),m("image",`
 display: inline-flex;
 max-height: 100%;
 max-width: 100%;
 `,[jt("preview-disabled",`
 cursor: pointer;
 `),N("img",`
 border-radius: inherit;
 `)])]),he=32,St=q({name:"ImagePreview",props:Object.assign(Object.assign({},Be),{onNext:Function,onPrev:Function,clsPrefix:{type:String,required:!0}}),setup(e){const o=ae("Image","-image",co,Et,e,T(e,"clsPrefix"));let r=null;const i=W(null),n=W(null),a=W(void 0),u=W(!1),c=W(!1),{localeRef:d}=er("Image");function l(){const{value:h}=n;if(!r||!h)return;const{style:x}=h,v=r.getBoundingClientRect(),B=v.left+v.width/2,D=v.top+v.height/2;x.transformOrigin=`${B}px ${D}px`}function s(h){var x,v;switch(h.key){case" ":h.preventDefault();break;case"ArrowLeft":(x=e.onPrev)===null||x===void 0||x.call(e);break;case"ArrowRight":(v=e.onNext)===null||v===void 0||v.call(e);break;case"Escape":De();break}}Gt(u,h=>{h?Re("keydown",document,s):fe("keydown",document,s)}),st(()=>{fe("keydown",document,s)});let f=0,b=0,y=0,g=0,C=0,j=0,z=0,k=0,E=!1;function L(h){const{clientX:x,clientY:v}=h;y=x-f,g=v-b,tr(K)}function p(h){const{mouseUpClientX:x,mouseUpClientY:v,mouseDownClientX:B,mouseDownClientY:D}=h,Z=B-x,Y=D-v,J=`vertical${Y>0?"Top":"Bottom"}`,re=`horizontal${Z>0?"Left":"Right"}`;return{moveVerticalDirection:J,moveHorizontalDirection:re,deltaHorizontal:Z,deltaVertical:Y}}function R(h){const{value:x}=i;if(!x)return{offsetX:0,offsetY:0};const v=x.getBoundingClientRect(),{moveVerticalDirection:B,moveHorizontalDirection:D,deltaHorizontal:Z,deltaVertical:Y}=h||{};let J=0,re=0;return v.width<=window.innerWidth?J=0:v.left>0?J=(v.width-window.innerWidth)/2:v.right<window.innerWidth?J=-(v.width-window.innerWidth)/2:D==="horizontalRight"?J=Math.min((v.width-window.innerWidth)/2,C-(Z!=null?Z:0)):J=Math.max(-((v.width-window.innerWidth)/2),C-(Z!=null?Z:0)),v.height<=window.innerHeight?re=0:v.top>0?re=(v.height-window.innerHeight)/2:v.bottom<window.innerHeight?re=-(v.height-window.innerHeight)/2:B==="verticalBottom"?re=Math.min((v.height-window.innerHeight)/2,j-(Y!=null?Y:0)):re=Math.max(-((v.height-window.innerHeight)/2),j-(Y!=null?Y:0)),{offsetX:J,offsetY:re}}function S(h){fe("mousemove",document,L),fe("mouseup",document,S);const{clientX:x,clientY:v}=h;E=!1;const B=p({mouseUpClientX:x,mouseUpClientY:v,mouseDownClientX:z,mouseDownClientY:k}),D=R(B);y=D.offsetX,g=D.offsetY,K()}const U=oe(Rt,null);function w(h){var x,v;if((v=(x=U==null?void 0:U.previewedImgPropsRef.value)===null||x===void 0?void 0:x.onMousedown)===null||v===void 0||v.call(x,h),h.button!==0)return;const{clientX:B,clientY:D}=h;E=!0,f=B-y,b=D-g,C=y,j=g,z=B,k=D,K(),Re("mousemove",document,L),Re("mouseup",document,S)}const I=1.5;let P=0,O=1,_=0;function V(h){var x,v;(v=(x=U==null?void 0:U.previewedImgPropsRef.value)===null||x===void 0?void 0:x.onDblclick)===null||v===void 0||v.call(x,h);const B=ce();O=O===B?1:B,K()}function H(){O=1,P=0}function F(){var h;H(),_=0,(h=e.onPrev)===null||h===void 0||h.call(e)}function G(){var h;H(),_=0,(h=e.onNext)===null||h===void 0||h.call(e)}function X(){_-=90,K()}function ee(){_+=90,K()}function be(){const{value:h}=i;if(!h)return 1;const{innerWidth:x,innerHeight:v}=window,B=Math.max(1,h.naturalHeight/(v-he)),D=Math.max(1,h.naturalWidth/(x-he));return Math.max(3,B*2,D*2)}function ce(){const{value:h}=i;if(!h)return 1;const{innerWidth:x,innerHeight:v}=window,B=h.naturalHeight/(v-he),D=h.naturalWidth/(x-he);return B<1&&D<1?1:Math.max(B,D)}function we(){const h=be();O<h&&(P+=1,O=Math.min(h,Math.pow(I,P)),K())}function xe(){if(O>.5){const h=O;P-=1,O=Math.max(.5,Math.pow(I,P));const x=h-O;K(!1);const v=R();O+=x,K(!1),O-=x,y=v.offsetX,g=v.offsetY,K()}}function ye(){const h=a.value;h&&Qe(h,void 0)}function K(h=!0){var x;const{value:v}=i;if(!v)return;const{style:B}=v,D=Yt((x=U==null?void 0:U.previewedImgPropsRef.value)===null||x===void 0?void 0:x.style);let Z="";if(typeof D=="string")Z=`${D};`;else for(const J in D)Z+=`${Xr(J)}: ${D[J]};`;const Y=`transform-origin: center; transform: translateX(${y}px) translateY(${g}px) rotate(${_}deg) scale(${O});`;E?B.cssText=`${Z}cursor: grabbing; transition: none;${Y}`:B.cssText=`${Z}cursor: grab;${Y}${h?"":"transition: none;"}`,h||v.offsetHeight}function De(){u.value=!u.value,c.value=!0}function zt(){O=ce(),P=Math.ceil(Math.log(O)/Math.log(I)),y=0,g=0,K()}const It={setPreviewSrc:h=>{a.value=h},setThumbnailEl:h=>{r=h},toggleShow:De};function Bt(h,x){if(e.showToolbarTooltip){const{value:v}=o;return t(Dt,{to:!1,theme:v.peers.Tooltip,themeOverrides:v.peerOverrides.Tooltip,keepAliveOnHover:!1},{default:()=>d.value[x],trigger:()=>h})}else return h}const Me=$(()=>{const{common:{cubicBezierEaseInOut:h},self:{toolbarIconColor:x,toolbarBorderRadius:v,toolbarBoxShadow:B,toolbarColor:D}}=o.value;return{"--n-bezier":h,"--n-toolbar-icon-color":x,"--n-toolbar-color":D,"--n-toolbar-border-radius":v,"--n-toolbar-box-shadow":B}}),{inlineThemeDisabled:je}=ue(),ie=je?ze("image-preview",void 0,Me,e):void 0;return Object.assign({previewRef:i,previewWrapperRef:n,previewSrc:a,show:u,appear:At(),displayed:c,previewedImgProps:U==null?void 0:U.previewedImgPropsRef,handleWheel(h){h.preventDefault()},handlePreviewMousedown:w,handlePreviewDblclick:V,syncTransformOrigin:l,handleAfterLeave:()=>{H(),_=0,c.value=!1},handleDragStart:h=>{var x,v;(v=(x=U==null?void 0:U.previewedImgPropsRef.value)===null||x===void 0?void 0:x.onDragstart)===null||v===void 0||v.call(x,h),h.preventDefault()},zoomIn:we,zoomOut:xe,handleDownloadClick:ye,rotateCounterclockwise:X,rotateClockwise:ee,handleSwitchPrev:F,handleSwitchNext:G,withTooltip:Bt,resizeToOrignalImageSize:zt,cssVars:je?void 0:Me,themeClass:ie==null?void 0:ie.themeClass,onRender:ie==null?void 0:ie.onRender},It)},render(){var e,o;const{clsPrefix:r,renderToolbar:i,withTooltip:n}=this,a=n(t(A,{clsPrefix:r,onClick:this.handleSwitchPrev},{default:ao}),"tipPrevious"),u=n(t(A,{clsPrefix:r,onClick:this.handleSwitchNext},{default:so}),"tipNext"),c=n(t(A,{clsPrefix:r,onClick:this.rotateCounterclockwise},{default:()=>t(eo,null)}),"tipCounterclockwise"),d=n(t(A,{clsPrefix:r,onClick:this.rotateClockwise},{default:()=>t(Qr,null)}),"tipClockwise"),l=n(t(A,{clsPrefix:r,onClick:this.resizeToOrignalImageSize},{default:()=>t(Kr,null)}),"tipOriginalSize"),s=n(t(A,{clsPrefix:r,onClick:this.zoomOut},{default:()=>t(oo,null)}),"tipZoomOut"),f=n(t(A,{clsPrefix:r,onClick:this.handleDownloadClick},{default:()=>t(Ct,null)}),"tipDownload"),b=n(t(A,{clsPrefix:r,onClick:this.toggleShow},{default:uo}),"tipClose"),y=n(t(A,{clsPrefix:r,onClick:this.zoomIn},{default:()=>t(ro,null)}),"tipZoomIn");return t(ve,null,(o=(e=this.$slots).default)===null||o===void 0?void 0:o.call(e),t(Ut,{show:this.show},{default:()=>{var g;return this.show||this.displayed?((g=this.onRender)===null||g===void 0||g.call(this),_e(t("div",{class:[`${r}-image-preview-container`,this.themeClass],style:this.cssVars,onWheel:this.handleWheel},t(Ce,{name:"fade-in-transition",appear:this.appear},{default:()=>this.show?t("div",{class:`${r}-image-preview-overlay`,onClick:this.toggleShow}):null}),this.showToolbar?t(Ce,{name:"fade-in-transition",appear:this.appear},{default:()=>this.show?t("div",{class:`${r}-image-preview-toolbar`},i?i({nodes:{prev:a,next:u,rotateCounterclockwise:c,rotateClockwise:d,resizeToOriginalSize:l,zoomOut:s,zoomIn:y,download:f,close:b}}):t(ve,null,this.onPrev?t(ve,null,a,u):null,c,d,l,s,y,f,b)):null}):null,t(Ce,{name:"fade-in-scale-up-transition",onAfterLeave:this.handleAfterLeave,appear:this.appear,onEnter:this.syncTransformOrigin,onBeforeLeave:this.syncTransformOrigin},{default:()=>{const{previewedImgProps:C={}}=this;return _e(t("div",{class:`${r}-image-preview-wrapper`,ref:"previewWrapperRef"},t("img",Object.assign({},C,{draggable:!1,onMousedown:this.handlePreviewMousedown,onDblclick:this.handlePreviewDblclick,class:[`${r}-image-preview`,C.class],key:this.previewSrc,src:this.previewSrc,ref:"previewRef",onDragstart:this.handleDragStart}))),[[Nt,this.show]])}})),[[Ft,{enabled:this.show}]])):null}}))}}),kt=$e("n-image-group"),fo=Be,go=q({name:"ImageGroup",props:fo,setup(e){let o;const{mergedClsPrefixRef:r}=ue(e),i=`c${Le()}`,n=Kt(),a=W(null),u=d=>{var l;o=d,(l=a.value)===null||l===void 0||l.setPreviewSrc(d)};function c(d){var l,s;if(!(n!=null&&n.proxy))return;const b=n.proxy.$el.parentElement.querySelectorAll(`[data-group-id=${i}]:not([data-error=true])`);if(!b.length)return;const y=Array.from(b).findIndex(g=>g.dataset.previewSrc===o);~y?u(b[(y+d+b.length)%b.length].dataset.previewSrc):u(b[0].dataset.previewSrc),d===1?(l=e.onPreviewNext)===null||l===void 0||l.call(e):(s=e.onPreviewPrev)===null||s===void 0||s.call(e)}return Ie(kt,{mergedClsPrefixRef:r,setPreviewSrc:u,setThumbnailEl:d=>{var l;(l=a.value)===null||l===void 0||l.setThumbnailEl(d)},toggleShow:()=>{var d;(d=a.value)===null||d===void 0||d.toggleShow()},groupId:i,renderToolbarRef:T(e,"renderToolbar")}),{mergedClsPrefix:r,previewInstRef:a,next:()=>{c(1)},prev:()=>{c(-1)}}},render(){return t(St,{theme:this.theme,themeOverrides:this.themeOverrides,clsPrefix:this.mergedClsPrefix,ref:"previewInstRef",onPrev:this.prev,onNext:this.next,showToolbar:this.showToolbar,showToolbarTooltip:this.showToolbarTooltip,renderToolbar:this.renderToolbar},this.$slots)}}),ho=Object.assign({alt:String,height:[String,Number],imgProps:Object,previewedImgProps:Object,lazy:Boolean,intersectionObserverOptions:Object,objectFit:{type:String,default:"fill"},previewSrc:String,fallbackSrc:String,width:[String,Number],src:String,previewDisabled:Boolean,loadDescription:String,onError:Function,onLoad:Function},Be),po=q({name:"Image",props:ho,slots:Object,inheritAttrs:!1,setup(e){const o=W(null),r=W(!1),i=W(null),n=oe(kt,null),{mergedClsPrefixRef:a}=n||ue(e),u={click:()=>{if(e.previewDisabled||r.value)return;const l=e.previewSrc||e.src;if(n){n.setPreviewSrc(l),n.setThumbnailEl(o.value),n.toggleShow();return}const{value:s}=i;s&&(s.setPreviewSrc(l),s.setThumbnailEl(o.value),s.toggleShow())}},c=W(!e.lazy);He(()=>{var l;(l=o.value)===null||l===void 0||l.setAttribute("data-group-id",(n==null?void 0:n.groupId)||"")}),He(()=>{if(e.lazy&&e.intersectionObserverOptions){let l;const s=Oe(()=>{l==null||l(),l=void 0,l=lo(o.value,e.intersectionObserverOptions,c)});st(()=>{s(),l==null||l()})}}),Oe(()=>{var l;e.src||((l=e.imgProps)===null||l===void 0||l.src),r.value=!1});const d=W(!1);return Ie(Rt,{previewedImgPropsRef:T(e,"previewedImgProps")}),Object.assign({mergedClsPrefix:a,groupId:n==null?void 0:n.groupId,previewInstRef:i,imageRef:o,showError:r,shouldStartLoading:c,loaded:d,mergedOnClick:l=>{var s,f;u.click(),(f=(s=e.imgProps)===null||s===void 0?void 0:s.onClick)===null||f===void 0||f.call(s,l)},mergedOnError:l=>{if(!c.value)return;r.value=!0;const{onError:s,imgProps:{onError:f}={}}=e;s==null||s(l),f==null||f(l)},mergedOnLoad:l=>{const{onLoad:s,imgProps:{onLoad:f}={}}=e;s==null||s(l),f==null||f(l),d.value=!0}},u)},render(){var e,o;const{mergedClsPrefix:r,imgProps:i={},loaded:n,$attrs:a,lazy:u}=this,c=rt(this.$slots.error,()=>[]),d=(o=(e=this.$slots).placeholder)===null||o===void 0?void 0:o.call(e),l=this.src||i.src,s=this.showError&&c.length?c:t("img",Object.assign(Object.assign({},i),{ref:"imageRef",width:this.width||i.width,height:this.height||i.height,src:this.showError?this.fallbackSrc:u&&this.intersectionObserverOptions?this.shouldStartLoading?l:void 0:l,alt:this.alt||i.alt,"aria-label":this.alt||i.alt,onClick:this.mergedOnClick,onError:this.mergedOnError,onLoad:this.mergedOnLoad,loading:io&&u&&!this.intersectionObserverOptions?"lazy":"eager",style:[i.style||"",d&&!n?{height:"0",width:"0",visibility:"hidden"}:"",{objectFit:this.objectFit}],"data-error":this.showError,"data-preview-src":this.previewSrc||this.src}));return t("div",Object.assign({},a,{role:"none",class:[a.class,`${r}-image`,(this.previewDisabled||this.showError)&&`${r}-image--preview-disabled`]}),this.groupId?s:t(St,{theme:this.theme,themeOverrides:this.themeOverrides,clsPrefix:r,ref:"previewInstRef",showToolbar:this.showToolbar,showToolbarTooltip:this.showToolbarTooltip,renderToolbar:this.renderToolbar},{default:()=>s}),!n&&d)}}),vo={success:t(lt,null),error:t(nt,null),warning:t(it,null),info:t(ot,null)},mo=q({name:"ProgressCircle",props:{clsPrefix:{type:String,required:!0},status:{type:String,required:!0},strokeWidth:{type:Number,required:!0},fillColor:[String,Object],railColor:String,railStyle:[String,Object],percentage:{type:Number,default:0},offsetDegree:{type:Number,default:0},showIndicator:{type:Boolean,required:!0},indicatorTextColor:String,unit:String,viewBoxWidth:{type:Number,required:!0},gapDegree:{type:Number,required:!0},gapOffsetDegree:{type:Number,default:0}},setup(e,{slots:o}){function r(n,a,u,c){const{gapDegree:d,viewBoxWidth:l,strokeWidth:s}=e,f=50,b=0,y=f,g=0,C=2*f,j=50+s/2,z=`M ${j},${j} m ${b},${y}
      a ${f},${f} 0 1 1 ${g},-100
      a ${f},${f} 0 1 1 0,${C}`,k=Math.PI*2*f,E={stroke:c==="rail"?u:typeof e.fillColor=="object"?"url(#gradient)":u,strokeDasharray:`${n/100*(k-d)}px ${l*8}px`,strokeDashoffset:`-${d/2}px`,transformOrigin:a?"center":void 0,transform:a?`rotate(${a}deg)`:void 0};return{pathString:z,pathStyle:E}}const i=()=>{const n=typeof e.fillColor=="object",a=n?e.fillColor.stops[0]:"",u=n?e.fillColor.stops[1]:"";return n&&t("defs",null,t("linearGradient",{id:"gradient",x1:"0%",y1:"100%",x2:"100%",y2:"0%"},t("stop",{offset:"0%","stop-color":a}),t("stop",{offset:"100%","stop-color":u})))};return()=>{const{fillColor:n,railColor:a,strokeWidth:u,offsetDegree:c,status:d,percentage:l,showIndicator:s,indicatorTextColor:f,unit:b,gapOffsetDegree:y,clsPrefix:g}=e,{pathString:C,pathStyle:j}=r(100,0,a,"rail"),{pathString:z,pathStyle:k}=r(l,c,n,"fill"),E=100+u;return t("div",{class:`${g}-progress-content`,role:"none"},t("div",{class:`${g}-progress-graph`,"aria-hidden":!0},t("div",{class:`${g}-progress-graph-circle`,style:{transform:y?`rotate(${y}deg)`:void 0}},t("svg",{viewBox:`0 0 ${E} ${E}`},i(),t("g",null,t("path",{class:`${g}-progress-graph-circle-rail`,d:C,"stroke-width":u,"stroke-linecap":"round",fill:"none",style:j})),t("g",null,t("path",{class:[`${g}-progress-graph-circle-fill`,l===0&&`${g}-progress-graph-circle-fill--empty`],d:z,"stroke-width":u,"stroke-linecap":"round",fill:"none",style:k}))))),s?t("div",null,o.default?t("div",{class:`${g}-progress-custom-content`,role:"none"},o.default()):d!=="default"?t("div",{class:`${g}-progress-icon`,"aria-hidden":!0},t(A,{clsPrefix:g},{default:()=>vo[d]})):t("div",{class:`${g}-progress-text`,style:{color:f},role:"none"},t("span",{class:`${g}-progress-text__percentage`},l),t("span",{class:`${g}-progress-text__unit`},b))):null)}}}),bo={success:t(lt,null),error:t(nt,null),warning:t(it,null),info:t(ot,null)},wo=q({name:"ProgressLine",props:{clsPrefix:{type:String,required:!0},percentage:{type:Number,default:0},railColor:String,railStyle:[String,Object],fillColor:[String,Object],status:{type:String,required:!0},indicatorPlacement:{type:String,required:!0},indicatorTextColor:String,unit:{type:String,default:"%"},processing:{type:Boolean,required:!0},showIndicator:{type:Boolean,required:!0},height:[String,Number],railBorderRadius:[String,Number],fillBorderRadius:[String,Number]},setup(e,{slots:o}){const r=$(()=>ne(e.height)),i=$(()=>{var u,c;return typeof e.fillColor=="object"?`linear-gradient(to right, ${(u=e.fillColor)===null||u===void 0?void 0:u.stops[0]} , ${(c=e.fillColor)===null||c===void 0?void 0:c.stops[1]})`:e.fillColor}),n=$(()=>e.railBorderRadius!==void 0?ne(e.railBorderRadius):e.height!==void 0?ne(e.height,{c:.5}):""),a=$(()=>e.fillBorderRadius!==void 0?ne(e.fillBorderRadius):e.railBorderRadius!==void 0?ne(e.railBorderRadius):e.height!==void 0?ne(e.height,{c:.5}):"");return()=>{const{indicatorPlacement:u,railColor:c,railStyle:d,percentage:l,unit:s,indicatorTextColor:f,status:b,showIndicator:y,processing:g,clsPrefix:C}=e;return t("div",{class:`${C}-progress-content`,role:"none"},t("div",{class:`${C}-progress-graph`,"aria-hidden":!0},t("div",{class:[`${C}-progress-graph-line`,{[`${C}-progress-graph-line--indicator-${u}`]:!0}]},t("div",{class:`${C}-progress-graph-line-rail`,style:[{backgroundColor:c,height:r.value,borderRadius:n.value},d]},t("div",{class:[`${C}-progress-graph-line-fill`,g&&`${C}-progress-graph-line-fill--processing`],style:{maxWidth:`${e.percentage}%`,background:i.value,height:r.value,lineHeight:r.value,borderRadius:a.value}},u==="inside"?t("div",{class:`${C}-progress-graph-line-indicator`,style:{color:f}},o.default?o.default():`${l}${s}`):null)))),y&&u==="outside"?t("div",null,o.default?t("div",{class:`${C}-progress-custom-content`,style:{color:f},role:"none"},o.default()):b==="default"?t("div",{role:"none",class:`${C}-progress-icon ${C}-progress-icon--as-text`,style:{color:f}},l,s):t("div",{class:`${C}-progress-icon`,"aria-hidden":!0},t(A,{clsPrefix:C},{default:()=>bo[b]}))):null)}}});function Xe(e,o,r=100){return`m ${r/2} ${r/2-e} a ${e} ${e} 0 1 1 0 ${2*e} a ${e} ${e} 0 1 1 0 -${2*e}`}const xo=q({name:"ProgressMultipleCircle",props:{clsPrefix:{type:String,required:!0},viewBoxWidth:{type:Number,required:!0},percentage:{type:Array,default:[0]},strokeWidth:{type:Number,required:!0},circleGap:{type:Number,required:!0},showIndicator:{type:Boolean,required:!0},fillColor:{type:Array,default:()=>[]},railColor:{type:Array,default:()=>[]},railStyle:{type:Array,default:()=>[]}},setup(e,{slots:o}){const r=$(()=>e.percentage.map((a,u)=>`${Math.PI*a/100*(e.viewBoxWidth/2-e.strokeWidth/2*(1+2*u)-e.circleGap*u)*2}, ${e.viewBoxWidth*8}`)),i=(n,a)=>{const u=e.fillColor[a],c=typeof u=="object"?u.stops[0]:"",d=typeof u=="object"?u.stops[1]:"";return typeof e.fillColor[a]=="object"&&t("linearGradient",{id:`gradient-${a}`,x1:"100%",y1:"0%",x2:"0%",y2:"100%"},t("stop",{offset:"0%","stop-color":c}),t("stop",{offset:"100%","stop-color":d}))};return()=>{const{viewBoxWidth:n,strokeWidth:a,circleGap:u,showIndicator:c,fillColor:d,railColor:l,railStyle:s,percentage:f,clsPrefix:b}=e;return t("div",{class:`${b}-progress-content`,role:"none"},t("div",{class:`${b}-progress-graph`,"aria-hidden":!0},t("div",{class:`${b}-progress-graph-circle`},t("svg",{viewBox:`0 0 ${n} ${n}`},t("defs",null,f.map((y,g)=>i(y,g))),f.map((y,g)=>t("g",{key:g},t("path",{class:`${b}-progress-graph-circle-rail`,d:Xe(n/2-a/2*(1+2*g)-u*g,a,n),"stroke-width":a,"stroke-linecap":"round",fill:"none",style:[{strokeDashoffset:0,stroke:l[g]},s[g]]}),t("path",{class:[`${b}-progress-graph-circle-fill`,y===0&&`${b}-progress-graph-circle-fill--empty`],d:Xe(n/2-a/2*(1+2*g)-u*g,a,n),"stroke-width":a,"stroke-linecap":"round",fill:"none",style:{strokeDasharray:r.value[g],strokeDashoffset:0,stroke:typeof d[g]=="object"?`url(#gradient-${g})`:d[g]}})))))),c&&o.default?t("div",null,t("div",{class:`${b}-progress-text`},o.default())):null)}}}),yo=N([m("progress",{display:"inline-block"},[m("progress-icon",`
 color: var(--n-icon-color);
 transition: color .3s var(--n-bezier);
 `),M("line",`
 width: 100%;
 display: block;
 `,[m("progress-content",`
 display: flex;
 align-items: center;
 `,[m("progress-graph",{flex:1})]),m("progress-custom-content",{marginLeft:"14px"}),m("progress-icon",`
 width: 30px;
 padding-left: 14px;
 height: var(--n-icon-size-line);
 line-height: var(--n-icon-size-line);
 font-size: var(--n-icon-size-line);
 `,[M("as-text",`
 color: var(--n-text-color-line-outer);
 text-align: center;
 width: 40px;
 font-size: var(--n-font-size);
 padding-left: 4px;
 transition: color .3s var(--n-bezier);
 `)])]),M("circle, dashboard",{width:"120px"},[m("progress-custom-content",`
 position: absolute;
 left: 50%;
 top: 50%;
 transform: translateX(-50%) translateY(-50%);
 display: flex;
 align-items: center;
 justify-content: center;
 `),m("progress-text",`
 position: absolute;
 left: 50%;
 top: 50%;
 transform: translateX(-50%) translateY(-50%);
 display: flex;
 align-items: center;
 color: inherit;
 font-size: var(--n-font-size-circle);
 color: var(--n-text-color-circle);
 font-weight: var(--n-font-weight-circle);
 transition: color .3s var(--n-bezier);
 white-space: nowrap;
 `),m("progress-icon",`
 position: absolute;
 left: 50%;
 top: 50%;
 transform: translateX(-50%) translateY(-50%);
 display: flex;
 align-items: center;
 color: var(--n-icon-color);
 font-size: var(--n-icon-size-circle);
 `)]),M("multiple-circle",`
 width: 200px;
 color: inherit;
 `,[m("progress-text",`
 font-weight: var(--n-font-weight-circle);
 color: var(--n-text-color-circle);
 position: absolute;
 left: 50%;
 top: 50%;
 transform: translateX(-50%) translateY(-50%);
 display: flex;
 align-items: center;
 justify-content: center;
 transition: color .3s var(--n-bezier);
 `)]),m("progress-content",{position:"relative"}),m("progress-graph",{position:"relative"},[m("progress-graph-circle",[N("svg",{verticalAlign:"bottom"}),m("progress-graph-circle-fill",`
 stroke: var(--n-fill-color);
 transition:
 opacity .3s var(--n-bezier),
 stroke .3s var(--n-bezier),
 stroke-dasharray .3s var(--n-bezier);
 `,[M("empty",{opacity:0})]),m("progress-graph-circle-rail",`
 transition: stroke .3s var(--n-bezier);
 overflow: hidden;
 stroke: var(--n-rail-color);
 `)]),m("progress-graph-line",[M("indicator-inside",[m("progress-graph-line-rail",`
 height: 16px;
 line-height: 16px;
 border-radius: 10px;
 `,[m("progress-graph-line-fill",`
 height: inherit;
 border-radius: 10px;
 `),m("progress-graph-line-indicator",`
 background: #0000;
 white-space: nowrap;
 text-align: right;
 margin-left: 14px;
 margin-right: 14px;
 height: inherit;
 font-size: 12px;
 color: var(--n-text-color-line-inner);
 transition: color .3s var(--n-bezier);
 `)])]),M("indicator-inside-label",`
 height: 16px;
 display: flex;
 align-items: center;
 `,[m("progress-graph-line-rail",`
 flex: 1;
 transition: background-color .3s var(--n-bezier);
 `),m("progress-graph-line-indicator",`
 background: var(--n-fill-color);
 font-size: 12px;
 transform: translateZ(0);
 display: flex;
 vertical-align: middle;
 height: 16px;
 line-height: 16px;
 padding: 0 10px;
 border-radius: 10px;
 position: absolute;
 white-space: nowrap;
 color: var(--n-text-color-line-inner);
 transition:
 right .2s var(--n-bezier),
 color .3s var(--n-bezier),
 background-color .3s var(--n-bezier);
 `)]),m("progress-graph-line-rail",`
 position: relative;
 overflow: hidden;
 height: var(--n-rail-height);
 border-radius: 5px;
 background-color: var(--n-rail-color);
 transition: background-color .3s var(--n-bezier);
 `,[m("progress-graph-line-fill",`
 background: var(--n-fill-color);
 position: relative;
 border-radius: 5px;
 height: inherit;
 width: 100%;
 max-width: 0%;
 transition:
 background-color .3s var(--n-bezier),
 max-width .2s var(--n-bezier);
 `,[M("processing",[N("&::after",`
 content: "";
 background-image: var(--n-line-bg-processing);
 animation: progress-processing-animation 2s var(--n-bezier) infinite;
 `)])])])])])]),N("@keyframes progress-processing-animation",`
 0% {
 position: absolute;
 left: 0;
 top: 0;
 bottom: 0;
 right: 100%;
 opacity: 1;
 }
 66% {
 position: absolute;
 left: 0;
 top: 0;
 bottom: 0;
 right: 0;
 opacity: 0;
 }
 100% {
 position: absolute;
 left: 0;
 top: 0;
 bottom: 0;
 right: 0;
 opacity: 0;
 }
 `)]),Co=Object.assign(Object.assign({},ae.props),{processing:Boolean,type:{type:String,default:"line"},gapDegree:Number,gapOffsetDegree:Number,status:{type:String,default:"default"},railColor:[String,Array],railStyle:[String,Array],color:[String,Array,Object],viewBoxWidth:{type:Number,default:100},strokeWidth:{type:Number,default:7},percentage:[Number,Array],unit:{type:String,default:"%"},showIndicator:{type:Boolean,default:!0},indicatorPosition:{type:String,default:"outside"},indicatorPlacement:{type:String,default:"outside"},indicatorTextColor:String,circleGap:{type:Number,default:1},height:Number,borderRadius:[String,Number],fillBorderRadius:[String,Number],offsetDegree:Number}),Ro=q({name:"Progress",props:Co,setup(e){const o=$(()=>e.indicatorPlacement||e.indicatorPosition),r=$(()=>{if(e.gapDegree||e.gapDegree===0)return e.gapDegree;if(e.type==="dashboard")return 75}),{mergedClsPrefixRef:i,inlineThemeDisabled:n}=ue(e),a=ae("Progress","-progress",yo,_t,e,i),u=$(()=>{const{status:d}=e,{common:{cubicBezierEaseInOut:l},self:{fontSize:s,fontSizeCircle:f,railColor:b,railHeight:y,iconSizeCircle:g,iconSizeLine:C,textColorCircle:j,textColorLineInner:z,textColorLineOuter:k,lineBgProcessing:E,fontWeightCircle:L,[Fe("iconColor",d)]:p,[Fe("fillColor",d)]:R}}=a.value;return{"--n-bezier":l,"--n-fill-color":R,"--n-font-size":s,"--n-font-size-circle":f,"--n-font-weight-circle":L,"--n-icon-color":p,"--n-icon-size-circle":g,"--n-icon-size-line":C,"--n-line-bg-processing":E,"--n-rail-color":b,"--n-rail-height":y,"--n-text-color-circle":j,"--n-text-color-line-inner":z,"--n-text-color-line-outer":k}}),c=n?ze("progress",$(()=>e.status[0]),u,e):void 0;return{mergedClsPrefix:i,mergedIndicatorPlacement:o,gapDeg:r,cssVars:n?void 0:u,themeClass:c==null?void 0:c.themeClass,onRender:c==null?void 0:c.onRender}},render(){const{type:e,cssVars:o,indicatorTextColor:r,showIndicator:i,status:n,railColor:a,railStyle:u,color:c,percentage:d,viewBoxWidth:l,strokeWidth:s,mergedIndicatorPlacement:f,unit:b,borderRadius:y,fillBorderRadius:g,height:C,processing:j,circleGap:z,mergedClsPrefix:k,gapDeg:E,gapOffsetDegree:L,themeClass:p,$slots:R,onRender:S}=this;return S==null||S(),t("div",{class:[p,`${k}-progress`,`${k}-progress--${e}`,`${k}-progress--${n}`],style:o,"aria-valuemax":100,"aria-valuemin":0,"aria-valuenow":d,role:e==="circle"||e==="line"||e==="dashboard"?"progressbar":"none"},e==="circle"||e==="dashboard"?t(mo,{clsPrefix:k,status:n,showIndicator:i,indicatorTextColor:r,railColor:a,fillColor:c,railStyle:u,offsetDegree:this.offsetDegree,percentage:d,viewBoxWidth:l,strokeWidth:s,gapDegree:E===void 0?e==="dashboard"?75:0:E,gapOffsetDegree:L,unit:b},R):e==="line"?t(wo,{clsPrefix:k,status:n,showIndicator:i,indicatorTextColor:r,railColor:a,fillColor:c,railStyle:u,percentage:d,processing:j,indicatorPlacement:f,unit:b,fillBorderRadius:g,railBorderRadius:y,height:C},R):e==="multiple-circle"?t(xo,{clsPrefix:k,strokeWidth:s,railColor:a,fillColor:c,railStyle:u,viewBoxWidth:l,percentage:d,showIndicator:i,circleGap:z},R):null)}}),se=$e("n-upload"),So=N([m("upload","width: 100%;",[M("dragger-inside",[m("upload-trigger",`
 display: block;
 `)]),M("drag-over",[m("upload-dragger",`
 border: var(--n-dragger-border-hover);
 `)])]),m("upload-dragger",`
 cursor: pointer;
 box-sizing: border-box;
 width: 100%;
 text-align: center;
 border-radius: var(--n-border-radius);
 padding: 24px;
 opacity: 1;
 transition:
 opacity .3s var(--n-bezier),
 border-color .3s var(--n-bezier),
 background-color .3s var(--n-bezier);
 background-color: var(--n-dragger-color);
 border: var(--n-dragger-border);
 `,[N("&:hover",`
 border: var(--n-dragger-border-hover);
 `),M("disabled",`
 cursor: not-allowed;
 `)]),m("upload-trigger",`
 display: inline-block;
 box-sizing: border-box;
 opacity: 1;
 transition: opacity .3s var(--n-bezier);
 `,[N("+",[m("upload-file-list","margin-top: 8px;")]),M("disabled",`
 opacity: var(--n-item-disabled-opacity);
 cursor: not-allowed;
 `),M("image-card",`
 width: 96px;
 height: 96px;
 `,[m("base-icon",`
 font-size: 24px;
 `),m("upload-dragger",`
 padding: 0;
 height: 100%;
 width: 100%;
 display: flex;
 align-items: center;
 justify-content: center;
 `)])]),m("upload-file-list",`
 line-height: var(--n-line-height);
 opacity: 1;
 transition: opacity .3s var(--n-bezier);
 `,[N("a, img","outline: none;"),M("disabled",`
 opacity: var(--n-item-disabled-opacity);
 cursor: not-allowed;
 `,[m("upload-file","cursor: not-allowed;")]),M("grid",`
 display: grid;
 grid-template-columns: repeat(auto-fill, 96px);
 grid-gap: 8px;
 margin-top: 0;
 `),m("upload-file",`
 display: block;
 box-sizing: border-box;
 cursor: default;
 padding: 0px 12px 0 6px;
 transition: background-color .3s var(--n-bezier);
 border-radius: var(--n-border-radius);
 `,[Ne(),m("progress",[Ne({foldPadding:!0})]),N("&:hover",`
 background-color: var(--n-item-color-hover);
 `,[m("upload-file-info",[Q("action",`
 opacity: 1;
 `)])]),M("image-type",`
 border-radius: var(--n-border-radius);
 text-decoration: underline;
 text-decoration-color: #0000;
 `,[m("upload-file-info",`
 padding-top: 0px;
 padding-bottom: 0px;
 width: 100%;
 height: 100%;
 display: flex;
 justify-content: space-between;
 align-items: center;
 padding: 6px 0;
 `,[m("progress",`
 padding: 2px 0;
 margin-bottom: 0;
 `),Q("name",`
 padding: 0 8px;
 `),Q("thumbnail",`
 width: 32px;
 height: 32px;
 font-size: 28px;
 display: flex;
 justify-content: center;
 align-items: center;
 `,[N("img",`
 width: 100%;
 `)])])]),M("text-type",[m("progress",`
 box-sizing: border-box;
 padding-bottom: 6px;
 margin-bottom: 6px;
 `)]),M("image-card-type",`
 position: relative;
 width: 96px;
 height: 96px;
 border: var(--n-item-border-image-card);
 border-radius: var(--n-border-radius);
 padding: 0;
 display: flex;
 align-items: center;
 justify-content: center;
 transition: border-color .3s var(--n-bezier), background-color .3s var(--n-bezier);
 border-radius: var(--n-border-radius);
 overflow: hidden;
 `,[m("progress",`
 position: absolute;
 left: 8px;
 bottom: 8px;
 right: 8px;
 width: unset;
 `),m("upload-file-info",`
 padding: 0;
 width: 100%;
 height: 100%;
 `,[Q("thumbnail",`
 width: 100%;
 height: 100%;
 display: flex;
 flex-direction: column;
 align-items: center;
 justify-content: center;
 font-size: 36px;
 `,[N("img",`
 width: 100%;
 `)])]),N("&::before",`
 position: absolute;
 z-index: 1;
 left: 0;
 right: 0;
 top: 0;
 bottom: 0;
 border-radius: inherit;
 opacity: 0;
 transition: opacity .2s var(--n-bezier);
 content: "";
 `),N("&:hover",[N("&::before","opacity: 1;"),m("upload-file-info",[Q("thumbnail","opacity: .12;")])])]),M("error-status",[N("&:hover",`
 background-color: var(--n-item-color-hover-error);
 `),m("upload-file-info",[Q("name","color: var(--n-item-text-color-error);"),Q("thumbnail","color: var(--n-item-text-color-error);")]),M("image-card-type",`
 border: var(--n-item-border-image-card-error);
 `)]),M("with-url",`
 cursor: pointer;
 `,[m("upload-file-info",[Q("name",`
 color: var(--n-item-text-color-success);
 text-decoration-color: var(--n-item-text-color-success);
 `,[N("a",`
 text-decoration: underline;
 `)])])]),m("upload-file-info",`
 position: relative;
 padding-top: 6px;
 padding-bottom: 6px;
 display: flex;
 flex-wrap: nowrap;
 `,[Q("thumbnail",`
 font-size: 18px;
 opacity: 1;
 transition: opacity .2s var(--n-bezier);
 color: var(--n-item-icon-color);
 `,[m("base-icon",`
 margin-right: 2px;
 vertical-align: middle;
 transition: color .3s var(--n-bezier);
 `)]),Q("action",`
 padding-top: inherit;
 padding-bottom: inherit;
 position: absolute;
 right: 0;
 top: 0;
 bottom: 0;
 width: 80px;
 display: flex;
 align-items: center;
 transition: opacity .2s var(--n-bezier);
 justify-content: flex-end;
 opacity: 0;
 `,[m("button",[N("&:not(:last-child)",{marginRight:"4px"}),m("base-icon",[N("svg",[Ht()])])]),M("image-type",`
 position: relative;
 max-width: 80px;
 width: auto;
 `),M("image-card-type",`
 z-index: 2;
 position: absolute;
 width: 100%;
 height: 100%;
 left: 0;
 right: 0;
 bottom: 0;
 top: 0;
 display: flex;
 justify-content: center;
 align-items: center;
 `)]),Q("name",`
 color: var(--n-item-text-color);
 flex: 1;
 display: flex;
 justify-content: center;
 text-overflow: ellipsis;
 overflow: hidden;
 flex-direction: column;
 text-decoration-color: #0000;
 font-size: var(--n-font-size);
 transition:
 color .3s var(--n-bezier),
 text-decoration-color .3s var(--n-bezier); 
 `,[N("a",`
 color: inherit;
 text-decoration: underline;
 `)])])])]),m("upload-file-input",`
 display: none;
 width: 0;
 height: 0;
 opacity: 0;
 `)]),Pt="__UPLOAD_DRAGGER__",ko=q({name:"UploadDragger",[Pt]:!0,setup(e,{slots:o}){const r=oe(se,null);return r||me("upload-dragger","`n-upload-dragger` must be placed inside `n-upload`."),()=>{const{mergedClsPrefixRef:{value:i},mergedDisabledRef:{value:n},maxReachedRef:{value:a}}=r;return t("div",{class:[`${i}-upload-dragger`,(n||a)&&`${i}-upload-dragger--disabled`]},o)}}}),Po=t("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 28 28"},t("g",{fill:"none"},t("path",{d:"M21.75 3A3.25 3.25 0 0 1 25 6.25v15.5A3.25 3.25 0 0 1 21.75 25H6.25A3.25 3.25 0 0 1 3 21.75V6.25A3.25 3.25 0 0 1 6.25 3h15.5zm.583 20.4l-7.807-7.68a.75.75 0 0 0-.968-.07l-.084.07l-7.808 7.68c.183.065.38.1.584.1h15.5c.204 0 .4-.035.583-.1l-7.807-7.68l7.807 7.68zM21.75 4.5H6.25A1.75 1.75 0 0 0 4.5 6.25v15.5c0 .208.036.408.103.593l7.82-7.692a2.25 2.25 0 0 1 3.026-.117l.129.117l7.82 7.692c.066-.185.102-.385.102-.593V6.25a1.75 1.75 0 0 0-1.75-1.75zm-3.25 3a2.5 2.5 0 1 1 0 5a2.5 2.5 0 0 1 0-5zm0 1.5a1 1 0 1 0 0 2a1 1 0 0 0 0-2z",fill:"currentColor"}))),Lo=t("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 28 28"},t("g",{fill:"none"},t("path",{d:"M6.4 2A2.4 2.4 0 0 0 4 4.4v19.2A2.4 2.4 0 0 0 6.4 26h15.2a2.4 2.4 0 0 0 2.4-2.4V11.578c0-.729-.29-1.428-.805-1.944l-6.931-6.931A2.4 2.4 0 0 0 14.567 2H6.4zm-.9 2.4a.9.9 0 0 1 .9-.9H14V10a2 2 0 0 0 2 2h6.5v11.6a.9.9 0 0 1-.9.9H6.4a.9.9 0 0 1-.9-.9V4.4zm16.44 6.1H16a.5.5 0 0 1-.5-.5V4.06l6.44 6.44z",fill:"currentColor"}))),Oo=q({name:"UploadProgress",props:{show:Boolean,percentage:{type:Number,required:!0},status:{type:String,required:!0}},setup(){return{mergedTheme:oe(se).mergedThemeRef}},render(){return t(at,null,{default:()=>this.show?t(Ro,{type:"line",showIndicator:!1,percentage:this.percentage,status:this.status,height:2,theme:this.mergedTheme.peers.Progress,themeOverrides:this.mergedTheme.peerOverrides.Progress}):null})}});var Te=function(e,o,r,i){function n(a){return a instanceof r?a:new r(function(u){u(a)})}return new(r||(r=Promise))(function(a,u){function c(s){try{l(i.next(s))}catch(f){u(f)}}function d(s){try{l(i.throw(s))}catch(f){u(f)}}function l(s){s.done?a(s.value):n(s.value).then(c,d)}l((i=i.apply(e,o||[])).next())})};function Lt(e){return e.includes("image/")}function Ge(e=""){const o=e.split("/"),i=o[o.length-1].split(/#|\?/)[0];return(/\.[^./\\]*$/.exec(i)||[""])[0]}const Ye=/(webp|svg|png|gif|jpg|jpeg|jfif|bmp|dpg|ico)$/i,Ot=e=>{if(e.type)return Lt(e.type);const o=Ge(e.name||"");if(Ye.test(o))return!0;const r=e.thumbnailUrl||e.url||"",i=Ge(r);return!!(/^data:image\//.test(r)||Ye.test(i))};function To(e){return Te(this,void 0,void 0,function*(){return yield new Promise(o=>{if(!e.type||!Lt(e.type)){o("");return}o(window.URL.createObjectURL(e))})})}const $o=tt&&window.FileReader&&window.File;function zo(e){return e.isDirectory}function Io(e){return e.isFile}function Bo(e,o){return Te(this,void 0,void 0,function*(){const r=[];function i(n){return Te(this,void 0,void 0,function*(){for(const a of n)if(a){if(o&&zo(a)){const u=a.createReader();let c=[],d;try{do d=yield new Promise((l,s)=>{u.readEntries(l,s)}),c=c.concat(d);while(d.length>0)}catch(l){Ee("upload","error happens when handling directory upload",l)}yield i(c)}else if(Io(a))try{const u=yield new Promise((c,d)=>{a.file(c,d)});r.push({file:u,entry:a,source:"dnd"})}catch(u){Ee("upload","error happens when handling file upload",u)}}})}return yield i(e),r})}function de(e){const{id:o,name:r,percentage:i,status:n,url:a,file:u,thumbnailUrl:c,type:d,fullPath:l,batchId:s}=e;return{id:o,name:r,percentage:i!=null?i:null,status:n,url:a!=null?a:null,file:u!=null?u:null,thumbnailUrl:c!=null?c:null,type:d!=null?d:null,fullPath:l!=null?l:null,batchId:s!=null?s:null}}function Do(e,o,r){return e=e.toLowerCase(),o=o.toLocaleLowerCase(),r=r.toLocaleLowerCase(),r.split(",").map(n=>n.trim()).filter(Boolean).some(n=>{if(n.startsWith(".")){if(e.endsWith(n))return!0}else if(n.includes("/")){const[a,u]=o.split("/"),[c,d]=n.split("/");if((c==="*"||a&&c&&c===a)&&(d==="*"||u&&d&&d===u))return!0}else return!0;return!1})}var Ke=function(e,o,r,i){function n(a){return a instanceof r?a:new r(function(u){u(a)})}return new(r||(r=Promise))(function(a,u){function c(s){try{l(i.next(s))}catch(f){u(f)}}function d(s){try{l(i.throw(s))}catch(f){u(f)}}function l(s){s.done?a(s.value):n(s.value).then(c,d)}l((i=i.apply(e,o||[])).next())})};const pe={paddingMedium:"0 3px",heightMedium:"24px",iconSizeMedium:"18px"},Mo=q({name:"UploadFile",props:{clsPrefix:{type:String,required:!0},file:{type:Object,required:!0},listType:{type:String,required:!0},index:{type:Number,required:!0}},setup(e){const o=oe(se),r=W(null),i=W(""),n=$(()=>{const{file:p}=e;return p.status==="finished"?"success":p.status==="error"?"error":"info"}),a=$(()=>{const{file:p}=e;if(p.status==="error")return"error"}),u=$(()=>{const{file:p}=e;return p.status==="uploading"}),c=$(()=>{if(!o.showCancelButtonRef.value)return!1;const{file:p}=e;return["uploading","pending","error"].includes(p.status)}),d=$(()=>{if(!o.showRemoveButtonRef.value)return!1;const{file:p}=e;return["finished"].includes(p.status)}),l=$(()=>{if(!o.showDownloadButtonRef.value)return!1;const{file:p}=e;return["finished"].includes(p.status)}),s=$(()=>{if(!o.showRetryButtonRef.value)return!1;const{file:p}=e;return["error"].includes(p.status)}),f=Vt(()=>i.value||e.file.thumbnailUrl||e.file.url),b=$(()=>{if(!o.showPreviewButtonRef.value)return!1;const{file:{status:p},listType:R}=e;return["finished"].includes(p)&&f.value&&R==="image-card"});function y(){return Ke(this,void 0,void 0,function*(){const p=o.onRetryRef.value;p&&(yield p({file:e.file}))===!1||o.submit(e.file.id)})}function g(p){p.preventDefault();const{file:R}=e;["finished","pending","error"].includes(R.status)?j(R):["uploading"].includes(R.status)?k(R):Zt("upload","The button clicked type is unknown.")}function C(p){p.preventDefault(),z(e.file)}function j(p){const{xhrMap:R,doChange:S,onRemoveRef:{value:U},mergedFileListRef:{value:w}}=o;Promise.resolve(U?U({file:Object.assign({},p),fileList:w,index:e.index}):!0).then(I=>{if(I===!1)return;const P=Object.assign({},p,{status:"removed"});R.delete(p.id),S(P,void 0,{remove:!0})})}function z(p){const{onDownloadRef:{value:R},customDownloadRef:{value:S}}=o;Promise.resolve(R?R(Object.assign({},p)):!0).then(U=>{U!==!1&&(S?S(Object.assign({},p)):Qe(p.url,p.name))})}function k(p){const{xhrMap:R}=o,S=R.get(p.id);S==null||S.abort(),j(Object.assign({},p))}function E(p){const{onPreviewRef:{value:R}}=o;if(R)R(e.file,{event:p});else if(e.listType==="image-card"){const{value:S}=r;if(!S)return;S.click()}}const L=()=>Ke(this,void 0,void 0,function*(){const{listType:p}=e;p!=="image"&&p!=="image-card"||o.shouldUseThumbnailUrlRef.value(e.file)&&(i.value=yield o.getFileThumbnailUrlResolver(e.file))});return Oe(()=>{L()}),{mergedTheme:o.mergedThemeRef,progressStatus:n,buttonType:a,showProgress:u,disabled:o.mergedDisabledRef,showCancelButton:c,showRemoveButton:d,showDownloadButton:l,showRetryButton:s,showPreviewButton:b,mergedThumbnailUrl:f,shouldUseThumbnailUrl:o.shouldUseThumbnailUrlRef,renderIcon:o.renderIconRef,imageRef:r,handleRemoveOrCancelClick:g,handleDownloadClick:C,handleRetryClick:y,handlePreviewClick:E}},render(){const{clsPrefix:e,mergedTheme:o,listType:r,file:i,renderIcon:n}=this;let a;const u=r==="image";u||r==="image-card"?a=!this.shouldUseThumbnailUrl(i)||!this.mergedThumbnailUrl?t("span",{class:`${e}-upload-file-info__thumbnail`},n?n(i):Ot(i)?t(A,{clsPrefix:e},{default:Po}):t(A,{clsPrefix:e},{default:Lo})):t("a",{rel:"noopener noreferer",target:"_blank",href:i.url||void 0,class:`${e}-upload-file-info__thumbnail`,onClick:this.handlePreviewClick},r==="image-card"?t(po,{src:this.mergedThumbnailUrl||void 0,previewSrc:i.url||void 0,alt:i.name,ref:"imageRef"}):t("img",{src:this.mergedThumbnailUrl||void 0,alt:i.name})):a=t("span",{class:`${e}-upload-file-info__thumbnail`},n?n(i):t(A,{clsPrefix:e},{default:()=>t(Gr,null)}));const d=t(Oo,{show:this.showProgress,percentage:i.percentage||0,status:this.progressStatus}),l=r==="text"||r==="image";return t("div",{class:[`${e}-upload-file`,`${e}-upload-file--${this.progressStatus}-status`,i.url&&i.status!=="error"&&r!=="image-card"&&`${e}-upload-file--with-url`,`${e}-upload-file--${r}-type`]},t("div",{class:`${e}-upload-file-info`},a,t("div",{class:`${e}-upload-file-info__name`},l&&(i.url&&i.status!=="error"?t("a",{rel:"noopener noreferer",target:"_blank",href:i.url||void 0,onClick:this.handlePreviewClick},i.name):t("span",{onClick:this.handlePreviewClick},i.name)),u&&d),t("div",{class:[`${e}-upload-file-info__action`,`${e}-upload-file-info__action--${r}-type`]},this.showPreviewButton?t(ge,{key:"preview",quaternary:!0,type:this.buttonType,onClick:this.handlePreviewClick,theme:o.peers.Button,themeOverrides:o.peerOverrides.Button,builtinThemeOverrides:pe},{icon:()=>t(A,{clsPrefix:e},{default:()=>t(rr,null)})}):null,(this.showRemoveButton||this.showCancelButton)&&!this.disabled&&t(ge,{key:"cancelOrTrash",theme:o.peers.Button,themeOverrides:o.peerOverrides.Button,quaternary:!0,builtinThemeOverrides:pe,type:this.buttonType,onClick:this.handleRemoveOrCancelClick},{icon:()=>t(Wt,null,{default:()=>this.showRemoveButton?t(A,{clsPrefix:e,key:"trash"},{default:()=>t(to,null)}):t(A,{clsPrefix:e,key:"cancel"},{default:()=>t(Yr,null)})})}),this.showRetryButton&&!this.disabled&&t(ge,{key:"retry",quaternary:!0,type:this.buttonType,onClick:this.handleRetryClick,theme:o.peers.Button,themeOverrides:o.peerOverrides.Button,builtinThemeOverrides:pe},{icon:()=>t(A,{clsPrefix:e},{default:()=>t(Jr,null)})}),this.showDownloadButton?t(ge,{key:"download",quaternary:!0,type:this.buttonType,onClick:this.handleDownloadClick,theme:o.peers.Button,themeOverrides:o.peerOverrides.Button,builtinThemeOverrides:pe},{icon:()=>t(A,{clsPrefix:e},{default:()=>t(Ct,null)})}):null)),!u&&d)}}),Tt=q({name:"UploadTrigger",props:{abstract:Boolean},slots:Object,setup(e,{slots:o}){const r=oe(se,null);r||me("upload-trigger","`n-upload-trigger` must be placed inside `n-upload`.");const{mergedClsPrefixRef:i,mergedDisabledRef:n,maxReachedRef:a,listTypeRef:u,dragOverRef:c,openOpenFileDialog:d,draggerInsideRef:l,handleFileAddition:s,mergedDirectoryDndRef:f,triggerClassRef:b,triggerStyleRef:y}=r,g=$(()=>u.value==="image-card");function C(){n.value||a.value||d()}function j(L){L.preventDefault(),c.value=!0}function z(L){L.preventDefault(),c.value=!0}function k(L){L.preventDefault(),c.value=!1}function E(L){var p;if(L.preventDefault(),!l.value||n.value||a.value){c.value=!1;return}const R=(p=L.dataTransfer)===null||p===void 0?void 0:p.items;R!=null&&R.length?Bo(Array.from(R).map(S=>S.webkitGetAsEntry()),f.value).then(S=>{s(S)}).finally(()=>{c.value=!1}):c.value=!1}return()=>{var L;const{value:p}=i;return e.abstract?(L=o.default)===null||L===void 0?void 0:L.call(o,{handleClick:C,handleDrop:E,handleDragOver:j,handleDragEnter:z,handleDragLeave:k}):t("div",{class:[`${p}-upload-trigger`,(n.value||a.value)&&`${p}-upload-trigger--disabled`,g.value&&`${p}-upload-trigger--image-card`,b.value],style:y.value,onClick:C,onDrop:E,onDragover:j,onDragenter:z,onDragleave:k},g.value?t(ko,null,{default:()=>rt(o.default,()=>[t(A,{clsPrefix:p},{default:()=>t(or,null)})])}):o)}}}),jo=q({name:"UploadFileList",setup(e,{slots:o}){const r=oe(se,null);r||me("upload-file-list","`n-upload-file-list` must be placed inside `n-upload`.");const{abstractRef:i,mergedClsPrefixRef:n,listTypeRef:a,mergedFileListRef:u,fileListClassRef:c,fileListStyleRef:d,cssVarsRef:l,themeClassRef:s,maxReachedRef:f,showTriggerRef:b,imageGroupPropsRef:y}=r,g=$(()=>a.value==="image-card"),C=()=>u.value.map((z,k)=>t(Mo,{clsPrefix:n.value,key:z.id,file:z,index:k,listType:a.value})),j=()=>g.value?t(go,Object.assign({},y.value),{default:C}):t(at,{group:!0},{default:C});return()=>{const{value:z}=n,{value:k}=i;return t("div",{class:[`${z}-upload-file-list`,g.value&&`${z}-upload-file-list--grid`,k?s==null?void 0:s.value:void 0,c.value],style:[k&&l?l.value:"",d.value]},j(),b.value&&!f.value&&g.value&&t(Tt,null,o))}}});var Je=function(e,o,r,i){function n(a){return a instanceof r?a:new r(function(u){u(a)})}return new(r||(r=Promise))(function(a,u){function c(s){try{l(i.next(s))}catch(f){u(f)}}function d(s){try{l(i.throw(s))}catch(f){u(f)}}function l(s){s.done?a(s.value):n(s.value).then(c,d)}l((i=i.apply(e,o||[])).next())})};function Uo(e,o,r){const{doChange:i,xhrMap:n}=e;let a=0;function u(d){var l;let s=Object.assign({},o,{status:"error",percentage:a});n.delete(o.id),s=de(((l=e.onError)===null||l===void 0?void 0:l.call(e,{file:s,event:d}))||s),i(s,d)}function c(d){var l;if(e.isErrorState){if(e.isErrorState(r)){u(d);return}}else if(r.status<200||r.status>=300){u(d);return}let s=Object.assign({},o,{status:"finished",percentage:a});n.delete(o.id),s=de(((l=e.onFinish)===null||l===void 0?void 0:l.call(e,{file:s,event:d}))||s),i(s,d)}return{handleXHRLoad:c,handleXHRError:u,handleXHRAbort(d){const l=Object.assign({},o,{status:"removed",file:null,percentage:a});n.delete(o.id),i(l,d)},handleXHRProgress(d){const l=Object.assign({},o,{status:"uploading"});if(d.lengthComputable){const s=Math.ceil(d.loaded/d.total*100);l.percentage=s,a=s}i(l,d)}}}function Fo(e){const{inst:o,file:r,data:i,headers:n,withCredentials:a,action:u,customRequest:c}=e,{doChange:d}=e.inst;let l=0;c({file:r,data:i,headers:n,withCredentials:a,action:u,onProgress(s){const f=Object.assign({},r,{status:"uploading"}),b=s.percent;f.percentage=b,l=b,d(f)},onFinish(){var s;let f=Object.assign({},r,{status:"finished",percentage:l});f=de(((s=o.onFinish)===null||s===void 0?void 0:s.call(o,{file:f}))||f),d(f)},onError(){var s;let f=Object.assign({},r,{status:"error",percentage:l});f=de(((s=o.onError)===null||s===void 0?void 0:s.call(o,{file:f}))||f),d(f)}})}function No(e,o,r){const i=Uo(e,o,r);r.onabort=i.handleXHRAbort,r.onerror=i.handleXHRError,r.onload=i.handleXHRLoad,r.upload&&(r.upload.onprogress=i.handleXHRProgress)}function $t(e,o){return typeof e=="function"?e({file:o}):e||{}}function Eo(e,o,r){const i=$t(o,r);i&&Object.keys(i).forEach(n=>{e.setRequestHeader(n,i[n])})}function Ao(e,o,r){const i=$t(o,r);i&&Object.keys(i).forEach(n=>{e.append(n,i[n])})}function _o(e,o,r,{method:i,action:n,withCredentials:a,responseType:u,headers:c,data:d}){const l=new XMLHttpRequest;l.responseType=u,e.xhrMap.set(r.id,l),l.withCredentials=a;const s=new FormData;if(Ao(s,d,r),r.file!==null&&s.append(o,r.file),No(e,r,l),n!==void 0){l.open(i.toUpperCase(),n),Eo(l,c,r),l.send(s);const f=Object.assign({},r,{status:"uploading"});e.doChange(f)}}const Ho=Object.assign(Object.assign({},ae.props),{name:{type:String,default:"file"},accept:String,action:String,customRequest:Function,directory:Boolean,directoryDnd:{type:Boolean,default:void 0},method:{type:String,default:"POST"},multiple:Boolean,showFileList:{type:Boolean,default:!0},data:[Object,Function],headers:[Object,Function],withCredentials:Boolean,responseType:{type:String,default:""},disabled:{type:Boolean,default:void 0},onChange:Function,onRemove:Function,onFinish:Function,onError:Function,onRetry:Function,onBeforeUpload:Function,isErrorState:Function,onDownload:Function,customDownload:Function,defaultUpload:{type:Boolean,default:!0},fileList:Array,"onUpdate:fileList":[Function,Array],onUpdateFileList:[Function,Array],fileListClass:String,fileListStyle:[String,Object],defaultFileList:{type:Array,default:()=>[]},showCancelButton:{type:Boolean,default:!0},showRemoveButton:{type:Boolean,default:!0},showDownloadButton:Boolean,showRetryButton:{type:Boolean,default:!0},showPreviewButton:{type:Boolean,default:!0},listType:{type:String,default:"text"},onPreview:Function,shouldUseThumbnailUrl:{type:Function,default:e=>$o?Ot(e):!1},createThumbnailUrl:Function,abstract:Boolean,max:Number,showTrigger:{type:Boolean,default:!0},imageGroupProps:Object,inputProps:Object,triggerClass:String,triggerStyle:[String,Object],renderIcon:Function}),Qo=q({name:"Upload",props:Ho,setup(e){e.abstract&&e.listType==="image-card"&&me("upload","when the list-type is image-card, abstract is not supported.");const{mergedClsPrefixRef:o,inlineThemeDisabled:r}=ue(e),i=ae("Upload","-upload",So,qt,e,o),n=Xt(e),a=W(e.defaultFileList),u=T(e,"fileList"),c=W(null),d={value:!1},l=W(!1),s=new Map,f=ir(u,a),b=$(()=>f.value.map(de)),y=$(()=>{const{max:w}=e;return w!==void 0?b.value.length>=w:!1});function g(){var w;(w=c.value)===null||w===void 0||w.click()}function C(w){const I=w.target;E(I.files?Array.from(I.files).map(P=>({file:P,entry:null,source:"input"})):null,w),I.value=""}function j(w){const{"onUpdate:fileList":I,onUpdateFileList:P}=e;I&&Ae(I,w),P&&Ae(P,w),a.value=w}const z=$(()=>e.multiple||e.directory),k=(w,I,P={append:!1,remove:!1})=>{const{append:O,remove:_}=P,V=Array.from(b.value),H=V.findIndex(F=>F.id===w.id);if(O||_||~H){O?V.push(w):_?V.splice(H,1):V.splice(H,1,w);const{onChange:F}=e;F&&F({file:w,fileList:V,event:I}),j(V)}};function E(w,I){if(!w||w.length===0)return;const{onBeforeUpload:P}=e;w=z.value?w:[w[0]];const{max:O,accept:_}=e;w=w.filter(({file:H,source:F})=>F==="dnd"&&(_!=null&&_.trim())?Do(H.name,H.type,_):!0),O&&(w=w.slice(0,O-b.value.length));const V=Le();Promise.all(w.map(H=>Je(this,[H],void 0,function*({file:F,entry:G}){var X;const ee={id:Le(),batchId:V,name:F.name,status:"pending",percentage:0,file:F,url:null,type:F.type,thumbnailUrl:null,fullPath:(X=G==null?void 0:G.fullPath)!==null&&X!==void 0?X:`/${F.webkitRelativePath||F.name}`};return!P||(yield P({file:ee,fileList:b.value}))!==!1?ee:null}))).then(H=>Je(this,void 0,void 0,function*(){let F=Promise.resolve();H.forEach(G=>{F=F.then(Qt).then(()=>{G&&k(G,I,{append:!0})})}),yield F})).then(()=>{e.defaultUpload&&L()})}function L(w){const{method:I,action:P,withCredentials:O,headers:_,data:V,name:H}=e,F=w!==void 0?b.value.filter(X=>X.id===w):b.value,G=w!==void 0;F.forEach(X=>{const{status:ee}=X;(ee==="pending"||ee==="error"&&G)&&(e.customRequest?Fo({inst:{doChange:k,xhrMap:s,onFinish:e.onFinish,onError:e.onError},file:X,action:P,withCredentials:O,headers:_,data:V,customRequest:e.customRequest}):_o({doChange:k,xhrMap:s,onFinish:e.onFinish,onError:e.onError,isErrorState:e.isErrorState},H,X,{method:I,action:P,withCredentials:O,responseType:e.responseType,headers:_,data:V}))})}function p(w){var I;if(w.thumbnailUrl)return w.thumbnailUrl;const{createThumbnailUrl:P}=e;return P?(I=P(w.file,w))!==null&&I!==void 0?I:w.url||"":w.url?w.url:w.file?To(w.file):""}const R=$(()=>{const{common:{cubicBezierEaseInOut:w},self:{draggerColor:I,draggerBorder:P,draggerBorderHover:O,itemColorHover:_,itemColorHoverError:V,itemTextColorError:H,itemTextColorSuccess:F,itemTextColor:G,itemIconColor:X,itemDisabledOpacity:ee,lineHeight:be,borderRadius:ce,fontSize:we,itemBorderImageCardError:xe,itemBorderImageCard:ye}}=i.value;return{"--n-bezier":w,"--n-border-radius":ce,"--n-dragger-border":P,"--n-dragger-border-hover":O,"--n-dragger-color":I,"--n-font-size":we,"--n-item-color-hover":_,"--n-item-color-hover-error":V,"--n-item-disabled-opacity":ee,"--n-item-icon-color":X,"--n-item-text-color":G,"--n-item-text-color-error":H,"--n-item-text-color-success":F,"--n-line-height":be,"--n-item-border-image-card-error":xe,"--n-item-border-image-card":ye}}),S=r?ze("upload",void 0,R,e):void 0;Ie(se,{mergedClsPrefixRef:o,mergedThemeRef:i,showCancelButtonRef:T(e,"showCancelButton"),showDownloadButtonRef:T(e,"showDownloadButton"),showRemoveButtonRef:T(e,"showRemoveButton"),showRetryButtonRef:T(e,"showRetryButton"),onRemoveRef:T(e,"onRemove"),onDownloadRef:T(e,"onDownload"),customDownloadRef:T(e,"customDownload"),mergedFileListRef:b,triggerClassRef:T(e,"triggerClass"),triggerStyleRef:T(e,"triggerStyle"),shouldUseThumbnailUrlRef:T(e,"shouldUseThumbnailUrl"),renderIconRef:T(e,"renderIcon"),xhrMap:s,submit:L,doChange:k,showPreviewButtonRef:T(e,"showPreviewButton"),onPreviewRef:T(e,"onPreview"),getFileThumbnailUrlResolver:p,listTypeRef:T(e,"listType"),dragOverRef:l,openOpenFileDialog:g,draggerInsideRef:d,handleFileAddition:E,mergedDisabledRef:n.mergedDisabledRef,maxReachedRef:y,fileListClassRef:T(e,"fileListClass"),fileListStyleRef:T(e,"fileListStyle"),abstractRef:T(e,"abstract"),acceptRef:T(e,"accept"),cssVarsRef:r?void 0:R,themeClassRef:S==null?void 0:S.themeClass,onRender:S==null?void 0:S.onRender,showTriggerRef:T(e,"showTrigger"),imageGroupPropsRef:T(e,"imageGroupProps"),mergedDirectoryDndRef:$(()=>{var w;return(w=e.directoryDnd)!==null&&w!==void 0?w:e.directory}),onRetryRef:T(e,"onRetry")});const U={clear:()=>{a.value=[]},submit:L,openOpenFileDialog:g};return Object.assign({mergedClsPrefix:o,draggerInsideRef:d,inputElRef:c,mergedTheme:i,dragOver:l,mergedMultiple:z,cssVars:r?void 0:R,themeClass:S==null?void 0:S.themeClass,onRender:S==null?void 0:S.onRender,handleFileInputChange:C},U)},render(){var e,o;const{draggerInsideRef:r,mergedClsPrefix:i,$slots:n,directory:a,onRender:u}=this;if(n.default&&!this.abstract){const d=n.default()[0];!((e=d==null?void 0:d.type)===null||e===void 0)&&e[Pt]&&(r.value=!0)}const c=t("input",Object.assign({},this.inputProps,{ref:"inputElRef",type:"file",class:`${i}-upload-file-input`,accept:this.accept,multiple:this.mergedMultiple,onChange:this.handleFileInputChange,webkitdirectory:a||void 0,directory:a||void 0}));return this.abstract?t(ve,null,(o=n.default)===null||o===void 0?void 0:o.call(n),t(Jt,{to:"body"},c)):(u==null||u(),t("div",{class:[`${i}-upload`,r.value&&`${i}-upload--dragger-inside`,this.dragOver&&`${i}-upload--drag-over`,this.themeClass],style:this.cssVars},c,this.showTrigger&&this.listType!=="image-card"&&t(Tt,null,n),this.showFileList&&t(jo,null,n)))}});export{Qo as NUpload,ko as NUploadDragger,jo as NUploadFileList,Tt as NUploadTrigger,ri as uploadDownload,Ho as uploadProps};
