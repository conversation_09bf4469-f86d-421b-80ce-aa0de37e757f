<?php
/**
 * 鲸犀商城 - 微信公众号小程序商城系统!
 * Copyright © 2020 鲸犀网络科技有限公司 保留所有权利
 * =========================================================
 * 版本：供应链版本
 * 授权主体：无限制版本
 * 授权域名：@@@
     
 * ----------------------------------------------
 * 您只能在商业授权范围内使用，不可二次转售、分发、分享、传播
 * 任何企业和个人不得对代码以任何目的任何形式的再发布
 * =========================================================
 */

// +----------------------------------------------------------------------
// | 舌诊设置管理
// +----------------------------------------------------------------------
namespace app\controller;
use think\facade\View;
use think\facade\Db;

class SheZhenSet extends Common
{
    protected $bid = 0; // 添加bid属性定义
    
    public function initialize(){
        parent::initialize();
        // 2025-01-13 16:30:00,200-INFO-[SheZhenSet][initialize_001] 初始化舌诊设置控制器 - 增加显示设置
        $this->bid = session('ADMIN_BID') ?: 0; // 确保bid属性正确设置
    }

    // 2025-01-13 16:30:00,201-INFO-[SheZhenSet][index_001] 舌诊设置首页 - 增加显示设置
    public function index(){
        // 记录操作日志
        \app\common\System::plog('查看舌诊设置');
        
        // 2025-01-13 16:30:00,202-INFO-[SheZhenSet][index_002] 获取舌诊设置信息 - 包含显示设置
        $set = Db::name('shezhen_set')->where('aid', $this->aid)->where('bid', $this->bid)->find();
        if(!$set){
            // 2025-01-17 创建默认设置 - 包含面诊和综合诊疗设置
            $defaultSet = [
                'aid' => $this->aid,
                'bid' => $this->bid,
                'is_open' => 0,
                'single_price' => 10.00,
                'free_level' => '',
                'daily_free_count' => 1,
                // 2025-01-17 新增面诊默认配置
                'face_diagnosis_enable' => 1,
                'face_price' => 12.90,
                'face_free_level' => '',
                'face_daily_free_count' => 1,
                // 2025-01-17 新增综合诊疗默认配置
                'comprehensive_diagnosis_enable' => 1,
                'comprehensive_price' => 19.90,
                'comprehensive_free_level' => '',
                'comprehensive_daily_free_count' => 1,
                'description' => '通过AI智能分析舌头图片，为您提供专业的中医体质分析和健康建议',
                'aliyun_access_key' => '',
                'aliyun_secret_key' => '',
                'aliyun_endpoint' => 'https://imagerecog.cn-shanghai.aliyuncs.com',
                'aliyun_app_code' => '',
                'show_score' => 1,
                'show_score_value' => 1,
                'show_symptoms' => 1,
                'show_tongue_analysis' => 1,
                'show_care_advice' => 1,
                'show_product_recommend' => 1,
                'is_recommend_open' => 1,
                'recommend_title' => '根据您的舌诊结果，为您推荐以下产品',
                'recommend_count' => 3,
                'createtime' => time(),
                'updatetime' => time()
            ];
            Db::name('shezhen_set')->insert($defaultSet);
            $set = $defaultSet;
        }

        // 2025-01-13 16:30:00,204-INFO-[SheZhenSet][index_004] 获取会员等级列表
        // 确保包含默认的普通会员等级
        $levelList = Db::name('member_level')->where('aid', $this->aid)->field('id,name,isdefault,sort')->order('isdefault desc,sort desc,id')->select()->toArray();
        
        // 2025-01-27 16:30:00,204-INFO-[SheZhenSet][index_004a] 确保默认会员等级存在
        $hasDefaultLevel = false;
        foreach($levelList as $level) {
            if($level['isdefault'] == 1) {
                $hasDefaultLevel = true;
                break;
            }
        }
        
        // 如果不存在默认等级，创建一个
        if(!$hasDefaultLevel) {
            $defaultLevelId = Db::name('member_level')->insertGetId([
                'aid' => $this->aid,
                'isdefault' => 1,
                'name' => '普通会员',
                'icon' => '/static/imgsrc/level_1.png',
                'createtime' => time(),
                'sort' => 0
            ]);
            
            // 重新获取等级列表
            $levelList = Db::name('member_level')->where('aid', $this->aid)->field('id,name,isdefault,sort')->order('isdefault desc,sort desc,id')->select()->toArray();
            
            \app\common\System::plog('自动创建默认会员等级：' . $defaultLevelId);
        }
        
        // 2025-01-13 16:30:00,205-DEBUG-[SheZhenSet][index_005] 会员等级数据调试
        error_log('2025-01-13 16:30:00,205-DEBUG-[SheZhenSet][index_005] aid=' . $this->aid . ', 会员等级数量=' . count($levelList) . ', 数据=' . json_encode($levelList, JSON_UNESCAPED_UNICODE));
        
        // 2025-01-17 处理免费等级数据格式
        $freeLevelArray = [];
        if(!empty($set['free_level'])){
            $freeLevelArray = explode(',', $set['free_level']);
        }

        $faceFreeLevelArray = [];
        if(!empty($set['face_free_level'])){
            $faceFreeLevelArray = explode(',', $set['face_free_level']);
        }

        $comprehensiveFreeLevelArray = [];
        if(!empty($set['comprehensive_free_level'])){
            $comprehensiveFreeLevelArray = explode(',', $set['comprehensive_free_level']);
        }
        
        // 2025-01-13 16:30:00,207-INFO-[SheZhenSet][index_007] 获取当前舌诊次数统计
        $currentTime = time();
        $todayStart = strtotime(date('Y-m-d', $currentTime));
        $monthStart = strtotime(date('Y-m-01', $currentTime));
        
        $where = [
            ['aid', '=', $this->aid],
            ['bid', '=', $this->bid],
            ['status', '=', 1]
        ];
        
        // 今日统计
        $todayCount = Db::name('shezhen_record')->where($where)->where('createtime', '>=', $todayStart)->count();
        $todayIncome = Db::name('shezhen_record')->where($where)->where('createtime', '>=', $todayStart)->sum('price');
        
        // 本月统计  
        $monthCount = Db::name('shezhen_record')->where($where)->where('createtime', '>=', $monthStart)->count();
        $monthIncome = Db::name('shezhen_record')->where($where)->where('createtime', '>=', $monthStart)->sum('price');
        
        // 总统计
        $totalCount = Db::name('shezhen_record')->where($where)->count();
        $totalIncome = Db::name('shezhen_record')->where($where)->sum('price');
        
        // 免费和付费次数统计
        $freeCount = Db::name('shezhen_record')->where($where)->where('is_free', 1)->count();
        $paidCount = $totalCount - $freeCount;
        
        // 2025-01-13 16:30:00,208-INFO-[SheZhenSet][index_008] 获取当前平台面诊次数余额
        $adminInfo = Db::name('admin')->where('id', $this->aid)->field('mianzhen_num')->find();
        $currentMianzhenNum = $adminInfo ? intval($adminInfo['mianzhen_num']) : 0;
        \app\common\System::plog('获取当前平台面诊次数余额：' . $currentMianzhenNum);
        
        $statistics = [
            'today_count' => $todayCount,
            'today_income' => round($todayIncome, 2),
            'month_count' => $monthCount, 
            'month_income' => round($monthIncome, 2),
            'total_count' => $totalCount,
            'total_income' => round($totalIncome, 2),
            'free_count' => $freeCount,
            'paid_count' => $paidCount,
            'current_mianzhen_num' => $currentMianzhenNum  // 添加当前平台面诊次数
        ];
        
        View::assign('set', $set);
        View::assign('levelList', $levelList);
        View::assign('freeLevelArray', $freeLevelArray);
        View::assign('faceFreeLevelArray', $faceFreeLevelArray);
        View::assign('comprehensiveFreeLevelArray', $comprehensiveFreeLevelArray);
        View::assign('statistics', $statistics);
        return View::fetch();
    }

    // 2025-01-13 16:30:00,210-INFO-[SheZhenSet][save_001] 保存舌诊设置 - 增加显示设置
    public function save(){
        // 记录操作日志
        \app\common\System::plog('保存舌诊设置');
        
        // 2025-01-13 16:30:00,211-INFO-[SheZhenSet][save_001] 开始保存舌诊设置 - 包含显示设置
        $info = input('info');
        
        // 2025-01-17 数据验证 - 增加面诊和综合诊疗验证
        if(!isset($info['is_open'])) $info['is_open'] = 0;
        if(!isset($info['face_diagnosis_enable'])) $info['face_diagnosis_enable'] = 0;
        if(!isset($info['comprehensive_diagnosis_enable'])) $info['comprehensive_diagnosis_enable'] = 0;

        // 价格验证
        if(!isset($info['single_price']) || $info['single_price'] < 0) {
            return json(['code'=>0,'msg'=>'舌诊价格不能为负数']);
        }
        if(!isset($info['face_price']) || $info['face_price'] < 0) {
            return json(['code'=>0,'msg'=>'面诊价格不能为负数']);
        }
        if(!isset($info['comprehensive_price']) || $info['comprehensive_price'] < 0) {
            return json(['code'=>0,'msg'=>'综合诊疗价格不能为负数']);
        }

        // 免费次数验证
        if(!isset($info['daily_free_count']) || intval($info['daily_free_count']) < 0) $info['daily_free_count'] = 1;
        if(!isset($info['face_daily_free_count']) || intval($info['face_daily_free_count']) < 0) $info['face_daily_free_count'] = 1;
        if(!isset($info['comprehensive_daily_free_count']) || intval($info['comprehensive_daily_free_count']) < 0) $info['comprehensive_daily_free_count'] = 1;

        if(!isset($info['description'])) $info['description'] = '';

        // 处理免费等级数据
        if(!isset($info['free_level'])) $info['free_level'] = '';
        if(is_array($info['free_level'])){
            $info['free_level'] = implode(',', $info['free_level']);
        }

        if(!isset($info['face_free_level'])) $info['face_free_level'] = '';
        if(is_array($info['face_free_level'])){
            $info['face_free_level'] = implode(',', $info['face_free_level']);
        }

        if(!isset($info['comprehensive_free_level'])) $info['comprehensive_free_level'] = '';
        if(is_array($info['comprehensive_free_level'])){
            $info['comprehensive_free_level'] = implode(',', $info['comprehensive_free_level']);
        }
        
        // 2025-01-13 16:30:00,213-INFO-[SheZhenSet][save_003] 处理显示设置字段
        if(!isset($info['show_score'])) $info['show_score'] = 0;
        if(!isset($info['show_score_value'])) $info['show_score_value'] = 0; // 2025-01-27 增加评分分值显示开关处理
        if(!isset($info['show_symptoms'])) $info['show_symptoms'] = 0;
        if(!isset($info['show_tongue_analysis'])) $info['show_tongue_analysis'] = 0;
        if(!isset($info['show_care_advice'])) $info['show_care_advice'] = 0;
        if(!isset($info['show_product_recommend'])) $info['show_product_recommend'] = 0;
        
        // 2025-01-13 16:30:00,214-INFO-[SheZhenSet][save_004] 检查设置是否存在
        $exists = Db::name('shezhen_set')->where('aid', $this->aid)->where('bid', $this->bid)->find();
        
        // 准备保存的数据
        $data = [
            'aid' => $this->aid,
            'bid' => $this->bid,
            'is_open' => intval($info['is_open']),
            'single_price' => floatval($info['single_price']),
            'free_level' => $info['free_level'],
            'daily_free_count' => intval($info['daily_free_count']),
            // 2025-01-17 新增面诊配置字段
            'face_diagnosis_enable' => intval($info['face_diagnosis_enable']),
            'face_price' => floatval($info['face_price']),
            'face_free_level' => $info['face_free_level'],
            'face_daily_free_count' => intval($info['face_daily_free_count']),
            // 2025-01-17 新增综合诊疗配置字段
            'comprehensive_diagnosis_enable' => intval($info['comprehensive_diagnosis_enable']),
            'comprehensive_price' => floatval($info['comprehensive_price']),
            'comprehensive_free_level' => $info['comprehensive_free_level'],
            'comprehensive_daily_free_count' => intval($info['comprehensive_daily_free_count']),
            'description' => trim($info['description']),
            'recharge_url' => isset($info['recharge_url']) ? trim($info['recharge_url']) : '/pagesA/member/chongzhi',
            'aliyun_access_key' => isset($info['aliyun_access_key']) ? trim($info['aliyun_access_key']) : '',
            'aliyun_secret_key' => isset($info['aliyun_secret_key']) ? trim($info['aliyun_secret_key']) : '',
            'aliyun_endpoint' => isset($info['aliyun_endpoint']) ? trim($info['aliyun_endpoint']) : 'https://imagerecog.cn-shanghai.aliyuncs.com',
            'aliyun_app_code' => isset($info['aliyun_app_code']) ? trim($info['aliyun_app_code']) : '',
            // 显示设置字段
            'show_score' => intval($info['show_score']),
            'show_score_value' => intval($info['show_score_value']),
            'show_symptoms' => intval($info['show_symptoms']),
            'show_tongue_analysis' => intval($info['show_tongue_analysis']),
            'show_care_advice' => intval($info['show_care_advice']),
            'show_product_recommend' => intval($info['show_product_recommend']),
            'is_recommend_open' => isset($info['is_recommend_open']) ? intval($info['is_recommend_open']) : 0,
            'recommend_title' => isset($info['recommend_title']) ? trim($info['recommend_title']) : '根据您的舌诊结果，为您推荐以下产品',
            'recommend_count' => isset($info['recommend_count']) ? intval($info['recommend_count']) : 3,
            'updatetime' => time()
        ];
        
        if($exists){
            // 2025-01-13 16:30:00,216-INFO-[SheZhenSet][save_006] 更新设置 - 包含显示设置
            $result = Db::name('shezhen_set')->where('aid', $this->aid)->where('bid', $this->bid)->update($data);
            \app\common\System::plog('更新舌诊设置：' . json_encode($data, JSON_UNESCAPED_UNICODE));
        }else{
            // 2025-01-13 16:30:00,217-INFO-[SheZhenSet][save_007] 创建新设置 - 包含显示设置
            $data['createtime'] = time();
            $result = Db::name('shezhen_set')->insert($data);
            \app\common\System::plog('创建舌诊设置：' . json_encode($data, JSON_UNESCAPED_UNICODE));
        }
        
        if($result !== false){
            // 2025-01-13 16:30:00,218-INFO-[SheZhenSet][save_008] 保存成功 - 包含显示设置
            return json(['code'=>1,'msg'=>'保存成功','data'=>null]);
        }else{
            return json(['code'=>0,'msg'=>'保存失败，请重试']);
        }
    }

    // 2025-01-13 16:30:00,220-INFO-[SheZhenSet][test_api_001] 测试阿里云接口连接
    public function testApi(){
        // 2025-01-13 16:30:00,221-INFO-[SheZhenSet][testApi_001] 开始测试API连接
        $info = input('info');
        
        if(empty($info['aliyun_access_key']) || empty($info['aliyun_secret_key'])){
            return json(['code'=>0,'msg'=>'请先配置阿里云AccessKey和SecretKey']);
        }
        
        // 这里可以添加实际的API测试逻辑
        // 暂时返回成功，实际项目中需要调用阿里云API进行测试
        
        // 2025-01-13 16:30:00,222-INFO-[SheZhenSet][testApi_002] API连接测试成功
        return json(['code'=>1,'msg'=>'API连接测试成功','data'=>null]);
    }

    // 2025-01-13 16:30:00,230-INFO-[SheZhenSet][statistics_001] 舌诊统计数据
    public function statistics(){
        // 2025-01-13 16:30:00,231-INFO-[SheZhenSet][statistics_001] 获取舌诊统计数据
        $startTime = input('start_time', date('Y-m-d', strtotime('-30 days')));
        $endTime = input('end_time', date('Y-m-d'));
        
        $startTimestamp = strtotime($startTime . ' 00:00:00');
        $endTimestamp = strtotime($endTime . ' 23:59:59');
        
        $where = [
            ['aid', '=', $this->aid],
            ['bid', '=', $this->bid],
            ['status', '=', 1]
        ];
        
        if($startTimestamp && $endTimestamp){
            $where[] = ['createtime', 'between', [$startTimestamp, $endTimestamp]];
        }
        
        // 获取统计数据
        $totalCount = Db::name('shezhen_record')->where($where)->count();
        $totalIncome = Db::name('shezhen_record')->where($where)->sum('price');
        $freeCount = Db::name('shezhen_record')->where($where)->where('is_free', 1)->count();
        $paidCount = $totalCount - $freeCount;
        
        $data = [
            'total_count' => $totalCount,
            'total_income' => round($totalIncome, 2),
            'free_count' => $freeCount,
            'paid_count' => $paidCount,
            'avg_price' => $totalCount > 0 ? round($totalIncome / $totalCount, 2) : 0
        ];
        
        // 2025-01-13 16:30:00,232-INFO-[SheZhenSet][statistics_002] 统计数据获取成功
        return json(['code'=>1,'msg'=>'获取成功','data'=>$data]);
    }

    // 2025-01-13 16:30:00,240-INFO-[SheZhenSet][getConfig_001] 获取舌诊配置状态 - 增加显示设置
    public function getConfig(){
        // 记录操作日志
        \app\common\System::plog('获取舌诊配置状态');
        
        // 2025-01-13 16:30:00,241-INFO-[SheZhenSet][getConfig_002] 获取当前平台设置 - 包含显示设置
        $set = Db::name('shezhen_set')->where('aid', $this->aid)->where('bid', $this->bid)->find();
        
        if(!$set){
            return json(['code'=>0,'msg'=>'配置不存在','data'=>null]);
        }
        
        // 判断是否配置了自己的阿里云
        $hasOwnAliyun = !empty($set['aliyun_access_key']) && !empty($set['aliyun_secret_key']) && !empty($set['aliyun_app_code']);
        
        $config = [
            'is_open' => $set['is_open'],
            'single_price' => $set['single_price'],
            'daily_free_count' => $set['daily_free_count'],
            'has_own_aliyun' => $hasOwnAliyun,
            'aliyun_endpoint' => $set['aliyun_endpoint'],
            'description' => $set['description'],
            // 2025-01-13 16:30:00,242-INFO-[SheZhenSet][getConfig_003] 添加显示设置到配置返回
            'show_score' => isset($set['show_score']) ? $set['show_score'] : 1,
            'show_score_value' => isset($set['show_score_value']) ? $set['show_score_value'] : 1, // 2025-01-27 增加评分分值显示开关到配置返回
            'show_symptoms' => isset($set['show_symptoms']) ? $set['show_symptoms'] : 1,
            'show_tongue_analysis' => isset($set['show_tongue_analysis']) ? $set['show_tongue_analysis'] : 1,
            'show_care_advice' => isset($set['show_care_advice']) ? $set['show_care_advice'] : 1,
            'show_product_recommend' => isset($set['show_product_recommend']) ? $set['show_product_recommend'] : 1,
            'recharge_url' => isset($set['recharge_url']) ? $set['recharge_url'] : '/pagesA/member/chongzhi'
        ];
        
        // 2025-01-13 16:30:00,243-INFO-[SheZhenSet][getConfig_004] 返回配置状态 - 包含显示设置
        return json(['code'=>1,'msg'=>'获取成功','data'=>$config]);
    }

    // 2025-01-13 16:30:00,250-INFO-[SheZhenSet][getCurrentUsage_001] 获取当前使用情况统计
    public function getCurrentUsage(){
        // 记录操作日志
        \app\common\System::plog('获取舌诊使用统计');
        
        $currentTime = time();
        $todayStart = strtotime(date('Y-m-d', $currentTime));
        $monthStart = strtotime(date('Y-m-01', $currentTime));
        
        $where = [
            ['aid', '=', $this->aid],
            ['bid', '=', $this->bid],
            ['status', '=', 1]
        ];
        
        // 今日统计
        $todayCount = Db::name('shezhen_record')->where($where)->where('createtime', '>=', $todayStart)->count();
        $todayIncome = Db::name('shezhen_record')->where($where)->where('createtime', '>=', $todayStart)->sum('price');
        
        // 本月统计  
        $monthCount = Db::name('shezhen_record')->where($where)->where('createtime', '>=', $monthStart)->count();
        $monthIncome = Db::name('shezhen_record')->where($where)->where('createtime', '>=', $monthStart)->sum('price');
        
        // 总统计
        $totalCount = Db::name('shezhen_record')->where($where)->count();
        $totalIncome = Db::name('shezhen_record')->where($where)->sum('price');
        
        // 免费和付费次数统计
        $freeCount = Db::name('shezhen_record')->where($where)->where('is_free', 1)->count();
        $paidCount = $totalCount - $freeCount;
        
        // 2025-01-13 16:30:00,251-INFO-[SheZhenSet][getCurrentUsage_002] 获取当前平台面诊次数余额
        $adminInfo = Db::name('admin')->where('id', $this->aid)->field('mianzhen_num')->find();
        $currentMianzhenNum = $adminInfo ? intval($adminInfo['mianzhen_num']) : 0;
        \app\common\System::plog('刷新获取当前平台面诊次数余额：' . $currentMianzhenNum);
        
        $statistics = [
            'today_count' => $todayCount,
            'today_income' => round($todayIncome, 2),
            'month_count' => $monthCount, 
            'month_income' => round($monthIncome, 2),
            'total_count' => $totalCount,
            'total_income' => round($totalIncome, 2),
            'free_count' => $freeCount,
            'paid_count' => $paidCount,
            'current_mianzhen_num' => $currentMianzhenNum  // 添加当前平台面诊次数
        ];
        
        return json(['code'=>1,'msg'=>'获取成功','data'=>$statistics]);
    }
} 