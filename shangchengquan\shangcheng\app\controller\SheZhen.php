<?php
/**
 * 鲸犀商城 - 微信公众号小程序商城系统!
 * Copyright © 2020 鲸犀网络科技有限公司 保留所有权利
 * =========================================================
 * 版本：供应链版本
 * 授权主体：无限制版本
 * 授权域名：@@@
     
 * ----------------------------------------------
 * 您只能在商业授权范围内使用，不可二次转售、分发、分享、传播
 * 任何企业和个人不得对代码以任何目的任何形式的再发布
 * =========================================================
 */

// +----------------------------------------------------------------------
// | 舌诊记录管理
// +----------------------------------------------------------------------
namespace app\controller;
use think\facade\View;
use think\facade\Db;

class SheZhen extends Common
{
    // 2025-01-03 22:55:53,565-INFO-[SheZhen][index_001] 舌诊记录列表
    public function index(){
        if(request()->isAjax()){
            // 2025-01-03 22:55:53,565-INFO-[SheZhen][index_002] 处理Ajax请求获取列表数据
            $page = input('param.page');
            $limit = input('param.limit');
            
            if(input('param.field') && input('param.order')){
                $order = input('param.field').' '.input('param.order');
            }else{
                $order = 'id desc';
            }
            
            $where = array();
            $where[] = ['aid','=',aid];
            if(bid==0){
                if(input('param.bid')){
                    $where[] = ['bid','=',input('param.bid')];
                }
            }else{
                $where[] = ['bid','=',bid];
            }
            
            // 2025-01-03 22:55:53,565-INFO-[SheZhen][index_003] 处理搜索条件
            if(input('param.mid')) $where[] = ['mid','=',input('param.mid/d')];
            if(input('param.order_no')) $where[] = ['order_no','like','%'.input('param.order_no').'%'];
            if(input('param.constitution_type')) $where[] = ['constitution_type','like','%'.input('param.constitution_type').'%'];
            if(input('?param.status') && input('param.status')!=='') $where[] = ['status','=',input('param.status')];
            if(input('?param.is_free') && input('param.is_free')!=='') $where[] = ['is_free','=',input('param.is_free')];
            
            if(input('param.ctime')){
                $ctime = explode(' ~ ',input('param.ctime'));
                $where[] = ['createtime','>=',strtotime($ctime[0])];
                $where[] = ['createtime','<',strtotime($ctime[1]) + 86400];
            }
            
            // 2025-01-03 22:55:53,565-INFO-[SheZhen][index_004] 查询数据
            $count = 0 + Db::name('shezhen_record')->where($where)->count();
            $data = Db::name('shezhen_record')->where($where)->page($page,$limit)->order($order)->select()->toArray();
            
            // 2025-01-03 22:55:53,565-INFO-[SheZhen][index_005] 处理数据关联信息
            foreach($data as $k=>$v){
                // 获取用户信息
                $member = Db::name('member')->where('id',$v['mid'])->field('nickname,headimg,tel')->find();
                $data[$k]['member_info'] = $member ?: ['nickname'=>'未知用户','headimg'=>'','tel'=>''];
                
                // 格式化时间
                $data[$k]['createtime_format'] = date('Y-m-d H:i:s',$v['createtime']);
                
                // 格式化价格
                $data[$k]['price_format'] = number_format($v['price'],2);
                
                // 处理体质得分
                $data[$k]['constitution_score_format'] = number_format($v['constitution_score'],1);
            }
            
            return json(['code'=>0,'msg'=>'查询成功','count'=>$count,'data'=>$data]);
        }

        // 2025-01-03 22:55:53,565-INFO-[SheZhen][index_006] 返回页面视图
        return View::fetch();
    }

    // 2025-01-03 22:55:53,565-INFO-[SheZhen][detail_001] 查看舌诊记录详情
    public function detail(){
        $id = input('param.id/d');
        if(!$id){
            return json(['code'=>0,'msg'=>'参数错误']);
        }
        
        // 2025-01-03 22:55:53,565-INFO-[SheZhen][detail_002] 获取记录详情
        $where = [
            ['aid','=',aid],
            ['id','=', $id]
        ];
        if(bid > 0){
            $where[] = ['bid','=',bid];
        }
        
        $record = Db::name('shezhen_record')->where($where)->find();
        if(!$record){
            return json(['code'=>0,'msg'=>'记录不存在']);
        }
        
        // 2025-01-03 22:55:53,565-INFO-[SheZhen][detail_003] 获取用户信息
        $member = Db::name('member')->where('id',$record['mid'])->field('nickname,headimg,tel')->find();
        $record['member_info'] = $member ?: ['nickname'=>'未知用户','headimg'=>'','tel'=>''];
        
        // 2025-01-03 22:55:53,565-INFO-[SheZhen][detail_004] 获取报告信息
        $report = Db::name('shezhen_report')->where('record_id',$id)->find();
        $record['report'] = $report;
        
        // 2025-01-03 22:55:53,565-INFO-[SheZhen][detail_005] 处理分析结果
        if($record['analysis_result']){
            $record['analysis_result'] = json_decode($record['analysis_result'], true);
        }
        
        View::assign('record', $record);
        return View::fetch();
    }

    // 2025-01-03 22:55:53,565-INFO-[SheZhen][report_001] 舌诊报告管理
    public function report(){
        if(request()->isAjax()){
            // 2025-01-03 22:55:53,565-INFO-[SheZhen][report_002] 处理Ajax请求获取报告列表
            $page = input('param.page');
            $limit = input('param.limit');
            
            if(input('param.field') && input('param.order')){
                $order = input('param.field').' '.input('param.order');
            }else{
                $order = 'id desc';
            }
            
            $where = array();
            $where[] = ['aid','=',aid];
            if(bid > 0){
                $where[] = ['bid','=',bid];
            }
            
            // 2025-01-03 22:55:53,565-INFO-[SheZhen][report_003] 处理搜索条件
            if(input('param.mid')) $where[] = ['mid','=',input('param.mid/d')];
            if(input('param.report_title')) $where[] = ['report_title','like','%'.input('param.report_title').'%'];
            if(input('param.health_status')) $where[] = ['health_status','like','%'.input('param.health_status').'%'];
            if(input('?param.status') && input('param.status')!=='') $where[] = ['status','=',input('param.status')];
            
            if(input('param.ctime')){
                $ctime = explode(' ~ ',input('param.ctime'));
                $where[] = ['createtime','>=',strtotime($ctime[0])];
                $where[] = ['createtime','<',strtotime($ctime[1]) + 86400];
            }
            
            // 2025-01-03 22:55:53,565-INFO-[SheZhen][report_004] 查询报告数据
            $count = 0 + Db::name('shezhen_report')->where($where)->count();
            $data = Db::name('shezhen_report')->where($where)->page($page,$limit)->order($order)->select()->toArray();
            
            // 2025-01-03 22:55:53,565-INFO-[SheZhen][report_005] 处理数据关联信息
            foreach($data as $k=>$v){
                // 获取用户信息
                $member = Db::name('member')->where('id',$v['mid'])->field('nickname,headimg,tel')->find();
                $data[$k]['member_info'] = $member ?: ['nickname'=>'未知用户','headimg'=>'','tel'=>''];
                
                // 获取记录信息
                $record = Db::name('shezhen_record')->where('id',$v['record_id'])->field('order_no,constitution_type,price')->find();
                $data[$k]['record_info'] = $record ?: ['order_no'=>'','constitution_type'=>'','price'=>0];
                
                // 格式化时间
                $data[$k]['createtime_format'] = date('Y-m-d H:i:s',$v['createtime']);
            }
            
            return json(['code'=>0,'msg'=>'查询成功','count'=>$count,'data'=>$data]);
        }

        // 2025-01-03 22:55:53,565-INFO-[SheZhen][report_006] 返回报告页面视图
        return View::fetch();
    }

    // 2025-01-03 22:55:53,565-INFO-[SheZhen][statistics_001] 舌诊统计页面
    public function statistics(){
        if(request()->isAjax()){
            // 处理时间范围
            $timeRange = input('param.time_range', 'month');
            $whereTime = [];
            
            $now = time();
        $today = strtotime(date('Y-m-d'));
            $yesterday = strtotime('-1 day', $today);
            $thisWeekStart = strtotime('this week Monday', $now);
            $thisMonthStart = strtotime(date('Y-m-01'));
            $thisYearStart = strtotime(date('Y-01-01'));
            
            switch($timeRange){
                case 'today':
                    $whereTime = ['createtime', 'between', [$today, $today + 86399]];
                    $startTime = $today;
                    break;
                case 'yesterday':
                    $whereTime = ['createtime', 'between', [$yesterday, $yesterday + 86399]];
                    $startTime = $yesterday;
                    break;
                case 'week':
                    $whereTime = ['createtime', '>=', $thisWeekStart];
                    $startTime = $thisWeekStart;
                    break;
                case 'year':
                    $whereTime = ['createtime', '>=', $thisYearStart];
                    $startTime = $thisYearStart;
                    break;
                case 'custom':
                    if(input('param.custom_time')){
                        $customTime = explode(' - ', input('param.custom_time'));
                        if(count($customTime) == 2){
                            $startDate = strtotime($customTime[0]);
                            $endDate = strtotime($customTime[1]) + 86399;
                            $whereTime = ['createtime', 'between', [$startDate, $endDate]];
                            $startTime = $startDate;
                        }
                    }
                    break;
                default: // month
                    $whereTime = ['createtime', '>=', $thisMonthStart];
                    $startTime = $thisMonthStart;
                    break;
            }
            
            // 基础查询条件
        $where = [
                ['aid', '=', aid],
                ['status', '=', 1]
        ];
        if(bid > 0){
                $where[] = ['bid', '=', bid];
            }
            
            // 添加时间条件
            if($whereTime){
                $where[] = $whereTime;
            }
            
            // 统计基础数据
            $totalCount = Db::name('shezhen_record')->where($where)->count();
            $totalIncome = Db::name('shezhen_record')->where($where)->sum('price') ?: 0;
            
            // 统计用户数量
            $userCount = Db::name('shezhen_record')->where($where)->distinct(true)->column('mid');
            $userCount = count($userCount);
            
            // 统计平均评分
            $avgScore = Db::name('shezhen_record')->where($where)->avg('constitution_score') ?: 0;
            
            // 统计体质类型分布
        $constitutionStats = Db::name('shezhen_record')
            ->where($where)
            ->field('constitution_type, count(*) as count')
            ->group('constitution_type')
            ->order('count desc')
            ->select()
            ->toArray();
        
            // 计算体质类型百分比
            foreach($constitutionStats as $key => $item){
                $constitutionStats[$key]['percentage'] = round(($item['count'] / $totalCount) * 100, 1);
            }
            
            // 统计付费类型分布
            $freeCount = Db::name('shezhen_record')->where($where)->where('is_free', 1)->count();
            $paidCount = $totalCount - $freeCount;
            $payTypeStats = [
                'free' => $freeCount,
                'paid' => $paidCount
            ];
            
            // 统计体质评分分布
            $scoreDistribution = [
                ['range' => '0-60', 'count' => 0],
                ['range' => '61-70', 'count' => 0],
                ['range' => '71-80', 'count' => 0],
                ['range' => '81-90', 'count' => 0],
                ['range' => '91-100', 'count' => 0]
            ];
            
            $scoreRanges = [
                ['min' => 0, 'max' => 60],
                ['min' => 61, 'max' => 70],
                ['min' => 71, 'max' => 80],
                ['min' => 81, 'max' => 90],
                ['min' => 91, 'max' => 100]
            ];
            
            foreach($scoreRanges as $index => $range){
                $count = Db::name('shezhen_record')
                    ->where($where)
                    ->where('constitution_score', '>=', $range['min'])
                    ->where('constitution_score', '<=', $range['max'])
                    ->count();
                $scoreDistribution[$index]['count'] = $count;
            }
            
            // 生成趋势数据
            $trendData = [];
            $endTime = $now;
            $interval = 24 * 3600; // 默认按天统计
            
            // 根据时间范围调整时间间隔
            if($timeRange == 'year'){
                $interval = 30 * 24 * 3600; // 按月统计
            }elseif($timeRange == 'month' && (($endTime - $startTime) / 86400) > 15){
                $interval = 7 * 24 * 3600; // 如果超过15天，按周统计
            }
            
            // 生成时间段数组
            $timePoints = [];
            $currentTime = $startTime;
            while($currentTime <= $endTime){
                $timePoints[] = $currentTime;
                $currentTime += $interval;
            }
            
            // 确保最后一个时间点不超过当前时间
            if(end($timePoints) > $endTime){
                $timePoints[count($timePoints)-1] = $endTime;
            }
            
            // 统计每个时间段的数据
            for($i = 0; $i < count($timePoints) - 1; $i++){
                $startPoint = $timePoints[$i];
                $endPoint = $timePoints[$i+1] - 1;
                
                $periodCount = Db::name('shezhen_record')
                ->where($where)
                    ->where('createtime', '>=', $startPoint)
                    ->where('createtime', '<=', $endPoint)
                ->count();
                
                $periodIncome = Db::name('shezhen_record')
                ->where($where)
                    ->where('createtime', '>=', $startPoint)
                    ->where('createtime', '<=', $endPoint)
                    ->sum('price') ?: 0;
                
                $dateFormat = $timeRange == 'year' ? 'm月' : 'm-d';
                $trendData[] = [
                    'date' => date($dateFormat, $startPoint),
                    'count' => $periodCount,
                    'income' => number_format($periodIncome, 2)
                ];
            }
            
            // 统计详细数据表格
            $detailData = [];
            if($timeRange == 'year'){
                // 按月统计
                for($m = 1; $m <= 12; $m++){
                    $monthStart = strtotime(date('Y').'-'.$m.'-01');
                    $monthEnd = strtotime('+1 month', $monthStart) - 1;
                    
                    if($monthStart > $now) break;
                    if($monthStart < $startTime) continue;
                    
                    $monthWhere = $where;
                    $monthWhere[] = ['createtime', 'between', [$monthStart, $monthEnd]];
                    
                    $mCount = Db::name('shezhen_record')->where($monthWhere)->count();
                    $mIncome = Db::name('shezhen_record')->where($monthWhere)->sum('price') ?: 0;
                    $mNewUsers = Db::name('shezhen_record')->where($monthWhere)->distinct(true)->count('mid');
                    $mFreeCount = Db::name('shezhen_record')->where($monthWhere)->where('is_free', 1)->count();
                    $mPaidCount = $mCount - $mFreeCount;
                    $mAvgScore = Db::name('shezhen_record')->where($monthWhere)->avg('constitution_score') ?: 0;
                    
                    $detailData[] = [
                        'date' => date('Y年m月', $monthStart),
                        'count' => $mCount,
                        'income' => number_format($mIncome, 2),
                        'new_users' => $mNewUsers,
                        'free_count' => $mFreeCount,
                        'paid_count' => $mPaidCount,
                        'avg_score' => number_format($mAvgScore, 1)
                    ];
                }
            } else {
                // 按天统计
                $dayCount = min(30, ceil(($endTime - $startTime) / 86400));
                for($d = 0; $d < $dayCount; $d++){
                    $dayStart = strtotime(date('Y-m-d', strtotime("-{$d} days")));
                    $dayEnd = $dayStart + 86399;
                    
                    if($dayEnd < $startTime) break;
                    if($dayStart > $endTime) continue;
                    
                    $dayWhere = $where;
                    $dayWhere[] = ['createtime', 'between', [$dayStart, $dayEnd]];
                    
                    $dCount = Db::name('shezhen_record')->where($dayWhere)->count();
                    $dIncome = Db::name('shezhen_record')->where($dayWhere)->sum('price') ?: 0;
                    $dNewUsers = Db::name('shezhen_record')->where($dayWhere)->distinct(true)->count('mid');
                    $dFreeCount = Db::name('shezhen_record')->where($dayWhere)->where('is_free', 1)->count();
                    $dPaidCount = $dCount - $dFreeCount;
                    $dAvgScore = Db::name('shezhen_record')->where($dayWhere)->avg('constitution_score') ?: 0;
                    
                    $detailData[] = [
                        'date' => date('Y-m-d', $dayStart),
                        'count' => $dCount,
                        'income' => number_format($dIncome, 2),
                        'new_users' => $dNewUsers,
                        'free_count' => $dFreeCount,
                        'paid_count' => $dPaidCount,
                        'avg_score' => number_format($dAvgScore, 1)
                    ];
                }
                
                // 倒序排列，最近日期在前
                $detailData = array_reverse($detailData);
            }
            
            // 返回统计数据
            $data = [
                'total_count' => $totalCount,
                'total_income' => number_format($totalIncome, 2),
                'user_count' => $userCount,
                'avg_score' => number_format($avgScore, 1),
                'constitution_stats' => $constitutionStats,
                'trend_data' => $trendData,
                'pay_type_stats' => $payTypeStats,
                'score_distribution' => $scoreDistribution,
                'detail_data' => $detailData
            ];
            
            return json(['code'=>1, 'msg'=>'查询成功', 'data'=>$data]);
        }

        return View::fetch();
    }

    // 2025-01-03 22:55:53,565-INFO-[SheZhen][del_001] 删除舌诊记录
    public function del(){
        $ids = input('post.ids/a');
        if(empty($ids)){
            return json(['code'=>0, 'msg'=>'请选择要删除的记录']);
        }
        
        $where = [
            ['aid', '=', aid],
            ['id', 'in', $ids]
        ];
        if(bid > 0){
            $where[] = ['bid', '=', bid];
        }
        
        // 2025-01-03 22:55:53,565-INFO-[SheZhen][del_002] 软删除记录
        Db::name('shezhen_record')->where($where)->update(['status'=>0, 'updatetime'=>time()]);
        
        // 2025-01-03 22:55:53,565-INFO-[SheZhen][del_003] 同时删除相关报告
        Db::name('shezhen_report')->where('record_id', 'in', $ids)->update(['status'=>0, 'updatetime'=>time()]);
        
        return json(['code'=>1, 'msg'=>'删除成功']);
    }

    // 2025-01-03 22:55:53,565-INFO-[SheZhen][setst_001] 设置记录状态
    public function setst(){
        $ids = input('post.ids/a');
        $status = input('post.status/d');
        
        if(empty($ids)){
            return json(['code'=>0, 'msg'=>'请选择要操作的记录']);
        }
        
        $where = [
            ['aid', '=', aid],
            ['id', 'in', $ids]
        ];
        if(bid > 0){
            $where[] = ['bid', '=', bid];
        }
        
        // 2025-01-03 22:55:53,565-INFO-[SheZhen][setst_002] 更新状态
        Db::name('shezhen_record')->where($where)->update(['status'=>$status, 'updatetime'=>time()]);
        
        $statusText = $status == 1 ? '启用' : '禁用';
        return json(['code'=>1, 'msg'=>$statusText.'成功']);
    }

    // 2025-01-03 22:55:53,565-INFO-[SheZhen][export_001] 导出舌诊记录
    public function excel()
    {
        // 排序规则处理  
        $field = input('param.field', 'id');
        $order = input('param.order', 'desc');
        
        // 允许的排序字段
        $allowField = ['id', 'createtime', 'order_no', 'is_free', 'status'];
        if (!in_array($field, $allowField)) {
            $field = 'id';
        }
        if (!in_array($order, ['asc', 'desc'])) {
            $order = 'desc';
        }
        
        // 查询条件构建
        $where = [];
        if (input('param.ordernum')) {
            $where[] = ['order_no', 'like', '%' . input('param.ordernum') . '%'];
        }
        if (input('param.mid')) {
            $where[] = ['mid', '=', input('param.mid')];
        }
        if (input('param.analysis_status') !== '') {
            $where[] = ['analysis_status', '=', input('param.analysis_status')];
        }
        if (input('param.pay_status') !== '') {
            $where[] = ['pay_status', '=', input('param.pay_status')];
        }
        if (input('param.is_free') !== '') {
            $where[] = ['is_free', '=', input('param.is_free')];
        }
        if (input('param.constitution_type')) {
            $where[] = ['constitution_type', 'like', '%' . input('param.constitution_type') . '%'];
        }
        if (input('param.status') !== '') {
            $where[] = ['a.status', '=', input('param.status')];
        } else {
            $where[] = ['a.status', '>=', 0];
        }
        
        // 指定ID导出
        if (input('param.ids')) {
            $ids = explode(',', input('param.ids'));
            $where[] = ['a.id', 'in', $ids];
        }
        
        // 查询数据并关联用户信息
        $data = Db::name('shezhen_record')
            ->alias('a')
            ->leftJoin('member m', 'a.mid = m.id')
            ->where($where)
            ->field('a.*, m.nickname, m.mobile')
            ->order($field . ' ' . $order)
            ->select();
        
        // 导出数据头部定义
        $titleArr = [
            'ID',
            '订单号',
            '用户ID',
            '用户昵称',
            '用户手机号',
            '舌头图片',
            '体质类型',
            '体质评分',
            '健康评分',
            '风险评估',
            '创建时间',
            '分析状态',
            '付费状态',
            '付费金额',
            '免费标识',
            '状态'
        ];
        
        // 数据处理
        $dataArr = [];
        foreach ($data as $item) {
            $dataArr[] = [
                $item['id'],
                $item['order_no'] ?: '无',
                $item['mid'],
                $item['nickname'] ?: '未知用户',
                $item['mobile'] ?: '未绑定',
                $item['tongue_image'] ?: '无图片',
                $item['constitution_type'] ?: '未分析',
                $item['constitution_score'] ?: '0',
                $item['health_score'] ?: '0',
                $item['risk_assessment'] ?: '未评估',
                date('Y-m-d H:i:s', $item['createtime']),
                $item['analysis_status'] == 1 ? '已分析' : '未分析',
                $item['pay_status'] == 1 ? '已支付' : '未支付',
                $item['price'] ?: '0.00',
                $item['is_free'] == 1 ? '免费' : '付费',
                $item['status'] == 1 ? '正常' : '已删除'
            ];
        }
        
        // 调用导出方法
        $this->export_excel($titleArr, $dataArr, [], '舌诊记录数据');
    }

    // 2025-01-03 22:55:53,565-INFO-[SheZhen][reportDetail_001] 查看舌诊报告详情
    public function reportDetail(){
        $id = input('param.id/d');
        if(!$id){
            return json(['code'=>0,'msg'=>'参数错误']);
        }
        
        // 查询报告详情
        $record = Db::name('shezhen_report')
            ->where(['id'=>$id, 'aid'=>aid])
            ->find();
            
        if(!$record){
            return json(['code'=>0,'msg'=>'报告不存在']);
        }
        
        // 获取关联的舌诊记录
        $shezhen_record = Db::name('shezhen_record')
            ->where('id', $record['record_id'])
            ->find();
            
        // 获取用户信息
        $member_info = Db::name('member')
            ->where('id', $record['mid'])
            ->field('id,nickname,headimg,tel,sex,age')
            ->find();
        
        View::assign('record', $record);
        View::assign('shezhen_record', $shezhen_record);
        View::assign('member_info', $member_info);
        
        return View::fetch();
    }

    public function exportStatistics(){
        $title = input('param.title', '舌诊统计数据');
        $timeRange = input('param.timeRange', 'month');
        $startTime = input('param.startTime');
        $endTime = input('param.endTime');
        
        // 时间范围处理
        $now = time();
        if($timeRange == 'today'){
            $startTime = strtotime(date('Y-m-d'));
            $endTime = $now;
        } elseif($timeRange == 'week'){
            $startTime = strtotime('-7 days');
            $endTime = $now;
        } elseif($timeRange == 'month'){
            $startTime = strtotime('-30 days');
            $endTime = $now;
        } elseif($timeRange == 'year'){
            $startTime = strtotime('-365 days');
            $endTime = $now;
        } elseif($timeRange == 'custom' && $startTime && $endTime){
            $startTime = strtotime($startTime);
            $endTime = strtotime($endTime) + 86399; // 结束时间加到当天最后一秒
        } else {
            $startTime = strtotime('-30 days');
            $endTime = $now;
        }

        // 构建查询条件
        $where = [
            ['aid', '=', aid],
            ['status', '=', 1],
            ['createtime', 'between', [$startTime, $endTime]]
        ];
        if(bid > 0){
            $where[] = ['bid', '=', bid];
        }

        // 基础统计
        $totalCount = Db::name('shezhen_record')->where($where)->count();
        $totalIncome = Db::name('shezhen_record')->where($where)->sum('pay_amount') ?: 0;
        $userCount = Db::name('shezhen_record')->where($where)->distinct(true)->count('mid');
        $avgScore = Db::name('shezhen_record')->where($where)->avg('health_score') ?: 0;
        $freeCount = Db::name('shezhen_record')->where($where)->where('is_free', 1)->count();
        $paidCount = $totalCount - $freeCount;

        // 统计体质类型分布
        $constitutionStats = Db::name('shezhen_record')
            ->where($where)
            ->field('constitution_type, count(*) as count')
            ->group('constitution_type')
            ->order('count desc')
            ->select()
            ->toArray();
        
        // 构造导出数据
        $exportData = [
            '基础统计' => [
                ['项目' => '总分析次数', '数值' => $totalCount.' 次'],
                ['项目' => '总收入', '数值' => '¥'.number_format($totalIncome, 2)],
                ['项目' => '用户数量', '数值' => $userCount.' 人'],
                ['项目' => '平均评分', '数值' => number_format($avgScore, 1).' 分'],
                ['项目' => '免费次数', '数值' => $freeCount.' 次'],
                ['项目' => '付费次数', '数值' => $paidCount.' 次'],
            ],
            '体质类型分布' => []
        ];
        
        // 添加体质类型分布数据
        foreach($constitutionStats as $item){
            $percentage = $totalCount > 0 ? round(($item['count'] / $totalCount) * 100, 1) : 0;
            $exportData['体质类型分布'][] = [
                '体质类型' => $item['constitution_type'],
                '数量' => $item['count'],
                '占比' => $percentage.'%'
            ];
        }
        
        // 添加详细记录
        $records = Db::name('shezhen_record')
            ->alias('shezhen_record')
            ->field('shezhen_record.*,member.nickname,member.tel')
            ->join('member member','member.id=shezhen_record.mid')
            ->where($where)
            ->order('createtime desc')
            ->limit(1000)
            ->select()
            ->toArray();
            
        $recordData = [];
        foreach($records as $record){
            $recordData[] = [
                '订单号' => $record['ordernum'],
                '用户昵称' => $record['nickname'] ?? '未知用户',
                '用户手机' => $record['tel'] ?? '',
                '体质类型' => $record['constitution_type'],
                '健康得分' => $record['health_score'],
                '体质得分' => $record['constitution_score'],
                '消费金额' => number_format($record['pay_amount'], 2),
                '是否免费' => $record['is_free'] ? '是' : '否',
                '创建时间' => date('Y-m-d H:i:s', $record['createtime'])
            ];
        }
        $exportData['详细记录'] = $recordData;
        
        // 这里需要使用Excel导出类，暂时返回JSON格式
        // 实际项目中应替换为实际的Excel导出逻辑
        return json(['code'=>1, 'msg'=>'导出成功', 'data'=>$exportData, 'title'=>$title]);
    }

    /**
     * 舌诊报告导出  
     */
    public function reportExcel()
    {
        // 设置排序规则
        if(input('param.field') && input('param.order')){
            $order = 'shezhen_report.'.input('param.field').' '.input('param.order');
        } else {
            $order = 'shezhen_report.id desc';
        }

        // 构建查询条件
        $where = [];
        $where[] = ['shezhen_report.aid','=',aid];
        if(bid > 0) $where[] = ['shezhen_report.bid','=',bid];
        $where[] = ['shezhen_report.status','=',1];
        
        if(input('param.report_num')) $where[] = ['shezhen_report.report_num','like','%'.trim(input('param.report_num')).'%'];
        if(input('param.mid')) $where[] = ['shezhen_report.mid','=',trim(input('param.mid'))];
        if(input('param.health_status')) $where[] = ['shezhen_report.health_status','like','%'.trim(input('param.health_status')).'%'];
        if(input('param.nickname')) $where[] = ['member.nickname','like','%'.trim(input('param.nickname')).'%'];

        // 查询数据
        $list = Db::name('shezhen_report')
            ->alias('shezhen_report')
            ->field('member.nickname,member.tel,shezhen_report.*,shezhen_record.ordernum,shezhen_record.analysis_time')
            ->join('member member','member.id=shezhen_report.mid')
            ->join('shezhen_record shezhen_record','shezhen_record.id=shezhen_report.record_id')
            ->where($where)
            ->order($order)
            ->select()
            ->toArray();

        // 表头定义
        $title = array();
        $title[] = '报告编号';
        $title[] = '用户信息';
        $title[] = '联系电话';
        $title[] = '报告标题';
        $title[] = '健康状态';
        $title[] = '风险评估';
        $title[] = '健康得分';
        $title[] = '体质类型';
        $title[] = '关联订单';
        $title[] = '分析时间';
        $title[] = '创建时间';

        // 数据处理
        $data = array();
        foreach($list as $v){
            $tdata = array();
            $tdata[] = $v['report_num'];
            $tdata[] = $v['nickname'].'(ID:'.$v['mid'].')';
            $tdata[] = $v['tel'] ?: '未填写';
            $tdata[] = $v['report_title'] ?: '舌诊分析报告';
            $tdata[] = $this->getHealthStatusText($v['health_status']);
            $tdata[] = $v['risk_assessment'] ?: '无风险';
            $tdata[] = $v['health_score'] ?: '未评分';
            $tdata[] = $v['constitution_type'] ?: '未分析';
            $tdata[] = $v['ordernum'] ?: '无订单';
            $tdata[] = $v['analysis_time'] ? date('Y-m-d H:i:s',$v['analysis_time']) : '未分析';
            $tdata[] = date('Y-m-d H:i:s',$v['createtime']);
            
            $data[] = $tdata;
        }

        // 调用导出方法
        $this->export_excel($title,$data);
    }

    // 2025-01-03 22:55:53,565-INFO-[SheZhen][exportReport_001] 导出舌诊报告
    public function exportReport(){
        $id = input('param.id/d');
        
        // 获取报告数据
        $where = [
            ['aid', '=', aid],
            ['status', '=', 1]
        ];
        if(bid > 0){
            $where[] = ['bid', '=', bid];
        }
        
        if($id){
            $where[] = ['id', '=', $id];
            $report = Db::name('shezhen_report')->where($where)->find();
            
            if(!$report){
                return json(['code'=>0, 'msg'=>'报告不存在']);
            }
            
            // 获取关联舌诊记录
            $record = Db::name('shezhen_record')->where('id', $report['record_id'])->find();
            
            // 获取用户信息
            $member = Db::name('member')->where('id', $report['mid'])->field('nickname,tel')->find();
            
            // 构造导出数据
            $exportData = [
                '报告基本信息' => [
                    ['项目' => '报告编号', '内容' => $report['report_num']],
                    ['项目' => '报告标题', '内容' => $report['report_title']],
                    ['项目' => '健康状态', '内容' => $this->getHealthStatusText($report['health_status'])],
                    ['项目' => '用户昵称', '内容' => $member['nickname'] ?? '未知用户'],
                    ['项目' => '用户手机', '内容' => $member['tel'] ?? ''],
                    ['项目' => '生成时间', '内容' => date('Y-m-d H:i:s', $report['createtime'])],
                ],
                '风险评估' => [
                    ['风险评估' => $report['risk_assessment'] ?: '无风险评估信息']
                ],
                '报告内容' => [
                    ['报告内容' => $report['content'] ?: '无报告内容']
                ]
            ];
            
            if($record){
                $exportData['关联舌诊记录'] = [
                    ['项目' => '订单号', '内容' => $record['ordernum']],
                    ['项目' => '体质类型', '内容' => $record['constitution_type']],
                    ['项目' => '体质评分', '内容' => $record['constitution_score']],
                    ['项目' => '付费类型', '内容' => $record['is_free'] ? '免费' : '付费('.number_format($record['pay_amount'], 2).'元)'],
                    ['项目' => '舌诊时间', '内容' => date('Y-m-d H:i:s', $record['createtime'])],
                ];
            }
            
            // 健康建议部分
            $suggestions = [];
            if(!empty($report['health_suggestions']) || (!empty($record) && !empty($record['health_suggestions']))){
                $suggestions[] = ['建议类型' => '健康建议', '建议内容' => $report['health_suggestions'] ?: $record['health_suggestions']];
            }
            if(!empty($report['diet_suggestions']) || (!empty($record) && !empty($record['diet_suggestions']))){
                $suggestions[] = ['建议类型' => '饮食建议', '建议内容' => $report['diet_suggestions'] ?: $record['diet_suggestions']];
            }
            if(!empty($report['exercise_suggestions']) || (!empty($record) && !empty($record['exercise_suggestions']))){
                $suggestions[] = ['建议类型' => '运动建议', '建议内容' => $report['exercise_suggestions'] ?: $record['exercise_suggestions']];
            }
            
            if(!empty($suggestions)){
                $exportData['健康建议'] = $suggestions;
            }
            
            $filename = '舌诊报告_' . $report['report_num'] . '_' . date('YmdHis') . '.xlsx';
            
            // 这里需要使用Excel导出类，暂时返回JSON格式
            return json(['code'=>1, 'msg'=>'导出成功', 'data'=>$exportData, 'filename'=>$filename]);
        }
        
        // 处理批量导出的情况
        $ids = input('param.ids');
        if($ids){
            $idArray = explode(',', $ids);
            $where[] = ['id', 'in', $idArray];
        }
        
        $reports = Db::name('shezhen_report')->where($where)->select()->toArray();
        
        if(empty($reports)){
            return json(['code'=>0, 'msg'=>'没有找到符合条件的报告']);
        }
        
        $exportData = [];
        foreach($reports as $report){
            $member = Db::name('member')->where('id', $report['mid'])->field('nickname,tel')->find();
            $exportData[] = [
                '报告编号' => $report['report_num'],
                '报告标题' => $report['report_title'],
                '健康状态' => $this->getHealthStatusText($report['health_status']),
                '用户昵称' => $member['nickname'] ?? '未知用户',
                '用户手机' => $member['tel'] ?? '',
                '风险评估' => $report['risk_assessment'],
                '生成时间' => date('Y-m-d H:i:s', $report['createtime'])
            ];
        }
        
        $filename = '批量舌诊报告_' . date('YmdHis') . '.xlsx';
        
        // 这里需要使用Excel导出类，暂时返回JSON格式
        return json(['code'=>1, 'msg'=>'导出成功', 'data'=>$exportData, 'filename'=>$filename]);
    }
    
    // 获取健康状态文本
    private function getHealthStatusText($status){
        switch($status){
            case 'good':
                return '良好';
            case 'normal':
                return '一般';
            case 'poor':
                return '较差';
            default:
                return '未知';
        }
    }

    // 2025-01-03 22:55:53,565-INFO-[SheZhen][delReport_001] 删除舌诊报告
    public function delReport(){
        $ids = input('post.ids/a');
        if(empty($ids)){
            return json(['code'=>0, 'msg'=>'请选择要删除的报告']);
        }
        
        $where = [
            ['aid', '=', aid],
            ['id', 'in', $ids]
        ];
        if(bid > 0){
            $where[] = ['bid', '=', bid];
        }
        
        // 2025-01-03 22:55:53,565-INFO-[SheZhen][delReport_002] 软删除报告
        Db::name('shezhen_report')->where($where)->update(['status'=>0, 'updatetime'=>time()]);
        
        return json(['code'=>1, 'msg'=>'删除成功']);
    }
    
    // 2025-01-03 22:55:53,565-INFO-[SheZhen][recommendManager_001] 推荐商品管理页面
    public function recommendManager(){
        if(request()->isAjax()){
            // 2025-01-03 22:55:53,565-INFO-[SheZhen][recommendManager_002] 处理Ajax请求获取推荐商品列表
            $page = input('param.page', 1);
            $limit = input('param.limit', 10);
            
            if(input('param.field') && input('param.order')){
                $order = input('param.field').' '.input('param.order');
            }else{
                $order = 'sort desc, id desc';
            }
            
            $where = array();
            $where[] = ['aid', '=', aid];
            if(bid > 0){
                $where[] = ['bid', '=', bid];
            }
            $where[] = ['status', '=', 1];
            
            // 2025-01-03 22:55:53,565-INFO-[SheZhen][recommendManager_003] 处理搜索条件
            if(input('param.recommend_type') !== null && input('param.recommend_type') !== '') $where[] = ['recommend_type', '=', input('param.recommend_type')];
            if(input('param.constitution_type')) $where[] = ['constitution_type','like','%'.input('param.constitution_type').'%'];
            if(input('param.product_type') !== null && input('param.product_type') !== '') $where[] = ['product_type', '=', input('param.product_type')];
            if(input('param.member_level_id')) {
                $memberLevelId = input('param.member_level_id');
                $where[] = ['member_level_ids', 'like', '%' . $memberLevelId . '%'];
            }
            
            // 2025-01-03 22:55:53,565-INFO-[SheZhen][recommendManager_004] 查询数据
            $count = Db::name('shezhen_recommend_product')->where($where)->count();
            $data = Db::name('shezhen_recommend_product')->where($where)->page($page, $limit)->order($order)->select()->toArray();
            
            // 2025-01-03 22:55:53,565-INFO-[SheZhen][recommendManager_005] 处理数据
            foreach($data as $k => $v){
                // 获取商品信息
                if($v['product_type'] == 1){ // 商品
                    $product = Db::name('shop_product')->where('id', $v['product_id'])->field('id,name,sell_price,pic')->find();
                    if($product){
                        $product['cname'] = ''; // 手动添加cname字段避免模板报错
                        $data[$k]['product_info'] = $product;
                        $data[$k]['product_type_text'] = '商品';
                    }else{
                        $data[$k]['product_info'] = ['id'=>0, 'name'=>'商品已删除', 'sell_price'=>0, 'pic'=>'', 'cname'=>''];
                        $data[$k]['product_type_text'] = '商品(已删除)';
                    }
                }else{ // 课程
                    $product = Db::name('kecheng_list')->where('id', $v['product_id'])->field('id,name,price,pic')->find();
                    if($product){
                        $product['cname'] = ''; // 手动添加cname字段避免模板报错
                        $data[$k]['product_info'] = $product;
                        $data[$k]['product_type_text'] = '课程';
                    }else{
                        $data[$k]['product_info'] = ['id'=>0, 'name'=>'课程已删除', 'price'=>0, 'pic'=>'', 'cname'=>''];
                        $data[$k]['product_type_text'] = '课程(已删除)';
                    }
                }
                
                // 设置推荐类型文本
                switch($v['recommend_type']){
                    case 1:
                        $data[$k]['recommend_type_text'] = '按体质推荐';
                        $data[$k]['score_range_text'] = '-';
                        break;
                    case 2:
                        $data[$k]['recommend_type_text'] = '按得分推荐';
                        $data[$k]['score_range_text'] = $v['min_score'] . '-' . $v['max_score'] . '分';
                        break;
                    case 3:
                        $data[$k]['recommend_type_text'] = '按体质+得分推荐';
                        $data[$k]['score_range_text'] = $v['min_score'] . '-' . $v['max_score'] . '分';
                        break;
                    case 4:
                        $data[$k]['recommend_type_text'] = '按会员等级推荐';
                        $data[$k]['score_range_text'] = '-';
                        break;
                    case 5:
                        $data[$k]['recommend_type_text'] = '按体质+会员等级推荐';
                        $data[$k]['score_range_text'] = '-';
                        break;
                    case 6:
                        $data[$k]['recommend_type_text'] = '按得分+会员等级推荐';
                        $data[$k]['score_range_text'] = $v['min_score'] . '-' . $v['max_score'] . '分';
                        break;
                    case 7:
                        $data[$k]['recommend_type_text'] = '按体质+得分+会员等级推荐';
                        $data[$k]['score_range_text'] = $v['min_score'] . '-' . $v['max_score'] . '分';
                        break;
                    default:
                        $data[$k]['recommend_type_text'] = '未知';
                        $data[$k]['score_range_text'] = '-';
                }
                
                // 处理会员等级名称
                $data[$k]['member_level_names'] = [];
                if(!empty($v['member_level_ids'])){
                    $levelIds = explode(',', $v['member_level_ids']);
                    $levelNames = Db::name('member_level')
                        ->where('aid', aid)
                        ->where('id', 'in', $levelIds)
                        ->column('name');
                    $data[$k]['member_level_names'] = array_values($levelNames);
                }
                
                // 格式化时间
                $data[$k]['createtime_format'] = date('Y-m-d H:i:s', $v['createtime']);
            }
            
            return json(['code'=>0, 'msg'=>'查询成功', 'count'=>$count, 'data'=>$data]);
        }
        
        // 2025-01-03 22:55:53,565-INFO-[SheZhen][recommendManager_006] 获取所有体质类型
        $constitutionTypes = [
            ['type'=>'平和质', 'desc'=>'平和体质是指阴阳气血调和，脏腑功能平衡的体质状态'],
            ['type'=>'气虚质', 'desc'=>'气虚体质是指体内气量不足，气的功能减退所表现的体质状态'],
            ['type'=>'阳虚质', 'desc'=>'阳虚体质是指体内阳气不足，阳气的温煦、推动、防御等功能减退的体质状态'],
            ['type'=>'阴虚质', 'desc'=>'阴虚体质是指体内阴液亏少，不能制约阳气，相对阳盛的体质状态'],
            ['type'=>'痰湿质', 'desc'=>'痰湿体质是指体内水液代谢异常，聚湿生痰，痰湿内停所表现的体质状态'],
            ['type'=>'湿热质', 'desc'=>'湿热体质是指体内湿、热之邪蕴结，互结难解所表现的体质状态'],
            ['type'=>'血瘀质', 'desc'=>'血瘀体质是指体内血液运行不畅，血流瘀滞所表现的体质状态'],
            ['type'=>'气郁质', 'desc'=>'气郁体质是指气机郁滞，情志不舒所表现的体质状态'],
            ['type'=>'特禀质', 'desc'=>'特禀体质是指先天禀赋特异，对某些物质、药物或环境特别敏感的体质状态']
        ];
        
        // 2025-01-06 11:45:00,038-INFO-[SheZhen][recommendManager_007] 获取会员等级列表
        $levelList = Db::name('member_level')->where('aid', aid)->field('id,name')->order('sort desc,id')->select()->toArray();
        
        View::assign('constitutionTypes', $constitutionTypes);
        View::assign('levelList', $levelList);
        return View::fetch();
    }
    
    // 2025-01-03 22:55:53,565-INFO-[SheZhen][saveRecommend_001] 保存推荐商品
    public function saveRecommend(){
        $data = input('post.');
        
        $recommendType = intval($data['recommend_type']) ?: 1;
        
        // 验证推荐类型相关参数
        if($recommendType == 1 || $recommendType == 3 || $recommendType == 5 || $recommendType == 7){
            if(empty($data['constitution_type'])){
                return json(['code'=>0, 'msg'=>'请选择体质类型']);
            }
        }
        
        if($recommendType == 2 || $recommendType == 3 || $recommendType == 6 || $recommendType == 7){
            if(!isset($data['min_score']) || !isset($data['max_score'])){
                return json(['code'=>0, 'msg'=>'请设置得分范围']);
            }
            
            $minScore = intval($data['min_score']);
            $maxScore = intval($data['max_score']);
            
            if($minScore < 0 || $maxScore > 100 || $minScore > $maxScore){
                return json(['code'=>0, 'msg'=>'得分范围设置有误，请设置0-100之间的有效范围']);
            }
        }
        
        // 验证会员等级参数
        $memberLevelIds = '';
        if($recommendType == 4 || $recommendType == 5 || $recommendType == 6 || $recommendType == 7){
            if(empty($data['member_level_ids']) || !is_array($data['member_level_ids'])){
                return json(['code'=>0, 'msg'=>'请选择会员等级']);
            }
            $memberLevelIds = implode(',', $data['member_level_ids']);
        }
        
        if(empty($data['product_id']) || empty($data['product_type'])){
            return json(['code'=>0, 'msg'=>'请选择推荐商品']);
        }
        
        if(!isset($data['sort']) || $data['sort'] === ''){
            $data['sort'] = 0;
        }
        
        // 处理商品ID，支持多个商品ID（逗号分隔）
        $productIds = explode(',', $data['product_id']);
        if (empty($productIds)) {
            return json(['code'=>0, 'msg'=>'请选择推荐商品']);
        }
        
        $successCount = 0;
        $existCount = 0;
        
        // 遍历所有商品ID，分别保存
        foreach ($productIds as $productId) {
            // 检查是否已存在
            $where = [
                ['aid', '=', aid],
                ['product_id', '=', $productId],
                ['product_type', '=', $data['product_type']],
                ['recommend_type', '=', $recommendType],
                ['status', '=', 1]
            ];
            
            // 根据推荐类型添加相应的查重条件
            if($recommendType == 1 || $recommendType == 3 || $recommendType == 5 || $recommendType == 7){
                $where[] = ['constitution_type', '=', $data['constitution_type']];
            }
            if($recommendType == 2 || $recommendType == 3 || $recommendType == 6 || $recommendType == 7){
                $where[] = ['min_score', '=', $minScore];
                $where[] = ['max_score', '=', $maxScore];
            }
            if($recommendType == 4 || $recommendType == 5 || $recommendType == 6 || $recommendType == 7){
                $where[] = ['member_level_ids', '=', $memberLevelIds];
            }
            
            if(bid > 0){
                $where[] = ['bid', '=', bid];
            }
            
            $exists = Db::name('shezhen_recommend_product')->where($where)->find();
            
            if($exists){
                $existCount++;
                continue; // 跳过已存在的商品
            }
            
            // 保存数据
            $saveData = [
                'aid' => aid,
                'bid' => bid,
                'constitution_type' => ($recommendType == 1 || $recommendType == 3 || $recommendType == 5 || $recommendType == 7) ? $data['constitution_type'] : '',
                'min_score' => ($recommendType == 2 || $recommendType == 3 || $recommendType == 6 || $recommendType == 7) ? $minScore : 0,
                'max_score' => ($recommendType == 2 || $recommendType == 3 || $recommendType == 6 || $recommendType == 7) ? $maxScore : 100,
                'recommend_type' => $recommendType,
                'product_id' => $productId,
                'product_type' => $data['product_type'],
                'member_level_ids' => $memberLevelIds,
                'sort' => intval($data['sort']),
                'status' => 1,
                'createtime' => time(),
                'updatetime' => time()
            ];
            
            $result = Db::name('shezhen_recommend_product')->insert($saveData);
            if($result){
                $successCount++;
            }
        }
        
        if($successCount > 0){
            $msg = '成功添加' . $successCount . '个商品';
            if($existCount > 0){
                $msg .= '，' . $existCount . '个商品已存在';
            }
            return json(['code'=>1, 'msg'=>$msg]);
        }else{
            if($existCount > 0){
                return json(['code'=>0, 'msg'=>'所选商品已全部存在，无需重复添加']);
            }
            return json(['code'=>0, 'msg'=>'添加失败，请重试']);
        }
    }
    
    // 2025-01-03 22:55:53,565-INFO-[SheZhen][deleteRecommend_001] 删除推荐商品
    public function deleteRecommend(){
        $ids = input('post.ids/a');
        
        if(empty($ids)){
            return json(['code'=>0, 'msg'=>'请选择要删除的记录']);
        }
        
        $where = [
            ['aid', '=', aid],
            ['id', 'in', $ids]
        ];
        if(bid > 0){
            $where[] = ['bid', '=', bid];
        }
        
        // 软删除
        $result = Db::name('shezhen_recommend_product')->where($where)->update(['status'=>0, 'updatetime'=>time()]);
        
        if($result){
            return json(['code'=>1, 'msg'=>'删除成功']);
        }else{
            return json(['code'=>0, 'msg'=>'删除失败，请重试']);
        }
    }
    
    // 2025-01-03 22:55:53,565-INFO-[SheZhen][updateRecommendSort_001] 更新推荐商品排序
    public function updateRecommendSort(){
        $id = input('post.id/d');
        $sort = input('post.sort/d');
        
        if(!$id){
            return json(['code'=>0, 'msg'=>'参数错误']);
        }
        
        $where = [
            ['aid', '=', aid],
            ['id', '=', $id]
        ];
        if(bid > 0){
            $where[] = ['bid', '=', bid];
        }
        
        $result = Db::name('shezhen_recommend_product')->where($where)->update(['sort'=>$sort, 'updatetime'=>time()]);
        
        if($result !== false){
            return json(['code'=>1, 'msg'=>'更新成功']);
        }else{
            return json(['code'=>0, 'msg'=>'更新失败，请重试']);
        }
    }
    
    // 2025-01-03 22:55:53,565-INFO-[SheZhen][getRecommendProducts_001] 获取推荐商品
    public function getRecommendProducts(){
        $constitutionType = input('param.constitution_type');
        $constitutionScore = intval(input('param.constitution_score')) ?: 0;
        
        if(!$constitutionType && !$constitutionScore){
            return json(['code'=>0, 'msg'=>'请提供体质类型或得分']);
        }
        
        // 获取舌诊设置
        $set = Db::name('shezhen_set')->where('aid', aid)->where('bid', bid)->find();
        if(!$set || $set['is_recommend_open'] != 1){
            return json(['code'=>0, 'msg'=>'推荐功能未开启']);
        }
        
        $where = [
            ['aid', '=', aid],
            ['status', '=', 1]
        ];
        if(bid > 0){
            $where[] = ['bid', '=', bid];
        }
        
        // 构建推荐商品查询条件
        $orWhere = [];
        
        // 1. 按体质类型推荐（recommend_type = 1）
        if($constitutionType){
            $orWhere[] = [
                ['recommend_type', '=', 1],
                ['constitution_type', '=', $constitutionType]
            ];
        }
        
        // 2. 按得分推荐（recommend_type = 2）
        if($constitutionScore > 0){
            $orWhere[] = [
                ['recommend_type', '=', 2],
                ['min_score', '<=', $constitutionScore],
                ['max_score', '>=', $constitutionScore]
            ];
        }
        
        // 3. 按体质+得分推荐（recommend_type = 3）
        if($constitutionType && $constitutionScore > 0){
            $orWhere[] = [
                ['recommend_type', '=', 3],
                ['constitution_type', '=', $constitutionType],
                ['min_score', '<=', $constitutionScore],
                ['max_score', '>=', $constitutionScore]
            ];
        }
        
        if(empty($orWhere)){
            return json(['code'=>0, 'msg'=>'无符合条件的推荐商品']);
        }
        
        $recommendLimit = isset($set['recommend_count']) ? intval($set['recommend_count']) : 3;
        
        // 使用whereOr查询多个条件
        $query = Db::name('shezhen_recommend_product')->where($where);
        
        // 添加OR条件组
        $query->where(function($query) use ($orWhere) {
            foreach($orWhere as $index => $conditions) {
                if($index == 0) {
                    $query->where($conditions);
                } else {
                    $query->whereOr($conditions);
                }
            }
        });
        
        $list = $query->order('sort desc, id desc')
            ->limit($recommendLimit)
            ->select()
            ->toArray();
        
        // 获取商品详情
        $productList = [];
        foreach($list as $item){
            if($item['product_type'] == 1){ // 商品
                $product = Db::name('shop_product')
                    ->where('id', $item['product_id'])
                    ->field('id,name,sell_price as price,pic')->find();
                
                if($product){
                    $product['type'] = 'product';
                    $product['url'] = '/pages/shop/product/detail?id=' . $product['id'];
                    $product['recommend_reason'] = $this->getRecommendReason($item, $constitutionType, $constitutionScore);
                    $productList[] = $product;
                }
            }else{ // 课程
                $course = Db::name('kecheng_list')
                    ->where('id', $item['product_id'])
                    ->field('id,name,price,pic')->find();
                
                if($course){
                    $course['type'] = 'course';
                    $course['url'] = '/pages/kecheng/detail?id=' . $course['id'];
                    $course['recommend_reason'] = $this->getRecommendReason($item, $constitutionType, $constitutionScore);
                    $productList[] = $course;
                }
            }
        }
        
        $data = [
            'recommend_title' => $set['recommend_title'] ?: '根据您的舌诊结果，为您推荐以下产品',
            'products' => $productList
        ];
        
        return json(['code'=>1, 'msg'=>'获取成功', 'data'=>$data]);
    }
    
    // 2025-01-03 22:55:53,565-INFO-[SheZhen][getRecommendReason_001] 获取推荐理由
    private function getRecommendReason($item, $constitutionType, $constitutionScore){
        switch($item['recommend_type']){
            case 1:
                return '适合' . $constitutionType . '体质';
            case 2:
                return '适合' . $item['min_score'] . '-' . $item['max_score'] . '分人群';
            case 3:
                return '适合' . $item['constitution_type'] . '体质（' . $item['min_score'] . '-' . $item['max_score'] . '分）';
            default:
                return '为您推荐';
        }
    }

    /**
     * 面诊记录列表
     * 2025-01-17 新增方法 - 面诊记录管理
     */
    public function faceIndex()
    {
        if (request()->isPost()) {
            $page = input('post.page/d', 1);
            $limit = input('post.limit/d', 10);
            $search = input('post.search', '');

            $where = [
                ['aid', '=', $this->aid],
                ['diagnosis_type', '=', 2] // 面诊类型
            ];

            if ($search) {
                $where[] = ['order_no|member_name', 'like', '%' . $search . '%'];
            }

            $total = Db::name('shezhen_record')
                ->alias('r')
                ->leftJoin('member m', 'r.mid = m.id')
                ->where($where)
                ->count();

            $list = Db::name('shezhen_record')
                ->alias('r')
                ->leftJoin('member m', 'r.mid = m.id')
                ->field('r.*, m.nickname as member_name')
                ->where($where)
                ->order('r.createtime desc')
                ->page($page, $limit)
                ->select();

            foreach ($list as &$item) {
                $item['createtime_text'] = date('Y-m-d H:i:s', $item['createtime']);
                $item['status_text'] = $item['status'] == 1 ? '已完成' : '处理中';
                $item['is_free_text'] = $item['is_free'] == 1 ? '免费' : '付费';
            }

            return json(['code' => 0, 'msg' => '', 'count' => $total, 'data' => $list]);
        }

        return view();
    }

    /**
     * 综合诊疗记录列表
     * 2025-01-17 新增方法 - 综合诊疗记录管理
     */
    public function comprehensiveIndex()
    {
        if (request()->isPost()) {
            $page = input('post.page/d', 1);
            $limit = input('post.limit/d', 10);
            $search = input('post.search', '');

            $where = [
                ['aid', '=', $this->aid],
                ['diagnosis_type', '=', 3] // 综合诊疗类型
            ];

            if ($search) {
                $where[] = ['order_no|member_name', 'like', '%' . $search . '%'];
            }

            $total = Db::name('shezhen_record')
                ->alias('r')
                ->leftJoin('member m', 'r.mid = m.id')
                ->where($where)
                ->count();

            $list = Db::name('shezhen_record')
                ->alias('r')
                ->leftJoin('member m', 'r.mid = m.id')
                ->field('r.*, m.nickname as member_name')
                ->where($where)
                ->order('r.createtime desc')
                ->page($page, $limit)
                ->select();

            foreach ($list as &$item) {
                $item['createtime_text'] = date('Y-m-d H:i:s', $item['createtime']);
                $item['status_text'] = $item['status'] == 1 ? '已完成' : '处理中';
                $item['is_free_text'] = $item['is_free'] == 1 ? '免费' : '付费';
                $item['images_count'] = 0;
                if ($item['tongue_image']) $item['images_count']++;
                if ($item['face_image']) $item['images_count']++;
                if ($item['sublingual_image']) $item['images_count']++;
            }

            return json(['code' => 0, 'msg' => '', 'count' => $total, 'data' => $list]);
        }

        return view();
    }
}