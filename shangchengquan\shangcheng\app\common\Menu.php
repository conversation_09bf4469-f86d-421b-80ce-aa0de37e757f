<?php
/**
 * 鲸犀商城 - 微信公众号小程序商城系统!
 * Copyright © 2020 鲸犀网络科技有限公司 保留所有权利
 * =========================================================
 * 版本：供应链版本
 * 授权主体：无限制版本
 * 授权域名：@@@
     
 * ----------------------------------------------
 * 您只能在商业授权范围内使用，不可二次转售、分发、分享、传播
 * 任何企业和个人不得对代码以任何目的任何形式的再发布
 * =========================================================
 */

namespace app\common;
use think\facade\Db;
class Menu
{
	//获取菜单数据
	public static function getdata($aid=0,$uid=0){
		$user = [];
		if($aid == 0){
			$platform = ['mp','wx','alipay','baidu','toutiao','qq','h5','app'];
		}else{
			$platform = explode(',',Db::name('admin')->where('id',$aid)->value('platform'));
		}
		if($uid > 0){
			$user = Db::name('admin_user')->where('id',$uid)->find();
			if($user['bid'] > 0){
				$isadmin = false;
				if($user['auth_type'] == 1){
					$user = Db::name('admin_user')->where('aid',$aid)->where('isadmin','>',0)->find();
				}
			}else{
				$isadmin = true;
			}
		}else{
			if($uid == -1){
				$isadmin = false;
				$user = Db::name('admin_user')->where('aid',$aid)->where('isadmin','>',0)->find();
			}else{
				$isadmin = true;
			}
		}
		$menudata = [];

		$shop_child = [];
		$shop_child[] = ['name'=>'商品管理','path'=>'ShopProduct/index','authdata'=>'ShopProduct/*,ShopCode/*'];
		$shop_child[] = ['name'=>'条形码录入','path'=>'BarcodeProduct/index','authdata'=>'BarcodeProduct/*'];
		$shop_child[] = ['name'=>'订单管理','path'=>'ShopOrder/index','authdata'=>'ShopOrder/*'];
		$shop_child[] = ['name'=>'打印订单列表','path'=>'ShopOrder/printList','authdata'=>'ShopOrder/*'];
        $shop_child[] = ['name'=>'退款申请','path'=>'ShopRefundOrder/index','authdata'=>'ShopRefundOrder/*'];
		$shop_child[] = ['name'=>'评价管理','path'=>'ShopComment/index','authdata'=>'ShopComment/*'];
		
		// // 添加进货管理和退货管理菜单
		// if($isadmin){
		// 	$shop_child[] = ['name'=>'进货管理','path'=>'ProductPurchase/index','authdata'=>'ProductPurchase/*'];
		// 	$shop_child[] = ['name'=>'退货管理','path'=>'ProductReturn/index','authdata'=>'ProductReturn/*'];
		// 	$shop_child[] = ['name'=>'进货统计','path'=>'AdminPurchaseStatistics/index','authdata'=>'AdminPurchaseStatistics/*'];
		// } else {
		// 	$shop_child[] = ['name'=>'向总平台进货','path'=>'ProductPurchase/index','authdata'=>'ProductPurchase/*'];
		// 	$shop_child[] = ['name'=>'退货申请','path'=>'ProductReturn/index','authdata'=>'ProductReturn/*'];
		// }
		
		if($isadmin){
			$shop_child[] = ['name'=>'商品分类','path'=>'ShopCategory/index','authdata'=>'ShopCategory/*'];
			$shop_child[] = ['name'=>'商品分组','path'=>'ShopGroup/index','authdata'=>'ShopGroup/*'];
		}else{
			$shop_child[] = ['name'=>'商品分类','path'=>'ShopCategory2/index','authdata'=>'ShopCategory2/*'];
		}
		$shop_child[] = ['name'=>'商品参数','path'=>'ShopParam/index','authdata'=>'ShopParam/*'];
		$shop_child[] = ['name'=>'商品服务','path'=>'ShopFuwu/index','authdata'=>'ShopFuwu/*'];
		if($isadmin){
			$shop_child[] = ['name'=>'商品海报','path'=>'ShopPoster/index','authdata'=>'ShopPoster/*'];
			$shop_child[] = ['name'=>'录入订单','path'=>'ShopOrderlr/index','authdata'=>'ShopOrderlr/*,ShopProduct/chooseproduct,ShopProduct/index,ShopProduct/getproduct,Member/index'];
		}
		$shop_child[] = ['name'=>'商品采集','path'=>'ShopTaobao/index','authdata'=>'ShopTaobao/*'];

		$shop_child[] = ['name'=>'销售统计','path'=>'ShopOrder/tongji','authdata'=>'ShopOrder/*'];
		if($isadmin){
            $shop_child[] = ['name'=>'系统设置','path'=>'ShopSet/index','authdata'=>'ShopSet/*'];
		}else{
            }

		$menudata['shop'] = ['name'=>'商城','fullname'=>'商城系统','icon'=>'my-icon my-icon-shop','child'=>$shop_child];
		if($isadmin){
			$member_child = [];
			$member_child[] = ['name'=>t('会员').'列表','path'=>'Member/index','authdata'=>'Member/index,Member/excel,Member/excel,Member/importexcel,Member/getplatform,Member/edit,Member/save,Member/del,Member/getcarddetail,Member/charts,Member/setst,Member/*,MemberStatistics/*'];
// 			$member_child[] = ['name'=>t('虚拟账号').'列表','path'=>'Member/ziindex','authdata'=>'Member/index,Member/excel,Member/excel,Member/importexcel,Member/getplatform,Member/edit,Member/save,Member/del,Member/getcarddetail,Member/charts,Member/setst'];
			$member_child[] = ['name'=>'充值','path'=>'Member/recharge','authdata'=>'Member/recharge','hide'=>true];
			$member_child[] = ['name'=>'加配资','path'=>'Member/addAllocation','authdata'=>'Member/addAllocation','hide'=>true];
			$member_child[] = ['name'=>'加积分','path'=>'Member/addscore','authdata'=>'Member/addscore','hide'=>true];
			$member_child[] = ['name'=>'加佣金','path'=>'Member/addcommission','authdata'=>'Member/addcommission','hide'=>true];
				$member_child[] = ['name'=>'加'.t('贡献值'),'path'=>'Member/addgongxianzhi','authdata'=>'Member/addgongxianzhi','hide'=>true];
			$member_child[] = ['name'=>'加'.t('现金券'),'path'=>'Member/addheijifen','authdata'=>'Member/addheijifen','hide'=>true];
			$member_child[] = ['name'=>'等级及分销','path'=>'MemberLevel/index','authdata'=>'MemberLevel/*'];
			$member_child[] = ['name'=>'升级申请记录','path'=>'MemberLevel/applyorder','authdata'=>'MemberLevel/*'];
            $member_child[] = ['name'=>t('会员').'关系图','path'=>'Member/charts','authdata'=>'Member/charts'];
			
			$member_child[] = ['name'=>t('会员').'标签','path'=>'MemberTag/index','authdata'=>'MemberTag/*'];
				
			
            $member_child[] = ['name' => '注册自定义', 'path' => 'RegisterForm/index', 'authdata' => 'RegisterForm/*'];
            
			$member_child[] = ['name'=>'分享海报','path'=>'MemberPoster/index','authdata'=>'MemberPoster/*'];
		
			
			$member_child[] = ['name'=>'团队分红','path'=>'teamfenhong','authdata'=>'teamfenhong','hide'=>true];
			$member_child[] = ['name'=>'股东分红','path'=>'gdfenhong','authdata'=>'gdfenhong','hide'=>true];
			$member_child[] = ['name'=>'区域分红','path'=>'areafenhong','authdata'=>'areafenhong','hide'=>true];

			$menudata['member'] = ['name'=>t('会员'),'fullname'=>t('会员').'管理','icon'=>'my-icon my-icon-member','child'=>$member_child];
		}

		if($isadmin){
			$finance_child = [];
				$finance_child[] = ['name'=>t('会员').'列表','path'=>'Membercaiwu/index','authdata'=>'Membecaiwu/*'];
			$finance_child[] = ['name'=>'全局明细','path'=>'Payorder/tongjiyue','authdata'=>'Payorder/*'];
			$finance_child[] = ['name'=>'每日收款明细','path'=>'Payorder/caiwu','authdata'=>'Payorder/*'];
				$finance_child[] = ['name'=>'每日货款明细','path'=>'Payorder/fahuomingxi','authdata'=>'Payorder/*'];
		//	$finance_child[] = ['name'=>'销售统计','path'=>'ShopOrder/tongji4','authdata'=>'ShopOrder/*'];
			$finance_child[] = ['name'=>'消费明细','path'=>'Payorder/index','authdata'=>'Payorder/*'];
			$finance_child[] = ['name'=>'用户明细','path'=>'BusinessMoney/memberindex','authdata'=>'BusinessMoney/*'];
			$finance_child[] = ['name'=>t('余额').'明细','path'=>'Money/moneylog','authdata'=>'Money/moneylog,Money/moneylogexcel,Money/moneylogsetst,Money/moneylogdel'];
// 			$finance_child[] = ['name'=>t('余额').'明细2','path'=>'Money/moneylog','authdata'=>'Money/moneylog,Money/moneylogexcel,Money/moneylogsetst,Money/moneylogdel'];
			
// 			$finance_child[] = ['name'=>t('创业值').'明细','path'=>'Money/chuangyelog','authdata'=>'Money/chuangyelog,Money/moneylog,Money/moneylogexcel,Money/moneylogsetst,Money/moneylogdel'];
			$finance_child[] = ['name'=>t('现金券').'明细','path'=>'Money/heijifenlog','authdata'=>'Money/heijifenlog,Money/moneylog,Money/moneylogexcel,Money/moneylogsetst,Money/moneylogdel'];
			$finance_child[] = ['name'=>'充值记录','path'=>'Money/rechargelog','authdata'=>'Money/rechargelog,Money/rechargelogexcel,Money/rechargelogdel'];
			$finance_child[] = ['name'=>t('余额').'提现','path'=>'Money/withdrawlog','authdata'=>'Money/*'];
			$finance_child[] = ['name'=>t('佣金').'记录','path'=>'Commission/record','authdata'=>'Commission/record'];
			$finance_child[] = ['name'=>t('佣金').'明细','path'=>'Commission/commissionlog','authdata'=>'Commission/commissionlog,Commission/commissionlogexcel,Commission/commissionlogdel'];
			$finance_child[] = ['name'=>t('佣金').'提现','path'=>'Commission/withdrawlog','authdata'=>'Commission/*'];
// 			$finance_child[] = ['name'=>t('销售分红发放').'记录','path'=>'Commission/sendxsjl','authdata'=>'Commission/*'];
			$finance_child[] = ['name'=>t('积分').'明细','path'=>'Score/scorelog','authdata'=>'Score/*'];
				$finance_child[] = ['name'=>t('积分').'转账明细','path'=>'Score/zhuanscorelog','authdata'=>'Score/*'];
					$finance_child[] = ['name'=>t('余额').t('转账').'明细','path'=>'Money/zhuangzhang','authdata'=>'Money/*'];
// 			$finance_child[] = ['name'=>t('虚拟账号积分').'明细','path'=>'Score/scoreduihuanfen','authdata'=>'Score/*'];
				
			// $finance_child[] = ['name'=>'红包明细','path'=>'Score/scorelogh','authdata'=>'Score/*'];
// 			$finance_child[] = ['name'=>'绿积分明细','path'=>'Score/scoreloglv','authdata'=>'Score/*'];
// 			$finance_child[] = ['name'=>'消费值明细','path'=>'Score/scorelogxiaofeizhi','authdata'=>'Score/*'];
// 			$finance_child[] = ['name'=>'打赏明细','path'=>'Score/scorelogdashang','authdata'=>'Score/*'];
		
// 			$finance_child[] = ['name'=>t('分红池').'明细','path'=>'Money/zijifhlog','authdata'=>'Money/*'];
// 			$finance_child[] = ['name'=>t('分享分红池').'明细','path'=>'Money/fxfhclog','authdata'=>'Money/*'];
// 			$finance_child[] = ['name'=>t('收益池').'明细','path'=>'Money/syclog','authdata'=>'Money/*'];
					
					//
			$finance_child[] = ['name'=>'买单记录','path'=>'Maidan/index','authdata'=>'Maidan/*'];
            // $finance_child[] = ['name'=>'贡献值明细','path'=>'Gxz/index','authdata'=>'Gxz/*'];
			$finance_child[] = ['name'=>'分红记录','path'=>'Commission/fenhonglog','authdata'=>'Commission/*'];
			
			}else{
                $finance_child = [];
                // 获取当前用户类型
                $userinfo = db('admin_user')->where('id', $uid)->find();
                $userType = isset($userinfo['type']) ? $userinfo['type'] : 'business';
				
                if($userType == 'business') {
                    // 商家菜单
                    $finance_child[] = ['name'=>'余额明细','path'=>'BusinessMoney/moneylog','authdata'=>'BusinessMoney/*'];
                    $finance_child[] = ['name'=>'用户明细','path'=>'BusinessMoney/memberindex','authdata'=>'BusinessMoney/*'];
                    $finance_child[] = ['name'=>'充值申请','path'=>'BusinessMoney/recharge','authdata'=>'BusinessMoney/*'];
                    $finance_child[] = ['name'=>'充值记录','path'=>'BusinessMoney/rechargelog','authdata'=>'BusinessMoney/*'];
                    $finance_child[] = ['name'=>'余额提现','path'=>'BusinessMoney/withdraw','authdata'=>'BusinessMoney/*'];
                    $finance_child[] = ['name'=>'提现记录','path'=>'BusinessMoney/withdrawlog','authdata'=>'BusinessMoney/*'];
                    $finance_child[] = ['name'=>'买单扣费','path'=>'BusinessMaidan/add','authdata'=>'BusinessMaidan/*'];
                    $finance_child[] = ['name'=>'买单记录','path'=>'BusinessMaidan/index','authdata'=>'BusinessMaidan/*'];
					$finance_child[] = ['name'=>'收款码','path'=>'BusinessMaidan/set','authdata'=>'BusinessMaidan/*'];
					
                } else if($userType == 'tuanzhang') {
                    // 团长菜单
                    $finance_child[] = ['name'=>'团长收益','path'=>'TuanzhangMoney/moneylog','authdata'=>'TuanzhangMoney/*'];
                    $finance_child[] = ['name'=>'提现申请','path'=>'TuanzhangMoney/withdraw','authdata'=>'TuanzhangMoney/*'];
                    $finance_child[] = ['name'=>'提现记录','path'=>'TuanzhangMoney/withdrawlog','authdata'=>'TuanzhangMoney/*'];
                    $finance_child[] = ['name'=>'订单记录','path'=>'TuanzhangOrder/index','authdata'=>'TuanzhangOrder/*'];
                }
			}
        $finance_child[] = ['name'=>'核销记录','path'=>'Hexiao/index','authdata'=>'Hexiao/*'];
       
        $finance_child[] = ['name'=>'发票管理','path'=>'Invoice/index','authdata'=>'Invoice/*'];
		$menudata['finance'] = ['name'=>'财务','fullname'=>'财务管理','icon'=>'my-icon my-icon-finance','child'=>$finance_child];
         
        // print_r($menudata);
        // exit();
		$yingxiao_child = [];
		$yingxiao_child[] = ['name'=>t('优惠券'),'path'=>'Coupon/index','authdata'=>'Coupon/*,ShopCategory/index,ShopCategory/choosecategory'];
		if($isadmin){
            $yingxiao_child[] = ['name'=>'注册赠送','path'=>'Member/registerGive','authdata'=>'Member/registerGive'];
            $yingxiao_child[] = ['name'=>'充值赠送','path'=>'Money/giveset','authdata'=>'Money/giveset'];
			$yingxiao_child[] = ['name'=>'购物满减','path'=>'Manjian/set','authdata'=>'Manjian/set'];
		}
		$yingxiao_child[] = ['name'=>'商品促销','path'=>'Cuxiao/index','authdata'=>'Cuxiao/*'];
		if($isadmin){
			$yingxiao_child[] = ['name'=>'购物返现','path'=>'Cashback/index','authdata'=>'Cashback/*'];
		}

		$yingxiao_collage = [];
		$yingxiao_collage[] = ['name'=>'商品管理','path'=>'CollageProduct/index','authdata'=>'CollageProduct/*,CollageCode/*'];
		$yingxiao_collage[] = ['name'=>'订单管理','path'=>'CollageOrder/index','authdata'=>'CollageOrder/*'];
		$yingxiao_collage[] = ['name'=>'拼团管理','path'=>'CollageTeam/index','authdata'=>'CollageTeam/*'];
		$yingxiao_collage[] = ['name'=>'评价管理','path'=>'CollageComment/index','authdata'=>'CollageComment/*'];
		if($isadmin){
			$yingxiao_collage[] = ['name'=>'商品分类','path'=>'CollageCategory/index','authdata'=>'CollageCategory/*'];
			$yingxiao_collage[] = ['name'=>'分享海报','path'=>'CollagePoster/index','authdata'=>'CollagePoster/*'];
			$yingxiao_collage[] = ['name'=>'系统设置','path'=>'CollageSet/index','authdata'=>'CollageSet/*'];
		}
		$yingxiao_child[] = ['name'=>'多人拼团','child'=>$yingxiao_collage];


		$yingxiao_kanjia = [];
		$yingxiao_kanjia[] = ['name'=>'商品管理','path'=>'KanjiaProduct/index','authdata'=>'KanjiaProduct/*,KanjiaCode/*'];
		$yingxiao_kanjia[] = ['name'=>'订单管理','path'=>'KanjiaOrder/index','authdata'=>'KanjiaOrder/*'];
		if($isadmin){
			$yingxiao_kanjia[] = ['name'=>'分享海报','path'=>'KanjiaPoster/index','authdata'=>'KanjiaPoster/*'];
			$yingxiao_kanjia[] = ['name'=>'系统设置','path'=>'KanjiaSet/index','authdata'=>'KanjiaSet/*'];
		}
		$yingxiao_child[] = ['name'=>'砍价活动','child'=>$yingxiao_kanjia];

		$yingxiao_seckill= [];
		//$yingxiao_seckill[] = ['name'=>'商品设置','path'=>'SeckillProset/index','authdata'=>'SeckillProset/*,ShopProduct/chooseproduct,ShopProduct/getproduct'];
		//$yingxiao_seckill[] = ['name'=>'秒杀列表','path'=>'SeckillList/index','authdata'=>'SeckillList/*'];
		$yingxiao_seckill[] = ['name'=>'商品列表','path'=>'SeckillProduct/index','authdata'=>'SeckillProduct/*,SeckillCode/*'];
		$yingxiao_seckill[] = ['name'=>'订单列表','path'=>'SeckillOrder/index','authdata'=>'SeckillOrder/*'];
		$yingxiao_seckill[] = ['name'=>'用户评价','path'=>'SeckillComment/index','authdata'=>'SeckillComment/*'];
		if($isadmin){
			$yingxiao_seckill[] = ['name'=>'秒杀设置','path'=>'SeckillSet/index','authdata'=>'SeckillSet/*'];
		}
		$yingxiao_child[] = ['name'=>'整点秒杀','child'=>$yingxiao_seckill];

		$yingxiao_tuangou = [];
		$yingxiao_tuangou[] = ['name'=>'商品管理','path'=>'TuangouProduct/index','authdata'=>'TuangouProduct/*,TuangouCode/*'];
		$yingxiao_tuangou[] = ['name'=>'订单管理','path'=>'TuangouOrder/index','authdata'=>'TuangouOrder/*'];
		$yingxiao_tuangou[] = ['name'=>'评价管理','path'=>'TuangouComment/index','authdata'=>'TuangouComment/*'];
		$yingxiao_tuangou[] = ['name'=>'商品分类','path'=>'TuangouCategory/index','authdata'=>'TuangouCategory/*'];
		if($isadmin){
			$yingxiao_tuangou[] = ['name'=>'分享海报','path'=>'TuangouPoster/index','authdata'=>'TuangouPoster/*'];
			$yingxiao_tuangou[] = ['name'=>'系统设置','path'=>'TuangouSet/index','authdata'=>'TuangouSet/*'];
		}
		$yingxiao_child[] = ['name'=>'团购活动','child'=>$yingxiao_tuangou];

		if($isadmin){
			$yingxiao_scoreshop = [];
			$yingxiao_scoreshop[] = ['name'=>'商品管理','path'=>'ScoreshopProduct/index','authdata'=>'ScoreshopProduct/*,ScoreshopCode/*'];
			$yingxiao_scoreshop[] = ['name'=>'兑换记录','path'=>'ScoreshopOrder/index','authdata'=>'ScoreshopOrder/*'];
			$yingxiao_scoreshop[] = ['name'=>'商品分类','path'=>'ScoreshopCategory/index','authdata'=>'ScoreshopCategory/*'];
			$yingxiao_scoreshop[] = ['name'=>'分享海报','path'=>'ScoreshopPoster/index','authdata'=>'ScoreshopPoster/*'];
			$yingxiao_scoreshop[] = ['name'=>'系统设置','path'=>'ScoreshopSet/index','authdata'=>'ScoreshopSet/*'];
			$yingxiao_child[] = ['name'=>t('积分').'兑换','child'=>$yingxiao_scoreshop];

// 			$yingxiao_hongbao = [];
// 			$yingxiao_hongbao[] = ['name'=>'活动列表','path'=>'Hongbao/index','authdata'=>'Hongbao/*'];
// 			$yingxiao_hongbao[] = ['name'=>'领取记录','path'=>'Hongbao/record','authdata'=>'Hongbao/*'];
// 			$yingxiao_child[] = ['name'=>'微信红包','child'=>$yingxiao_hongbao];

		}
		if($isadmin){
			$yingxiao_choujiang = [];
			$yingxiao_choujiang[] = ['name'=>'活动列表','path'=>'Choujiang/index','authdata'=>'Choujiang/*'];
			$yingxiao_choujiang[] = ['name'=>'抽奖记录','path'=>'Choujiang/record','authdata'=>'Choujiang/*'];
			$yingxiao_child[] = ['name'=>'抽奖活动','child'=>$yingxiao_choujiang];
		}
        $lucky_collage = [];
		$lucky_collage[] = ['name'=>'商品管理','path'=>'LuckyCollageProduct/index','authdata'=>'LuckyCollageProduct/*,LuckyCollageCode/*'];
		$lucky_collage[] = ['name'=>'订单管理','path'=>'LuckyCollageOrder/index','authdata'=>'LuckyCollageOrder/*'];
		$lucky_collage[] = ['name'=>'拼团管理','path'=>'LuckyCollageTeam/index','authdata'=>'LuckyCollageTeam/*'];
		$lucky_collage[] = ['name'=>'评价管理','path'=>'LuckyCollageComment/index','authdata'=>'LuckyCollageComment/*'];
		if($isadmin){
			$lucky_collage[] = ['name'=>'商品分类','path'=>'LuckyCollageCategory/index','authdata'=>'LuckyCollageCategory/*'];
			$lucky_collage[] = ['name'=>'分享海报','path'=>'LuckyCollagePoster/index','authdata'=>'LuckyCollagePoster/*'];
			$lucky_collage[] = ['name'=>'机器人管理','path'=>'LuckyCollageJiqiren/index','authdata'=>'LuckyCollageJiqiren/*'];
			$lucky_collage[] = ['name'=>'系统设置','path'=>'LuckyCollageSet/index','authdata'=>'LuckyCollageSet/*'];
		}
		$yingxiao_child[] = ['name'=>'幸运拼团','child'=>$lucky_collage];

		$short_video= [];
		$short_video[] = ['name'=>'分类列表','path'=>'ShortvideoCategory/index','authdata'=>'ShortvideoCategory/*'];
		$short_video[] = ['name'=>'视频列表','path'=>'Shortvideo/index','authdata'=>'Shortvideo/*'];
		$short_video[] = ['name'=>'评论列表','path'=>'ShortvideoComment/index','authdata'=>'ShortvideoComment/*'];
		$short_video[] = ['name'=>'笔记回评','path'=>'ShortvideoCommentReply/index','authdata'=>'ShortvideoCommentReply/*'];
		if($isadmin){
			$short_video[] = ['name'=>'海报设置','path'=>'ShortvideoPoster/index','authdata'=>'ShortvideoPoster/*'];
			$short_video[] = ['name'=>'系统设置','path'=>'ShortvideoSysset/index','authdata'=>'ShortvideoSysset/*'];
		}
		$yingxiao_child[] = ['name'=>'短视频','child'=>$short_video];
        $menudata['yingxiao'] = ['name'=>'营销','fullname'=>'营销活动','icon'=>'my-icon my-icon-yingxiao','child'=>$yingxiao_child];

		$component_child = [];
		if($isadmin){
			$component_business = [];
			$component_business[] = ['name'=>'商户管理','path'=>'Business/index','authdata'=>'Business/*,BusinessFreight/*'];
			$component_business[] = ['name'=>'商户分类','path'=>'Business/category','authdata'=>'Business/*'];
			$component_business[] = ['name'=>'商户费率','path'=>'Business/feilv','authdata'=>'Business/*'];
			$component_business[] = ['name'=>'商家海报','path'=>'BusinessPoster/index','authdata'=>'BusinessPoster/*'];
			$component_business[] = ['name'=>'商品管理','path'=>'ShopProduct/index&showtype=2','authdata'=>'ShopProduct/*'];

			
			$component_business[] = ['name'=>'商品管理','path'=>'ShopProduct/index&showtype=2','authdata'=>'ShopProduct/*'];

			$component_business[] = ['name'=>'商户订单','path'=>'ShopOrder/index&showtype=2','authdata'=>'ShopOrder/*'];
			$component_business[] = ['name'=>'商户评价','path'=>'BusinessComment/index&showtype=2','authdata'=>'BusinessComment/*'];
			$component_business[] = ['name'=>'拼团商品','path'=>'CollageProduct/index&showtype=2','authdata'=>'CollageProduct/*'];
			$component_business[] = ['name'=>'砍价商品','path'=>'KanjiaProduct/index&showtype=2','authdata'=>'KanjiaProduct/*'];
			$component_business[] = ['name'=>'秒杀商品','path'=>'SeckillProduct/index&showtype=2','authdata'=>'SeckillProduct/*'];
			$component_business[] = ['name'=>'团购商品','path'=>'TuangouProduct/index&showtype=2','authdata'=>'TuangouProduct/*'];
			$component_business[] = ['name'=>'服务商品','path'=>'YuyueList/index&showtype=2','authdata'=>'YuyueList/*'];
            $component_business[] = ['name'=>'周期购商品','path'=>'CycleProduct/index&showtype=2','authdata'=>'CycleProduct/*'];
			$component_business[] = ['name'=>'文章列表','path'=>'Article/index&showtype=2','authdata'=>'Article/*'];
			$component_business[] = ['name'=>'短视频列表','path'=>'Shortvideo/index&showtype=2','authdata'=>'Shortvideo/*'];
			if(false){}else{
				$component_business[] = ['name'=>t('自定义表单'),'path'=>'Form/index&showtype=2','authdata'=>'Form/*'];
			}
			$component_business[] = ['name'=>t('余额').'明细','path'=>'BusinessMoney/moneylog','authdata'=>'BusinessMoney/moneylog,BusinessMoney/moneylogexcel,BusinessMoney/moneylogsetst,BusinessMoney/moneylogdel'];
			
			$component_business[] = ['name'=>'充值审核','path'=>'BusinessMoney/rechargelog','authdata'=>'BusinessMoney/*'];
			$component_business[] = ['name'=>'提现记录','path'=>'BusinessMoney/withdrawlog','authdata'=>'BusinessMoney/*'];
			$component_business[] = ['name'=>'通知公告','path'=>'BusinessNotice/index','authdata'=>'BusinessNotice/*'];
			$component_business[] = ['name'=>'默认导航','path'=>'DesignerMenu/business','authdata'=>'DesignerMenu/*'];
			$component_business[] = ['name'=>'系统设置','path'=>'Business/sysset','authdata'=>'Business/sysset'];


			$component_child[] = ['name'=>'商户','child'=>$component_business];
            //$menudata['business'] = ['name'=>'商户','fullname'=>'商户菜单','icon'=>'my-icon my-icon-yingxiao','child'=>$component_business];
			if($uid == 0){
			
				
				$component_business2[] = ['name'=>'商品分类','path'=>'ShopCategory2/index','authdata'=>'ShopCategory2/*','hide'=>true];
				$component_business2[] = ['name'=>'余额明细','path'=>'BusinessMoney/moneylog','authdata'=>'BusinessMoney/*','hide'=>true];
				$component_business2[] = ['name'=>'余额提现','path'=>'BusinessMoney/withdraw','authdata'=>'BusinessMoney/*','hide'=>true];
				$component_business2[] = ['name'=>'充值申请','path'=>'BusinessMoney/recharge','authdata'=>'BusinessMoney/*','hide'=>true];
				$component_business2[] = ['name'=>'充值记录','path'=>'BusinessMoney/rechargelog','authdata'=>'BusinessMoney/*','hide'=>true];
				$component_business2[] = ['name'=>'提现记录','path'=>'BusinessMoney/withdrawlog','authdata'=>'BusinessMoney/*','hide'=>true];
				$component_business2[] = ['name'=>'买单扣费','path'=>'BusinessMaidan/add','authdata'=>'BusinessMaidan/*','hide'=>true];
				$component_business2[] = ['name'=>'买单记录','path'=>'BusinessMaidan/index','authdata'=>'BusinessMaidan/*','hide'=>true];
				$component_business2[] = ['name'=>'店铺评价','path'=>'BusinessComment/index','authdata'=>'BusinessComment/*','hide'=>true];
                $component_child[] = ['name'=>'商户后台','child'=>$component_business2,'hide'=>true];

			}
			$maidan_child = [];
			$maidan_child[] = ['name'=>'买单扣费','path'=>'Maidan/add','authdata'=>'Maidan/*'];
			$maidan_child[] = ['name'=>'买单记录','path'=>'Maidan/index','authdata'=>'Maidan/*'];
			$maidan_child[] = ['name'=>'聚合收款码','path'=>'Maidan/set','authdata'=>'Maidan/set'];
			$component_child[] = ['name'=>'买单收款','child'=>$maidan_child];
		// 进货退货管理
		$component_purchase = [];
		if($isadmin){
			$component_purchase[] = ['name'=>'进货管理','path'=>'ProductPurchase/index','authdata'=>'ProductPurchase/*'];
			$component_purchase[] = ['name'=>'退货管理','path'=>'ProductReturn/index','authdata'=>'ProductReturn/*'];
			$component_purchase[] = ['name'=>'进货统计','path'=>'AdminPurchaseStatistics/index','authdata'=>'AdminPurchaseStatistics/*'];
			$component_purchase[] = ['name'=>'进货设置','path'=>'ProductPurchaseSet/index','authdata'=>'ProductPurchaseSet/*'];
		} else {
			$component_purchase[] = ['name'=>'向总平台进货','path'=>'ProductPurchase/index','authdata'=>'ProductPurchase/*'];
			$component_purchase[] = ['name'=>'退货申请','path'=>'ProductReturn/index','authdata'=>'ProductReturn/*'];
			$component_purchase[] = ['name'=>'进货设置','path'=>'ProductPurchaseSet/index','authdata'=>'ProductPurchaseSet/*'];
		}
		$component_child[] = ['name'=>'进货退货管理','child'=>$component_purchase];
	

			// 添加股权池功能菜单
			$component_equity_pool = [];
			$component_equity_pool[] = ['name'=>'股权池概览','path'=>'EquityPool/index','authdata'=>'EquityPool/*'];
			$component_equity_pool[] = ['name'=>'股权明细','path'=>'EquityPool/detail','authdata'=>'EquityPool/*'];
			$component_equity_pool[] = ['name'=>'操作记录','path'=>'EquityPool/log','authdata'=>'EquityPool/*'];
			$component_equity_pool[] = ['name'=>'股权池设置','path'=>'EquityPool/set','authdata'=>'EquityPool/*'];
			$component_child[] = ['name'=>'股权池','child'=>$component_equity_pool];
			
				// 在这里添加招聘管理模块
				$component_zhaopin = [];
				$component_zhaopin[] = ['name'=>'数据统计','path'=>'Zhaopin/statistics','authdata'=>'Zhaopin/statistics'];
				$component_zhaopin[] = ['name'=>'企业管理','path'=>'Zhaopin/company','authdata'=>'Zhaopin/*,Zhaopin/company,ApiZhaopin/saveCompany,ApiZhaopin/delCompany'];
				$component_zhaopin[] = ['name'=>'职位管理','path'=>'Zhaopin/job','authdata'=>'Zhaopin/job,Zhaopin/job_edit,Zhaopin/jobList,Zhaopin/saveJob,Zhaopin/delJob'];
				$component_zhaopin[] = ['name'=>'职位分类','path'=>'Zhaopin/category','authdata'=>'Zhaopin/category,ApiZhaopin/saveCategory,ApiZhaopin/delCategory'];
					$component_zhaopin[] = ['name'=>'职位类型','path'=>'Zhaopin/type','authdata'=>'Zhaopin/type,ApiZhaopin/typeList,ApiZhaopin/type_edit,ApiZhaopin/saveType'];
				$component_zhaopin[] = ['name'=>'标签类型','path'=>'Zhaopin/option_types','authdata'=>'Zhaopin/option_types,Zhaopin/option_types_edit,Zhaopin/optionTypesList,Zhaopin/saveOptionType,Zhaopin/delOptionType,Zhaopin/setOptionTypeStatus'];
				$component_zhaopin[] = ['name'=>'标签管理','path'=>'Zhaopin/options','authdata'=>'Zhaopin/options,ApiZhaopin/saveOptions,ApiZhaopin/delOptions'];
				$component_zhaopin[] = ['name'=>'求职会员','path'=>'Zhaopin/Member','authdata'=>'Zhaopin/Member,Zhaopin/excel,Zhaopin/excel,Zhaopin/importexcel,Zhaopin/getplatform,Zhaopin/edit,Zhaopin/save,Zhaopin/del,Zhaopin/getcarddetail,Zhaopin/charts,Zhaopin/setst'];
				$component_zhaopin[] = ['name'=>t('信用分').'明细','path'=>'zhaopin/xinyongfenlog','authdata'=>'zhaopin/xinyongfenlog,zhaopin/xinyongfenlogexcel,zhaopin/xinyongfenlogsetst,zhaopin/xinyongfenlogdel'];
				$component_zhaopin[] = ['name'=>'投递记录','path'=>'Zhaopin/apply','authdata'=>'Zhaopin/apply,ApiZhaopin/updateApplyStatus'];
				$component_zhaopin[] = ['name'=>'入职记录','path'=>'Zhaopin/applyruzhi','authdata'=>'Zhaopin/applyruzhi,ApiZhaopin/updateApplyStatus'];
				$component_zhaopin[] = ['name'=>'面试管理','path'=>'Zhaopin/interview','authdata'=>'Zhaopin/interview,ApiZhaopin/saveInterview,ApiZhaopin/saveInterviewResult'];
				$component_zhaopin[] = ['name'=>'招聘设置','path'=>'Zhaopin/setting','authdata'=>'Zhaopin/setting'];
				$component_zhaopin[] = ['name'=>'简历管理','path'=>'Zhaopin/resume','authdata'=>'Zhaopin/resume,Zhaopin/resumeList,Zhaopin/resume_edit,Zhaopin/saveResume,Zhaopin/delResume'];
				$component_child[] = ['name'=>'招聘管理','child'=>$component_zhaopin];
	





				//门店相关插件
				$component_mendian = [];
				$component_mendian[] = ['name'=>'门店管理','path'=>'Mendian/index','authdata'=>'Mendian/*'];
				$component_mendian[] = ['name'=>'门店分组','path'=>'MendianGroup/index','authdata'=>'MendianGroup/*'];
				$component_mendian[] = ['name'=>'门店等级','path'=>'MendianLevel/index','authdata'=>'MendianLevel/*'];
				$component_mendian[] = ['name'=>'门店管理设置','path'=>'MendianSet/index','authdata'=>'MendianSet/*'];

				$component_child[] = ['name'=>'门店管理','child'=>$component_mendian];
				$component_tuanzhang = [];
		
			$component_tuanzhang[] = ['name'=>'团长管理','path'=>'Tuanzhang/index','authdata'=>'Tuanzhang/*,TuanzhangFreight/*'];
			$component_tuanzhang[] = ['name'=>'团长分类','path'=>'Tuanzhang/category','authdata'=>'Tuanzhang/*'];
			$component_tuanzhang[] = ['name'=>'团管理','path'=>'Tuanzhang/tuanguanli','authdata'=>'Tuanzhang/*'];
			$component_tuanzhang[] = ['name'=>'商品管理','path'=>'TuanzhangProduct/index','authdata'=>'TuanzhangProduct/*'];
				$component_tuanzhang[] = ['name'=>'订单管理','path'=>'TuanzhangOrder/index','authdata'=>'TuanzhangOrder/*'];
// 			$component_tuanzhang[] = ['name'=>'商品管理','path'=>'ShopProduct/index&showtype=2','authdata'=>'ShopProduct/*'];
// 			$component_tuanzhang[] = ['name'=>'团长订单','path'=>'ShopOrder/index&showtype=2','authdata'=>'ShopOrder/*'];
// 			$component_tuanzhang[] = ['name'=>'团长评价','path'=>'TuanzhangComment/index&showtype=2','authdata'=>'TuanzhangComment/*'];
			
		
		
// 			$component_tuanzhang[] = ['name'=>t('余额').'明细','path'=>'TuanzhangMoney/moneylog','authdata'=>'TuanzhangMoney/moneylog,BusinessMoney/moneylogexcel,BusinessMoney/moneylogsetst,BusinessMoney/moneylogdel'];
			
			$component_tuanzhang[] = ['name'=>'提现记录','path'=>'TuanzhangMoney/withdrawlog','authdata'=>'TuanzhangMoney/*'];
// 			$component_business[] = ['name'=>'通知公告','path'=>'TuanzhangNotice/index','authdata'=>'TuanzhangNotice/*'];
// 			$component_business[] = ['name'=>'默认导航','path'=>'DesignerMenu/business','authdata'=>'DesignerMenu/*'];
	$component_tuanzhang[] = ['name'=>'分享海报','path'=>'TuanzhangPoster/index','authdata'=>'TuanzhangPoster/*'];
			$component_tuanzhang[] = ['name'=>'系统设置','path'=>'Tuanzhang/sysset','authdata'=>'Tuanzhang/sysset'];


			$component_child[] = ['name'=>'团长','child'=>$component_tuanzhang];
			 
			//多城市拓展菜单项
			$component_area[] = ['name'=>'基础设置','path'=>'Area/config','authdata'=>'Area/*'];
        	$component_area[] = ['name'=>'装修模板','path'=>'Area/templates','authdata'=>'Area/*'];
        	$component_area[] = ['name'=>'模板配置','path'=>'Area/settings','authdata'=>'Area/*'];
        	$component_area[] = ['name'=>'默认模板','path'=>'Area/defaulttemplate','authdata'=>'Area/*'];
        
        	$component_child[] = ['name'=>'多城市','child'=>$component_area];
        	
        	// 省市区街道管理
        	if($isadmin){
        		$component_area_manage = [];
        		$component_area_manage[] = ['name'=>'地区管理','path'=>'Area/areamanage','authdata'=>'Area/*'];
        		$component_area_manage[] = ['name'=>'地区搜索','path'=>'Area/areasearch','authdata'=>'Area/*'];
        		$component_area_manage[] = ['name'=>'批量导入','path'=>'Area/areaimport','authdata'=>'Area/*'];
        		$component_area_manage[] = ['name'=>'地区统计','path'=>'Area/areastats','authdata'=>'Area/*'];
        		$component_child[] = ['name'=>'地址库管理','child'=>$component_area_manage];
        	}
        	
				//多城市拓展菜单项
// 			$component_shangjia[] = ['name'=>'基础设置','path'=>'shangjia/config','authdata'=>'shangjia/*'];
        
        
//         	$component_child[] = ['name'=>'商户充值','child'=>$component_shangjia];
			
			
			$sweepstakes_settings_child = [
				['name'=>'抽奖列表', 'path'=>'SweepstakesList/index', 'authdata'=>'SweepstakesList/*'],
				['name'=>'抽奖商品', 'path'=>'SweepstakesList/list', 'authdata'=>'SweepstakesList/*'],
				['name'=>'抽奖订单', 'path'=>'SweepstakesOrder/index', 'authdata'=>'SweepstakesOrder/*'],
				['name'=>'抽奖设置', 'path'=>'SweepstakesSet/index', 'authdata'=>'SweepstakesSet/*'],
			];

			$component_child[] = ['name'=>'欢乐抽奖', 'child'=> $sweepstakes_settings_child];

// ... existing code ...

		}
		
		// 仓库管理
		$component_warehouse = [];
		$component_warehouse[] = ['name'=>'商品列表','path'=>'WarehouseProduct/index','authdata'=>'WarehouseProduct/*'];

		$component_warehouse[] = ['name'=>'入库单','path'=>'WarehouseInbound/index','authdata'=>'WarehouseProduct/*,WarehouseInbound/*'];
		$component_warehouse[] = ['name'=>'出库单','path'=>'WarehouseOutbound/index','authdata'=>'WarehouseProduct/*,WarehouseOutbound/*'];
		$component_warehouse[] = ['name'=>'组装单','path'=>'WarehouseAssemble/index','authdata'=>'WarehouseProduct/*,WarehouseAssemble/*'];
		$component_warehouse[] = ['name'=>'拆卸单','path'=>'WarehouseSplit/index','authdata'=>'WarehouseProduct/*,WarehouseSplit/*'];
		$component_warehouse[] = ['name'=>'库存盘点','path'=>'WarehouseInventory/index','authdata'=>'WarehouseProduct/*,WarehouseInventory/*'];
		$component_warehouse[] = ['name'=>'模板管理','path'=>'WarehouseTemplate/index','authdata'=>'WarehouseProduct/*,WarehouseTemplate/*'];
		$component_warehouse[] = ['name'=>'设置','path'=>'WarehouseSet/index','authdata'=>'WarehouseProduct/*,WarehouseSet/*'];

		$component_child[] = ['name'=>'仓库管理','child'=>$component_warehouse];
		
	
			// 保险管理
			$component_insurance = [];
			$component_insurance[] = ['name'=>'保险订单','path'=>'insurance_order/index','authdata'=>'InsuranceOrder/*,insurance_setting/*'];
			$component_insurance[] = ['name'=>'保险设置','path'=>'insurance_setting/index','authdata'=>'InsuranceSettingr/*,insurance_setting/*'];

			$component_child[] = ['name'=>'保险管理','child'=>$component_insurance];
			
			// Coze API管理
			$component_coze = [];
			$component_coze[] = ['name'=>'API配置','path'=>'coze/config','authdata'=>'Coze/*,coze/config'];
			$component_coze[] = ['name'=>'API日志','path'=>'coze/logs','authdata'=>'Coze/*,coze/logs'];
			$component_coze[] = ['name'=>'响应日志','path'=>'coze/responseLogs','authdata'=>'Coze/*,coze/responseLogs'];
			$component_coze[] = ['name'=>'对话列表','path'=>'coze/conversations','authdata'=>'Coze/*,coze/conversations'];
			$component_coze[] = ['name'=>'演示页面','path'=>'coze/demo','authdata'=>'Coze/*,coze/demo'];

			$component_child[] = ['name'=>'Coze API','child'=>$component_coze];
		
		
		// 直播管理
		$component_liveWeb = [];
		$component_liveWeb[] = ['name'=>'直播列表','path'=>'LiveWeb/index','authdata'=>'LiveWeb/*,LiveWebProduct/*,LiveWebProduct/*,LiveWebShare/*'];
		$component_liveWeb[] = ['name'=>'直播分组','path'=>'live_web_share_group/index','authdata'=>'live_web_share_group/*'];
		$component_liveWeb[] = ['name'=>'海报设置','path'=>'LiveWebPoster/index','authdata'=>'LiveWebPoster/*'];
		$component_liveWeb[] = ['name'=>'观看记录','path'=>'LiveWeb/watch_log','authdata'=>'LiveWeb/watch_log'];

		$component_child[] = ['name'=>'直播管理','child'=>$component_liveWeb];
		
		// BPV管理
		$component_bpv = [];
		$component_bpv[] = ['name'=>'BPV概览','path'=>'Bpv/index','authdata'=>'Bpv/*'];
		$component_bpv[] = ['name'=>'基础设置','path'=>'Bpv/setting','authdata'=>'Bpv/*'];
		$component_bpv[] = ['name'=>'兑换记录','path'=>'Bpv/exchangeList','authdata'=>'Bpv/*'];
		$component_bpv[] = ['name'=>'提现记录','path'=>'Bpv/cashoutList','authdata'=>'Bpv/*'];
		$component_bpv[] = ['name'=>'分红记录','path'=>'Bpv/profitList','authdata'=>'Bpv/*'];

		$component_child[] = ['name'=>'BPV管理','child'=>$component_bpv];

		// 单数奖管理
		$component_danshujiang = [];
		$component_danshujiang[] = ['name'=>'单数奖列表','path'=>'Danshujiang/index','authdata'=>'Danshujiang/*'];
		$component_danshujiang[] = ['name'=>'奖励记录管理','path'=>'Danshujiang/record','authdata'=>'Danshujiang/*'];
		$component_child[] = ['name'=>'单数奖管理','child'=>$component_danshujiang];

		// 团队业绩奖励管理
		$component_tuandui = [];
		$component_tuandui[] = ['name'=>'奖励活动管理','path'=>'Tuandui/index','authdata'=>'Tuandui/*'];
		$component_tuandui[] = ['name'=>'奖励记录管理','path'=>'Tuandui/record','authdata'=>'Tuandui/*'];
		$component_child[] = ['name'=>'业绩奖励','child'=>$component_tuandui];

		// 添加等级推荐奖励菜单
		$component_levelreward = [];
		$component_levelreward[] = ['name'=>'等级奖励设置','path'=>'Levelreward/index','authdata'=>'Levelreward/*'];
		$component_levelreward[] = ['name'=>'奖励记录管理','path'=>'Levelreward/record','authdata'=>'Levelreward/*'];
		$component_levelreward[] = ['name'=>'系统设置','path'=>'Levelreward/set','authdata'=>'Levelreward/*'];
		$component_child[] = ['name'=>'等级推荐奖励','child'=>$component_levelreward];
	// 单数奖管理
		$component_paimingjiang = [];
		$component_paimingjiang[] = ['name'=>'排名奖励列表','path'=>'Paimingjiang/index','authdata'=>'Paimingjiang/*'];
		$component_paimingjiang[] = ['name'=>'奖励记录管理','path'=>'Paimingjiang/record','authdata'=>'Paimingjiang/*'];
		$component_child[] = ['name'=>'排名奖励管理','child'=>$component_paimingjiang];

				// 场馆管理
		$component_venues = [];
		$component_venues[] = ['name'=>'场馆列表','path'=>'Venues/index','authdata'=>'Venues/*'];

		if($isadmin){
			$component_venues[] = ['name'=>'场馆类别','path'=>'VenuesCatalog/index','authdata'=>'Venues/*,VenuesCatalog/*'];
			$component_venues[] = ['name'=>'场馆服务','path'=>'VenuesService/index','authdata'=>'Venues/*,VenuesService/*'];
			$component_venues[] = ['name'=>'节假日管理','path'=>'VenuesHoliday/index','authdata'=>'Venues/*,VenuesHoliday/*'];
		}
		$component_venues[] = ['name'=>'场地管理','path'=>'VenuesField/index','authdata'=>'Venues/*,VenuesField/*'];
		$component_venues[] = ['name'=>'包场管理','path'=>'VenuesPackage/index','authdata'=>'Venues/*,VenuesPackage/*'];		
		$component_venues[] = ['name'=>'订单管理','path'=>'VenuesOrder/index','authdata'=>'Venues/*,VenuesOrder/*'];
		$component_venues[] = ['name'=>'评价管理','path'=>'VenuesComment/index','authdata'=>'Venues/*,VenuesComment/*'];


		$component_venues[] = ['name'=>'活动管理','path'=>'VenuesAppointment/index','authdata'=>'Venues/*,VenuesAppointment/*'];
		
		if($isadmin){
			$component_venues[] = ['name'=>'订场配置','path'=>'Venues/set','authdata'=>'Venues/*'];
		}

		$component_child[] = ['name'=>'场馆管理','child'=>$component_venues];

			// 演出管理
		$component_theater = [];
		$component_theater[] = ['name'=>'场馆列表','path'=>'TheaterVenues/index','authdata'=>'TheaterVenues/*'];
		$component_theater[] = ['name'=>'演出列表','path'=>'TheaterEpisode/index','authdata'=>'TheaterEpisode/*'];
		$component_theater[] = ['name'=>'订单管理','path'=>'TheaterOrder/index','authdata'=>'TheaterOrder/*'];
		$component_theater[] = ['name'=>'创建订单','path'=>'TheaterOrder/placeOrder','authdata'=>'TheaterOrder/*'];
		$component_theater[] = ['name'=>'数据统计','path'=>'TheaterEpisode/statistics','authdata'=>'TheaterEpisode/*'];

		$component_child[] = ['name'=>'演出管理','child'=>$component_theater];

		
		$component_article = [];
		$component_article[] = ['name'=>'文章列表','path'=>'Article/index','authdata'=>'Article/*'];
		$component_article[] = ['name'=>'文章分类','path'=>'ArticleCategory/index','authdata'=>'ArticleCategory/*'];
		$component_article[] = ['name'=>'评论列表','path'=>'ArticlePinglun/index','authdata'=>'ArticlePinglun/*'];
		$component_article[] = ['name'=>'文章回评','path'=>'ArticlePlreply/index','authdata'=>'ArticlePlreply/*'];
		$component_article[] = ['name'=>'系统设置','path'=>'ArticleSet/set','authdata'=>'ArticleSet/*'];
		$component_child[] = ['name'=>'文章管理','child'=>$component_article];

			$component_huodong_baoming=[];
			$component_huodong_baoming[] = ['name'=>'活动报名类型','path'=>'HuodongBaomingCategory/index','authdata'=>'HuodongBaomingCategory/*'];
			$component_huodong_baoming[] = ['name'=>'活动报名列表','path'=>'HuodongBaomingList/index','authdata'=>'HuodongBaomingList/*'];
			$component_huodong_baoming[] = ['name'=>'活动报名订单','path'=>'HuodongBaomingOrder/index','authdata'=>'HuodongBaomingOrder/*'];
            if($isadmin){
				$component_huodong_baoming[] = ['name'=>'海报设置','path'=>'HuodongBaomingPoster/index','authdata'=>'HuodongBaomingPoster/*'];
				$component_huodong_baoming[] = ['name'=>'系统设置','path'=>'HuodongBaomingSet/set','authdata'=>'HuodongBaomingSet/*'];
			}
			$component_child[] = ['name'=>'活动报名','child'=>$component_huodong_baoming];
		
		$component_filem = [];
		$component_filem[] = ['name'=>'文件列表','path'=>'Filem/index','authdata'=>'Filem/*'];
		$component_filem[] = ['name'=>'文件分类','path'=>'FilemCategory/index','authdata'=>'FilemCategory/*'];
		$component_filem[] = ['name'=>'系统设置','path'=>'FilemSet/set','authdata'=>'FilemSet/*'];
		$component_child[] = ['name'=>'文件管家','child'=>$component_filem];
		//$menudata['Filem'] = ['name'=>'文件管家','fullname'=>'文件管家菜单','icon'=>'my-icon my-icon-yingxiao','child'=>$component_filem];
		if($isadmin){
			$component_luntan = [];
			$component_luntan[] = ['name'=>'帖子列表','path'=>'Luntan/index','authdata'=>'Luntan/*'];
			$component_luntan[] = ['name'=>'笔记管理','path'=>'LuntanCategory/index','authdata'=>'LuntanCategory/*'];
			$component_luntan[] = ['name'=>'评论列表','path'=>'LuntanPinglun/index','authdata'=>'LuntanPinglun/*'];
			$component_luntan[] = ['name'=>'帖子回评','path'=>'LuntanPlreply/index','authdata'=>'LuntanPlreply/*'];
			$component_luntan[] = ['name'=>'系统设置','path'=>'Luntan/sysset','authdata'=>'Luntan/sysset'];
			$component_child[] = ['name'=>'用户论坛','child'=>$component_luntan];
		}
		
		if($isadmin){
            $component_caigou = [];
            $component_caigou[] = ['name'=>'信息列表','path'=>'Caigou/index','authdata'=>'Caigou/*'];
            $component_caigou[] = ['name'=>'分类管理','path'=>'CaigouCategory/index','authdata'=>'CaigouCategory/*'];
            $component_caigou[] = ['name'=>'评论列表','path'=>'CaigouPinglun/index','authdata'=>'CaigouPinglun/*'];
            $component_caigou[] = ['name'=>'评论回复','path'=>'CaigouPlreply/index','authdata'=>'CaigouPlreply/*'];
            $component_caigou[] = ['name'=>'系统设置','path'=>'Caigou/sysset','authdata'=>'Caigou/sysset'];
            $component_child[] = ['name'=>'采购供应','child'=>$component_caigou];
		}
		
	if ($isadmin) {
    $component_farm = [];
    
    // 地块管理
    $component_farm[] = [ 'name' => '地块管理', 'path' => 'Plots/dikuai', 'authdata' => 'Plots/*'];
    
    // 排队队列
    $component_farm[] = ['name' => '排队队列', 'path' => 'Plots/queueList','authdata' => 'Plots/*'];
    
    // 猪仔管理
    $component_farm[] = [ 'name' => '猪仔管理','path' => 'Plots/piglist', 'authdata' => 'Plots/*'];
    
    // 喂养日志
    $component_farm[] = ['name' => '喂养日志','path' => 'Plots/weiyangLogs','authdata' => 'Plots/*'];
    
    // 出售记录
    $component_farm[] = ['name' => '出售记录','path' => 'Plots/saleLogs','authdata' => 'Plots/*'];

    // 将菜单添加到主菜单
    $component_child[] = ['name' => '农场管理', 'child' => $component_farm ];
}


		if($isadmin){
			$component_Daihuobiji = [];
			$component_Daihuobiji[] = ['name'=>'笔记管理','path'=>'Daihuobiji/index','authdata'=>'Daihuobiji/*'];
			$component_Daihuobiji[] = ['name'=>'笔记分类','path'=>'DaihuobijiCategory/index','authdata'=>'DaihuobijiCategory/*'];
			$component_Daihuobiji[] = ['name'=>'笔记标签','path'=>'Daihuobijibiaoqian/index','authdata'=>'Daihuobijibiaoqian/*'];
			$component_Daihuobiji[] = ['name'=>'关注记录','path'=>'DaihuobijiGuanzhu/index','authdata'=>'DaihuobijiGuanzhu/*'];
			$component_Daihuobiji[] = ['name'=>'评论列表','path'=>'DaihuobijiPinglun/index','authdata'=>'DaihuobijiPinglun/*'];
			$component_Daihuobiji[] = ['name'=>'评论回评','path'=>'DaihuobijiPlreply/index','authdata'=>'DaihuobijiPlreply/*'];
			$component_Daihuobiji[] = ['name'=>'系统设置','path'=>'Daihuobiji/sysset','authdata'=>'Daihuobiji/sysset'];
			$component_child[] = ['name'=>'带货笔记','child'=>$component_Daihuobiji];
				//$menudata['Daihuobiji'] = ['name'=>'笔记','fullname'=>'带货笔记菜单','icon'=>'my-icon my-icon-yingxiao','child'=>$component_Daihuobiji];
		}
		$component_Daihuoyiuan = [];
		$component_Daihuoyiuan[] = ['name'=>'带货团管理','path'=>'Daihuoyiuan/index','authdata'=>'Daihuoyiuan/*'];
		$component_Daihuoyiuan[] = ['name'=>'商品管理','path'=>'TuanzhangProduct/index','authdata'=>'TuanzhangProduct/*'];
		$component_Daihuoyiuan[] = ['name'=>'订单管理','path'=>'TuanzhangOrder/index','authdata'=>'TuanzhangOrder/*'];
		$component_Daihuoyiuan[] = ['name'=>'团长收益','path'=>'TuanzhangMoney/moneylog','authdata'=>'TuanzhangMoney/*'];
		$component_Daihuoyiuan[] = ['name'=>'提现申请','path'=>'TuanzhangMoney/withdraw','authdata'=>'TuanzhangMoney/*'];
        $component_Daihuoyiuan[] = ['name'=>'提现记录','path'=>'TuanzhangMoney/withdrawlog','authdata'=>'TuanzhangMoney/*'];
		if($isadmin){
// 			$component_Daihuoyiuan = [];
// 		$component_Daihuoyiuan[] = ['name'=>'带货团管理','path'=>'Daihuoyiuan/index','authdata'=>'Daihuoyiuan/*'];
		$component_Daihuoyiuan[] = ['name'=>'带货团分类','path'=>'DaihuoyiuanCategory/index','authdata'=>'DaihuoyiuanCategory/*'];
// 			$component_Daihuoyiuan[] = ['name'=>'团长','path'=>'DaihuoyiuanCategory/index','authdata'=>'DaihuoyiuanCategory/*'];
		$component_Daihuoyiuan[] = ['name'=>'评论列表','path'=>'DaihuoyiuanPinglun/index','authdata'=>'DaihuoyiuanPinglun/*'];
		$component_Daihuoyiuan[] = ['name'=>'回评列表','path'=>'DaihuoyiuanPlreply/index','authdata'=>'DaihuoyiuanPlreply/*'];
		$component_Daihuoyiuan[] = ['name'=>'系统设置','path'=>'DaihuoyiuanSet/set','authdata'=>'DaihuoyiuanSet/*'];

		}
				$component_child[] = ['name'=>'带货团专题','child'=>$component_Daihuoyiuan];
				//$menudata['Daihuoyiuan'] = ['name'=>'带货团','fullname'=>'带货团菜单','icon'=>'my-icon my-icon-yingxiao','child'=>$component_Daihuoyiuan];
		
				$component_shenqingbandian=[];
		$component_shenqingbandian[] = ['name'=>'服务类型','path'=>'Shenqingbandian/index','authdata'=>'Shenqingbandian/*'];
		$component_shenqingbandian[] = ['name'=>'服务商品','path'=>'ShenqingbandianList/index','authdata'=>'ShenqingbandianList/*'];
		$component_shenqingbandian[] = ['name'=>'服务订单','path'=>'ShenqingbandianOrder/index','authdata'=>'ShenqingbandianOrder/*'];
		$component_shenqingbandian[] = ['name'=>'人员类型','path'=>'ShenqingbandianWorkerCategory/index','authdata'=>'ShenqingbandianWorkerCategory/*'];
		$component_shenqingbandian[] = ['name'=>'人员列表','path'=>'ShenqingbandianWorker/index','authdata'=>'ShenqingbandianWorker/*'];
		$component_shenqingbandian[] = ['name'=>'人员评价','path'=>'ShenqingbandianWorkerComment/index','authdata'=>'ShenqingbandianWorkerComment/*'];
	    
	    $component_shenqingbandian[] = ['name'=>'管理人员','path'=>'ShenqingbandianAdmin/index','authdata'=>'ShenqingbandianAdmin/*'];
		$component_shenqingbandian[] = ['name'=>'系统设置','path'=>'ShenqingbandianSet/set','authdata'=>'ShenqingbandianSet/*'];
		$component_shenqingbandian[] = ['name'=>'pdf生成设置','path'=>'ShenqingbandianSet/setpdf','authdata'=>'ShenqingbandianSet/*'];
		$component_child[] = ['name'=>'申请办电','child'=>$component_shenqingbandian];

		$component_tongxunlu=[];
		$component_tongxunlu[] = ['name'=>'区域管理','path'=>'TongxunluFuwuquyu/index','authdata'=>'TongxunluFuwuquyu/*'];
// 		$component_tongxunlu[] = ['name'=>'网点管理','path'=>'TongxunluFuwuzhan/index','authdata'=>'TongxunluFuwuzhan/*'];
	   // $component_tongxunlu[] = ['name'=>'小区管理','path'=>'TongxunluXiaoqu/index','authdata'=>'TongxunluXiaoqu/*'];
		$component_tongxunlu[] = ['name'=>'人员列表','path'=>'TongxunluWorker/index','authdata'=>'TongxunluWorker/*'];
	    $component_tongxunlu[] = ['name'=>'系统设置','path'=>'TongxunluSet/set','authdata'=>'TongxunluSet/*'];
		
		$component_child[] = ['name'=>'服务网点','child'=>$component_tongxunlu];

		$component_electricityform=[];
		$component_electricityform[] = ['name'=>'表单管理','path'=>'ElectricityForm/index','authdata'=>'ElectricityForm/*'];
		$component_electricityform[] = ['name'=>'人员列表','path'=>'ElectricityWorker/index','authdata'=>'ElectricityForm/*,ElectricityWorker/*'];
		$component_child[] = ['name'=>'流程中转','child'=>$component_electricityform];
		
		if($isadmin){
			$component_sign = [];
			$component_sign[] = ['name'=>'签到记录','path'=>'Sign/record','authdata'=>'Sign/record,Sign/recordexcel,Sign/recorddel'];
			$component_sign[] = ['name'=>'签到设置','path'=>'Sign/set','authdata'=>'Sign/set'];
			$component_child[] = ['name'=>t('积分').'签到','child'=>$component_sign];
		}
		//预约服务
		//$yuyue = db('yuyue_set')->field('diyname')->where('aid',aid)->find();
		$component_yuyue=[];
		$component_yuyue[] = ['name'=>'服务类型','path'=>'Yuyue/index','authdata'=>'Yuyue/*'];
		$component_yuyue[] = ['name'=>'服务商品','path'=>'YuyueList/index','authdata'=>'YuyueList/*'];
		$component_yuyue[] = ['name'=>'服务套餐','path'=>'YuyuePackage/index','authdata'=>'YuyuePackage/*'];
		$component_yuyue[] = ['name'=>'套餐订单列表','path'=>'YuyuePackageOrder/index','authdata'=>'YuyuePackageOrder/*']; // 新增套餐订单列表菜单
		$component_yuyue[] = ['name'=>'服务订单','path'=>'YuyueOrder/index','authdata'=>'YuyueOrder/*'];
		$component_yuyue[] = ['name'=>'录入订单','path'=>'YuyueOrderlr/index','authdata'=>'YuyueOrderlr/*,YuyueFuwu/index,YuyueFuwu/getfuwu,Member/index'];
		$component_yuyue[] = ['name'=>'商品服务','path'=>'YuyueFuwu/index','authdata'=>'YuyueFuwu/*'];
		$component_yuyue[] = ['name'=>'商品评价','path'=>'YuyueComment/index','authdata'=>'YuyueComment/*'];
		if($isadmin){
			$component_yuyue[] = ['name'=>'海报设置','path'=>'YuyuePoster/index','authdata'=>'YuyuePoster/*'];
		}
		$component_yuyue[] = ['name'=>'人员类型','path'=>'YuyueWorkerCategory/index','authdata'=>'YuyueWorkerCategory/*'];
		$component_yuyue[] = ['name'=>'人员列表','path'=>'YuyueWorker/index','authdata'=>'YuyueWorker/*'];
		$component_yuyue[] = ['name'=>'服务日历','path'=>'YuyueCalendar/index','authdata'=>'YuyueCalendar/*'];
		$component_yuyue[] = ['name'=>'人员评价','path'=>'YuyueWorkerComment/index','authdata'=>'YuyueWorkerComment/*'];
		$component_yuyue[] = ['name'=>'提成明细','path'=>'YuyueMoney/moneylog','authdata'=>'YuyueMoney/*'];
		$component_yuyue[] = ['name'=>'提现记录','path'=>'YuyueMoney/withdrawlog','authdata'=>'YuyueMoney/*'];
		$component_yuyue[] = ['name'=>'系统设置','path'=>'YuyueSet/set','authdata'=>'YuyueSet/*'];
		$component_child[] = ['name'=>'预约服务','child'=>$component_yuyue];

//     	$component_yuyue=[];
// 		$component_yuyue[] = ['name'=>'活动类型','path'=>'Huodongbaoming/index','authdata'=>'Yuyue/*'];
// 		$component_yuyue[] = ['name'=>'活动管理','path'=>'HuodongbaomingList/index','authdata'=>'YuyueList/*'];
// 		$component_yuyue[] = ['name'=>'报名订单','path'=>'HuodongbaomingOrder/index','authdata'=>'YuyueOrder/*'];
// 		$component_yuyue[] = ['name'=>'商品评价','path'=>'HuodongbaomingComment/index','authdata'=>'YuyueComment/*'];
// 		if($isadmin){
// 			$component_yuyue[] = ['name'=>'海报设置','path'=>'HuodongbaomingPoster/index','authdata'=>'YuyuePoster/*'];
// 		}
// 		$component_yuyue[] = ['name'=>'提成明细','path'=>'Huodongbaoming/moneylog','authdata'=>'YuyueMoney/*'];
// 		$component_yuyue[] = ['name'=>'提现记录','path'=>'Huodongbaoming/withdrawlog','authdata'=>'YuyueMoney/*'];
// 		$component_yuyue[] = ['name'=>'系统设置','path'=>'HuodongbaomingSet/set','authdata'=>'YuyueSet/*'];
// 		$component_child[] = ['name'=>'活动报名','child'=>$component_yuyue];

		$component_kecheng=[];
		$component_kecheng[] = ['name'=>'课程类型','path'=>'KechengCategory/index','authdata'=>'KechengCategory/*'];
		$component_kecheng[] = ['name'=>'课程列表','path'=>'KechengList/index','authdata'=>'KechengList/*,KechengRecord/*'];
		$component_kecheng[] = ['name'=>'课程训练营','path'=>'KechengTraining/index','authdata'=>'KechengTraining/*'];
		$component_kecheng[] = ['name'=>'课程章节','path'=>'KechengChapter/index','authdata'=>'KechengChapter/*'];
		$component_kecheng[] = ['name'=>'课程笔记','path'=>'KechengNotes/index','authdata'=>'KechengNotes/*'];
		$component_kecheng[] = ['name'=>'题库管理','path'=>'KechengTiku/index','authdata'=>'KechengTiku/*'];
		$component_kecheng[] = ['name'=>'课程订单','path'=>'KechengOrder/index','authdata'=>'KechengOrder/*'];
		$component_kecheng[] = ['name'=>'学习记录','path'=>'KechengStudylog/index','authdata'=>'KechengStudylog/*'];
		if($isadmin){
			$component_kecheng[] = ['name'=>'课程海报','path'=>'KechengPoster/index','authdata'=>'KechengPoster/*'];
			$component_kecheng[] = ['name'=>'课程设置','path'=>'KechengSet/index','authdata'=>'KechengSet/*'];
		}
		$component_child[] = ['name'=>'知识付费','child'=>$component_kecheng];
		
		
		//考试系统
		$component_exam=[];
		$component_exam[] = ['name'=>'题库类型','path'=>'ExamType/index','authdata'=>'ExamType/*'];
		$component_exam[] = ['name'=>'答题分类','path'=>'ExamCategory/index','authdata'=>'ExamCategory/*'];
		$component_exam[] = ['name'=>'试卷管理','path'=>'ExamList/index','authdata'=>'ExamList/*'];
		$component_exam[] = ['name'=>'订单管理','path'=>'ExamOrder/index','authdata'=>'ExamOrder/*'];
		$component_exam[] = ['name'=>'题库管理','path'=>'ExamTiku/index','authdata'=>'ExamTiku/*'];
		
		$component_child[] = ['name'=>'考试系统','child'=>$component_exam];
		
		$component_daxue = [];
		$component_daxue[] = ['name'=>'大学列表','path'=>'Daxue/index','authdata'=>'Daxue/*,DaxueFreight/*'];
		$component_daxue[] = ['name'=>'笔记管理','path'=>'Daxue/category','authdata'=>'Daxue/*'];
		$component_daxue[] = ['name'=>'类型管理','path'=>'Daxue/leixing','authdata'=>'Daxue/*'];
		$component_daxue[] = ['name'=>'笔记标签','path'=>'Daxue/biaoqian','authdata'=>'Daxue/*'];
		$component_daxue[] = ['name'=>'专业管理','path'=>'Daxue/zhuanye','authdata'=>'Daxue/*'];
		$component_daxue[] = ['name'=>'分数线管理','path'=>'Daxue/fenshuxian','authdata'=>'Daxue/*'];
		$component_daxue[] = ['name'=>'招生资料','path'=>'Daxue/Article','authdata'=>'Daxue/*'];
		$component_daxue[] = ['name'=>'系统设置','path'=>'Daxue/sysset','authdata'=>'Daxue/sysset'];
			$component_child[] = ['name'=>'大学列表','child'=>$component_daxue];

		if($isadmin){
			$component_peisong = [];
			$component_peisong[] = ['name'=>'配送员列表','path'=>'PeisongUser/index','authdata'=>'PeisongUser/*'];
			$component_peisong[] = ['name'=>'配送单列表','path'=>'PeisongOrder/index','authdata'=>'PeisongOrder/*'];
			$component_peisong[] = ['name'=>'评价列表','path'=>'PeisongComment/index','authdata'=>'PeisongComment/*'];
			$component_peisong[] = ['name'=>'提成明细','path'=>'PeisongMoney/moneylog','authdata'=>'PeisongMoney/*'];
			$component_peisong[] = ['name'=>'提现记录','path'=>'PeisongMoney/withdrawlog','authdata'=>'Peisong/*'];
			$component_peisong[] = ['name'=>'退框记录','path'=>'PeisongOrder/tuikuanglog','authdata'=>'PeisongOrder/*'];
			$component_peisong[] = ['name'=>'系统设置','path'=>'Peisong/set','authdata'=>'Peisong/*'];
			$component_peisong[] = ['name'=>'码科跑腿对接','path'=>'Peisong/makeset','authdata'=>'Peisong/*'];
			$component_child[] = ['name'=>'同城配送','child'=>$component_peisong];
			$component_express = [];
			$component_express[] = ['name'=>'活动列表','path'=>'Toupiao/index','authdata'=>'Toupiao/*'];
			$component_express[] = ['name'=>'选手列表','path'=>'Toupiao/joinlist','authdata'=>'Toupiao/*'];
			$component_express[] = ['name'=>'投票记录','path'=>'Toupiao/helplist','authdata'=>'Toupiao/*'];
			//$component_express[] = ['name'=>'投票设置','path'=>'Toupiao/set','authdata'=>'Toupiao/*'];
			$component_child[] = ['name'=>'投票活动','child'=>$component_express];
			
					$component_jianbanbaoming = [];
			$component_jianbanbaoming [] = ['name'=>'活动列表','path'=>'Jianbanbaoming/index','authdata'=>'Jianbanbaoming/*'];
			$component_jianbanbaoming [] = ['name'=>'报名列表','path'=>'Jianbanbaoming/joinlist','authdata'=>'Jianbanbaoming/*'];
		//	$component_express[] = ['name'=>'投票记录','path'=>'Toupiao/helplist','authdata'=>'Toupiao/*'];
			//$component_express[] = ['name'=>'投票设置','path'=>'Toupiao/set','authdata'=>'Toupiao/*'];
			$component_child[] = ['name'=>'报名活动','child'=>$component_jianbanbaoming ];
		}

		$yingxiao_cycle = [];
		$yingxiao_cycle[] = ['name'=>'商品管理','path'=>'CycleProduct/index','authdata'=>'CycleProduct/*,CycleCode/*'];
		$yingxiao_cycle[] = ['name'=>'订单管理','path'=>'CycleOrder/index','authdata'=>'CycleOrder/*'];
		$yingxiao_cycle[] = ['name'=>'配送管理','path'=>'CycleOrder/cycle_list&status=1','authdata'=>'CycleOrder/*'];
		$yingxiao_cycle[] = ['name'=>'评价管理','path'=>'CycleComment/index','authdata'=>'CycleComment/*'];
		if($isadmin){
			$yingxiao_cycle[] = ['name'=>'商品分类','path'=>'CycleCategory/index','authdata'=>'CycleCategory/*'];
			$yingxiao_cycle[] = ['name'=>'分享海报','path'=>'CyclePoster/index','authdata'=>'CyclePoster/*'];
			$yingxiao_cycle[] = ['name'=>'系统设置','path'=>'CycleSet/index','authdata'=>'CycleSet/*'];
		}
		$component_child[] = ['name'=>'周期购','child'=>$yingxiao_cycle];
		
			$yingxiao_zuji = [];
		$yingxiao_zuji[] = ['name'=>'机型管理','path'=>'ZujiProduct/index','authdata'=>'ZujiProduct/*,ZujiProduct/*'];
		$yingxiao_zuji[] = ['name'=>'订单管理','path'=>'ZujiOrder/index','authdata'=>'ZujiOrder/*'];
// 		$yingxiao_zuji[] = ['name'=>'配送管理','path'=>'CycleOrder/cycle_list&status=1','authdata'=>'CycleOrder/*'];
// 		$yingxiao_zuji[] = ['name'=>'评价管理','path'=>'CycleComment/index','authdata'=>'CycleComment/*'];
		if($isadmin){
			$yingxiao_zuji[] = ['name'=>'商品分类','path'=>'ZujiCategory/index','authdata'=>'ZujiCategory/*'];
// 			$yingxiao_zuji[] = ['name'=>'分享海报','path'=>'CyclePoster/index','authdata'=>'CyclePoster/*'];
// 			$yingxiao_zuji[] = ['name'=>'系统设置','path'=>'CycleSet/index','authdata'=>'CycleSet/*'];
		}
		$component_child[] = ['name'=>'租机插件','child'=>$yingxiao_zuji];
		
		if($isadmin){
			$component_mingpian=[];
			$component_mingpian[] = ['name'=>'名片列表','path'=>'Mingpian/index','authdata'=>'Mingpian/*'];
			$component_mingpian[] = ['name'=>'系统设置','path'=>'Mingpian/set','authdata'=>'Mingpian/*'];
			$component_child[] = ['name'=>'名片','child'=>$component_mingpian];
		}

        $cashier_child = [];
        $cashier_child[] = ['name' => '收银设置', 'path' => 'Cashier/index', 'authdata' => 'Cashier/*'];
        $cashier_child[] = ['name' => '收银订单', 'path' => 'CashierOrder/index', 'authdata' => 'CashierOrder/*'];
		$cashier_child[] = ['name' => '订单统计', 'path' => 'CashierOrder/tongji', 'authdata' => 'CashierOrder/*'];
        $component_child[] = ['name'=>'收银台','child'=>$cashier_child];




		$component_child[] = ['name'=>t('自定义表单'),'path'=>'Form/index','authdata'=>'Form/*'];
		
        if($isadmin){

            $lipin_child = [];
            $lipin_child[] = ['name' => '礼品卡', 'path' => 'Lipin/index', 'authdata' => 'Lipin/*'];
            $lipin_child[] = ['name' => '礼品卡兑换码', 'path' => 'Lipin/codelist', 'authdata' => 'Lipin/codelist'];
            $lipin_child[] = ['name'=>'兑换码生成','path'=>'Lipin/makecode','authdata'=>'Lipin/makecode','hide'=>true];
            $lipin_child[] = ['name'=>'兑换码导入','path'=>'Lipin/importexcel','authdata'=>'Lipin/importexcel','hide'=>true];
            $lipin_child[] = ['name'=>'兑换码导出','path'=>'Lipin/codelistexcel','authdata'=>'Lipin/codelistexcel','hide'=>true];
            $lipin_child[] = ['name'=>'兑换码修改状态','path'=>'Lipin/setst','authdata'=>'Lipin/setst','hide'=>true];
            $lipin_child[] = ['name'=>'兑换码删除','path'=>'Lipin/codelistdel','authdata'=>'Lipin/codelistdel','hide'=>true];
            $lipin_child[] = ['name' => '礼品卡分类', 'path' => 'LipinCategory/index', 'authdata' => 'LipinCategory/*'];
            $component_child[] = ['name'=>'礼品卡','child'=>$lipin_child];


   $lipin2_child = [];
            $lipin2_child[] = ['name' => '超级礼品卡', 'path' => 'Lipin2/index', 'authdata' => 'Lipin2/*'];
            $lipin2_child[] = ['name' => '超级礼品卡兑换码', 'path' => 'Lipin2/codelist', 'authdata' => 'Lipin2/codelist'];
            $lipin2_child[] = ['name'=>'超级礼品卡生成','path'=>'Lipin2/makecode','authdata'=>'Lipin2/makecode','hide'=>true];
            $lipin2_child[] = ['name'=>'兑换码导入','path'=>'Lipin2/importexcel','authdata'=>'Lipin2/importexcel','hide'=>true];
            $lipin2_child[] = ['name'=>'兑换码导出','path'=>'Lipin2/codelistexcel','authdata'=>'Lipin2/codelistexcel','hide'=>true];
            $lipin2_child[] = ['name'=>'兑换码修改状态','path'=>'Lipin2/setst','authdata'=>'Lipin2/setst','hide'=>true];
            $lipin2_child[] = ['name'=>'兑换码删除','path'=>'Lipin2/codelistdel','authdata'=>'Lipin2/codelistdel','hide'=>true];
            $lipin2_child[] = ['name' => '超级礼品卡分类', 'path' => 'Lipin2Category/index', 'authdata' => 'Lipin2Category/*'];

            $component_child[] = ['name'=>'超级礼品卡','child'=>$lipin2_child];



			if(in_array('wx',$platform)){
				$component_child[] = ['name'=>'物流助手','path'=>'Miandan/index','authdata'=>'Miandan/*'];
				$component_child[] = ['name'=>'小程序直播','path'=>'Live/index','authdata'=>'Live/*'];
				$component_child[] = ['name'=>'视频号接入','child'=>[
					['name'=>'申请接入','path'=>'Wxvideo/apply','authdata'=>'Wxvideo/*'],
					['name'=>'商家信息','path'=>'Wxvideo/setinfo','authdata'=>'Wxvideo/*'],
					['name'=>'商品管理','path'=>'ShopProduct/index&fromwxvideo=1','authdata'=>'ShopProduct/*,ShopCode/*'],
					['name'=>'订单管理','path'=>'ShopOrder/index&fromwxvideo=1','authdata'=>'ShopOrder/*'],
					['name'=>'退款申请','path'=>'ShopRefundOrder/index&fromwxvideo=1','authdata'=>'ShopRefundOrder/*'],
					['name'=>'我的类目','path'=>'Wxvideo/category','authdata'=>'Wxvideo/*'],
					['name'=>'我的品牌','path'=>'Wxvideo/brand','authdata'=>'Wxvideo/*'],
					['name'=>'同步修复','path'=>'Wxvideo/deliverytongbu','authdata'=>'Wxvideo/*'],
				]];
			}
			//if(in_array('toutiao',$platform)){
			//	$component_child[] = ['name'=>'抖音接入','child'=>[
			//		['name'=>'接入配置','path'=>'DouyinSet/index','authdata'=>'Douyin/*'],
					//['name'=>'商品管理','path'=>'ShopProduct/index&fromdouyin=1','authdata'=>'ShopProduct/*,ShopCode/*'],
			//		['name'=>'商品管理','path'=>'DouyinProduct/index','authdata'=>'DouyinProduct/*'],
			//	]];
			//}
            }
            
            
            
            	if($isadmin){
			$component_jishou = [];
			$component_jishou[] = ['name'=>'场次配置','path'=>'Miaosha/changci','authdata'=>'Miaosha/*'];
// 			$component_jishou[] = ['name'=>'预付款记录','path'=>'Miaosha/yufukuan&id=4','authdata'=>'Miaosha/*'];
			$component_jishou[] = ['name'=>'寄售商品','path'=>'MiaoshaProduct/index','authdata'=>'MiaoshaProduct/*'];
			$component_jishou[] = ['name'=>'卖单列表','path'=>'Miaosha/index','authdata'=>'Miaosha/*'];
			$component_jishou[] = ['name'=>'买单列表','path'=>'Miaosha/maidan','authdata'=>'Miaosha/*'];
// 			$component_jishou[] = ['name'=>'付款记录','path'=>'Miaosha/jilu','authdata'=>'Miaosha/*'];
			$component_jishou[] = ['name'=>'用户列表','path'=>'Miaosha/miaoshauser','authdata'=>'Miaosha/*'];
			$component_jishou[] = ['name'=>'签约审核','path'=>'Miaosha/miaoshauserqianyue','authdata'=>'Miaosha/*'];
			$component_jishou[] = ['name'=>'签约列表','path'=>'Miaosha/miaoshauserqianyuelist','authdata'=>'Miaosha/*'];
			$component_jishou[] = ['name'=>'预约记录','path'=>'Miaosha/changciyuyue','authdata'=>'Miaosha/*'];
			$component_jishou[] = ['name'=>'委托付款记录','path'=>'Miaosha/rechargelog','authdata'=>'Miaosha/*'];
			$component_jishou[] = ['name'=>'订单历史列表','path'=>'Miaosha/historyList','authdata'=>'Miaosha/*'];
			$component_jishou[] = ['name'=>'回收列表','path'=>'Miaosha/huishou','authdata'=>'Miaosha/*'];
			$component_jishou[] = ['name'=>'寄售全局配置','path'=>'MiaoshaSet/index','authdata'=>'MiaoshaSet/*'];
			
			$component_child[] = ['name'=>'寄售系统','child'=>$component_jishou];
		}
		
		
		
        // if($isadmin){
        $component_yunkucun=[];
        $component_yunkucun[] = ['name'=>'系统设置','path'=>'Yunkuncun/sysset','authdata'=>'Yunkuncun/*'];
        $component_yunkucun[] = ['name'=>'添加云库存商品','path'=>'Yunkuncun/addproductlist','authdata'=>'Yunkuncun/*'];
        $component_yunkucun[] = ['name'=>'云库存商品','path'=>'Yunkuncun/productlist','authdata'=>'Yunkuncun/*'];
        $component_yunkucun[] = ['name'=>'云库存订单','path'=>'Yunkuncun/yunorder','authdata'=>'Yunkuncun/*'];
        $component_yunkucun[] = ['name'=>'提货管理','path'=>'Yunkuncun/tihuo','authdata'=>'Yunkuncun/*'];
        $component_yunkucun[] = ['name'=>'收益记录','path'=>'Yunkuncun/shouyilog','authdata'=>'Yunkuncun/*'];
        $component_yunkucun[] = ['name'=>'库存列表','path'=>'Yunkuncun/kuncunlist','authdata'=>'Yunkuncun/*'];
        $component_yunkucun[] = ['name'=>'库存详情','path'=>'Yunkuncun/kundetail','authdata'=>'Yunkuncun/*'];
        $component_yunkucun[] = ['name'=>'库存明细','path'=>'Yunkuncun/kunmingxi','authdata'=>'Yunkuncun/*'];
        $component_child[] = ['name'=>'云库存','child'=>$component_yunkucun];
        
        // }
        $component_yunkucun=[];
        if($isadmin){
            $component_yunkucun[] = ['name'=>'系统设置','path'=>'Paidui/sysset','authdata'=>'Paidui/*'];
            $component_yunkucun[] = ['name'=>'买单比例设置','path'=>'Paidui/sysset2','authdata'=>'Paidui/*'];
            $component_yunkucun[] = ['name'=>'分期管理','path'=>'Paidui/periods','authdata'=>'Paidui/*'];
            $component_yunkucun[] = ['name'=>'分期配置','path'=>'Paidui/periodsConfig','authdata'=>'Paidui/*'];
            $component_yunkucun[] = ['name'=>'分红日志','path'=>'Paidui/distributionLogs','authdata'=>'Paidui/*'];
            $component_yunkucun[] = ['name'=>'详细分红记录','path'=>'Paidui/distributionDetails','authdata'=>'Paidui/*'];
            $component_yunkucun[] = ['name'=>'免单分红设置','path'=>'Paiduifenhong/index','authdata'=>'Paiduifenhong/*'];
            $component_yunkucun[] = ['name'=>'免单分红明细','path'=>'Paiduifenhong/fenhonglist','authdata'=>'Paiduifenhong/*'];
        }else{
            $component_yunkucun[] = ['name'=>'买单比例设置','path'=>'Paidui/sysset2','authdata'=>'Paidui/*'];
        }
        $component_yunkucun[] = ['name'=>'排队列表','path'=>'Paidui/paiduilist','authdata'=>'Paidui/*'];
        $component_yunkucun[] = ['name'=>'免单金额明细','path'=>'Paidui/score3mingxi','authdata'=>'Paidui/*'];
        $component_child[] = ['name'=>'排队免单','child'=>$component_yunkucun];
        
        $component_yunkucun=[];
        if($isadmin){
			$component_yunkucun[] = ['name'=>'系统设置','path'=>'Liandong/sysset','authdata'=>'Liandong/*'];
			$component_yunkucun[] = ['name'=>'脱离记录','path'=>'Liandong/tuolilog','authdata'=>'Paidui/*'];
			$component_yunkucun[] = ['name'=>'滑落记录','path'=>'Liandong/hualalog','authdata'=>'Liandong/*']; // 新增滑落记录菜单
			$component_yunkucun[] = ['name'=>'联动脱离记录','path'=>'Liandong/liandonglog','authdata'=>'Liandong/*'];
			$component_yunkucun[] = ['name'=>'回归记录','path'=>'Liandong/huiguilog','authdata'=>'Paidui/*'];
			$component_yunkucun[] = ['name'=>'扶持金明细','path'=>'Liandong/dongjienum','authdata'=>'Paidui/*'];
		}
        
        // $component_yunkucun[] = ['name'=>'免单金额明细','path'=>'Paidui/score3mingxi','authdata'=>'Paidui/*'];
        
        $component_child[] = ['name'=>'联动2+1','child'=>$component_yunkucun];
        
        $component_yunkucun=[];
        if($isadmin){
            $component_yunkucun[] = ['name'=>'系统设置','path'=>'Yihuo/sysset','authdata'=>'Yihuo/*'];
            $component_yunkucun[] = ['name'=>'拓展员列表','path'=>'Yihuo/tuozhanyuan','authdata'=>'Yihuo/*'];
			$component_yunkucun[] = ['name'=>'拓展员申请','path'=>'Yihuo/tuozhanyuanapply','authdata'=>'Yihuo/*'];
            $component_yunkucun[] = ['name'=>'城市统计','path'=>'Yihuo/citytongji','authdata'=>'Yihuo/*'];
            $component_yunkucun[] = ['name'=>'拓展员分类','path'=>'Yihuo/tuozhanyuancate','authdata'=>'Yihuo/*'];
            $component_yunkucun[] = ['name'=>'商户信息','path'=>'Yihuo/business','authdata'=>'Yihuo/*'];
            $component_yunkucun[] = ['name'=>'拓展费记录','path'=>'Yihuo/tuozhanfeire','authdata'=>'Yihuo/*'];
            $component_yunkucun[] = ['name'=>'拓展费明细','path'=>'Yihuo/tuozhanfeilog','authdata'=>'Yihuo/*'];
			$component_yunkucun[] = ['name'=>'商家奖励记录','path'=>'Yihuo/businessRewardLog','authdata'=>'Yihuo/*'];
            $component_yunkucun[] = ['name'=>'额度明细','path'=>'Yihuo/edulog','authdata'=>'Yihuo/*'];

        }
        
        // $component_yunkucun[] = ['name'=>'免单金额明细','path'=>'Paidui/score3mingxi','authdata'=>'Paidui/*'];
        
        $component_child[] = ['name'=>'拓展员','child'=>$component_yunkucun];
        
        // 城市代理管理菜单
        $component_cityagent = [];
        if($isadmin){
			$component_cityagent[] = ['name'=>'系统设置','path'=>'Cityagent/sysset','authdata'=>'Cityagent/*'];
            $component_cityagent[] = ['name'=>'城市代理管理','path'=>'Cityagent/index','authdata'=>'Cityagent/*'];
            $component_cityagent[] = ['name'=>'代理分类管理','path'=>'Cityagent/category','authdata'=>'Cityagent/*'];
            $component_cityagent[] = ['name'=>'收益统计','path'=>'Cityagent/income','authdata'=>'Cityagent/*'];
        }
        $component_child[] = ['name'=>'城市代理','child'=>$component_cityagent];
        
        // 排单模块
        $component_paidan = [];
        if($isadmin){
            $component_paidan[] = ['name'=>'排单配置','path'=>'Paidan/index','authdata'=>'Paidan/*'];
            $component_paidan[] = ['name'=>'点位管理','path'=>'Paidan/positionList','authdata'=>'Paidan/*'];
            $component_paidan[] = ['name'=>'奖励记录','path'=>'Paidan/rewardList','authdata'=>'Paidan/*'];
        }
        $component_child[] = ['name'=>'排单模块','child'=>$component_paidan];
        
           $component_tuozhancrm=[];
        if($isadmin){
            $component_tuozhancrm[] = ['name'=>'系统设置','path'=>'Tuozhancrm/sysset','authdata'=>'Tuozhancrm/*'];
            $component_tuozhancrm[] = ['name'=>'公海列表','path'=>'Tuozhancrm/tuozhangonghai','authdata'=>'Tuozhancrm/*'];
            $component_tuozhancrm[] = ['name'=>'商机列表','path'=>'Tuozhancrm/businessOpportunityList','authdata'=>'Tuozhancrm/*'];
            $component_tuozhancrm[] = ['name'=>'跟进记录','path'=>'Tuozhancrm/followUpList','authdata'=>'Tuozhancrm/*'];
            $component_tuozhancrm[] = ['name'=>'商机分类','path'=>'Tuozhancrm/shangjifenlei','authdata'=>'Tuozhancrm/*'];
            $component_tuozhancrm[] = ['name'=>'意向度分类','path'=>'Tuozhancrm/intentionCategory','authdata'=>'Tuozhancrm/*'];
           // $component_tuozhancrm[] = ['name' => '报告与统计', 'path' => 'Tuozhancrm/report', 'authdata' => 'Tuozhancrm/*'];

        }
        
        // $component_yunkucun[] = ['name'=>'免单金额明细','path'=>'Paidui/score3mingxi','authdata'=>'Paidui/*'];
        
        $component_child[] = ['name'=>'拓展员CRM','child'=>$component_tuozhancrm];
        
        
        
             $component_yunkucun=[];
        if($isadmin){
          	 $component_yunkucun[] = ['name'=>'积分池配置','path'=>'Backstage/setjifenchi','authdata'=>'Backstage/setjifenchi'];
          	 	 $component_yunkucun[] = ['name'=>'静态积分明细','path'=>'Score/scoreloga','authdata'=>'Score/*'];
			 $component_yunkucun[] = ['name'=>'动态积分明细','path'=>'Score/scorelogb','authdata'=>'Score/*'];
        }
        
        // $component_yunkucun[] = ['name'=>'免单金额明细','path'=>'Paidui/score3mingxi','authdata'=>'Paidui/*'];
        
        $component_child[] = ['name'=>'积分池模式','child'=>$component_yunkucun];
        
        
                $component_yunkucun=[];
        if($isadmin){
           $component_yunkucun[] = ['name'=>'九星设置','path'=>'Backstage/setjiuxing','authdata'=>'Backstage/setjiuxing'];
          $component_yunkucun[] = ['name'=>t('接点人').'关系图','path'=>'Member/jieidancharts','authdata'=>'Member/jieidancharts'];
          
        }
        
        // $component_yunkucun[] = ['name'=>'免单金额明细','path'=>'Paidui/score3mingxi','authdata'=>'Paidui/*'];
        
        $component_child[] = ['name'=>'九星公排','child'=>$component_yunkucun];
        
                   $component_yunkucun=[];
        if($isadmin){
           $component_yunkucun[] = ['name'=>'七星设置','path'=>'Backstage/setqixing','authdata'=>'Backstage/setjiuxing'];
           	 $component_yunkucun[] = ['name'=>t('虚拟账号').'列表','path'=>'Member/ziindex','authdata'=>'Member/index,Member/excel,Member/excel,Member/importexcel,Member/getplatform,Member/edit,Member/save,Member/del,Member/getcarddetail,Member/charts,Member/setst'];
              $component_yunkucun[] = ['name'=>t('会员公排').'关系图','path'=>'Member/gongpaicharts','authdata'=>'Member/gongpaicharts'];
            	 $component_yunkucun[] = ['name'=>t('虚拟账号积分').'明细','path'=>'Score/scoreduihuanfen','authdata'=>'Score/*'];
           
        }
        
        // $component_yunkucun[] = ['name'=>'免单金额明细','path'=>'Paidui/score3mingxi','authdata'=>'Paidui/*'];
        
        $component_child[] = ['name'=>'七星波卡','child'=>$component_yunkucun];
        
        
        
        $component_yunfenhong=[];
        if($isadmin){
        $component_yunfenhong[] = ['name'=>'分红等级设置','path'=>'Yuefenhong/index','authdata'=>'Yuefenhong/*'];
        $component_yunfenhong[] = ['name'=>'分红记录','path'=>'Yuefenhong/fenhonglist','authdata'=>'Yuefenhong/*'];
           
        }
        
        $component_child[] = ['name'=>'月分红模式','child'=>$component_yunfenhong];
        
              
        $component_jietikaohe=[];
        if($isadmin){
        $component_jietikaohe[] = ['name'=>'分红等级设置','path'=>'Jietikaohe/index','authdata'=>'Jietikaohe/*'];
        $component_jietikaohe[] = ['name'=>'分红记录','path'=>'Jietikaohe/fenhonglist','authdata'=>'Jietikaohe/*'];
        $component_jietikaohe[] = ['name'=>'阶梯考核设置','path'=>'Jietikaohe/set','authdata'=>'Jietikaohe/*'];
           
        }
        
        $component_child[] = ['name'=>'阶梯考核模式','child'=>$component_jietikaohe];
        
        $component_yuejieset=[];
        if($isadmin){
        $component_yuejieset[] = ['name'=>'系统设置','path'=>'Yuejieset/index','authdata'=>'Yuejieset/*'];
        $component_yuejieset[] = ['name'=>'月结用户','path'=>'Yuejieset/userlist','authdata'=>'Yuejieset/*'];
		$component_yuejieset[] = ['name'=>'月结订单','path'=>'Yuejieset/orderlist','authdata'=>'Yuejieset/*'];
		$component_yuejieset[] = ['name'=>'月结明细','path'=>'Yuejieset/arrearslog','authdata'=>'Yuejieset/*'];
           
        }
        
        $component_child[] = ['name'=>'月结中心','child'=>$component_yuejieset];
        
        
       $component_kucunjinxiaocun = [];

        // 添加主菜单和各子菜单项到 $component_kucunjinxiaocun 数组
        $component_kucunjinxiaocun[] = ['name' => '系统设置', 'path' => 'Kucunjinxiaocun/index', 'authdata' => 'Kucunjinxiaocun/*'];
        $component_kucunjinxiaocun[] = ['name' => '商品管理', 'path' => 'ProductManagement/index', 'authdata' => 'ProductManagement/*'];
        $component_kucunjinxiaocun[] = ['name' => '订单管理', 'path' => 'OrderManagement/index', 'authdata' => 'OrderManagement/*'];
        $component_kucunjinxiaocun[] = ['name' => '订单入库', 'path' => 'OrderManagement/inbound', 'authdata' => 'OrderManagement/inbound'];
        $component_kucunjinxiaocun[] = ['name' => '订单配单', 'path' => 'OrderManagement/dispatch', 'authdata' => 'OrderManagement/dispatch'];
        $component_kucunjinxiaocun[] = ['name' => '库存预警', 'path' => 'InventoryWarning/index', 'authdata' => 'InventoryWarning/*'];
        $component_kucunjinxiaocun[] = ['name' => '出入库管理', 'path' => 'InventoryInOut/index', 'authdata' => 'InventoryInOut/*'];
        $component_kucunjinxiaocun[] = ['name' => '新建出库单', 'path' => 'InventoryInOut/outbound', 'authdata' => 'InventoryInOut/outbound'];
        $component_kucunjinxiaocun[] = ['name' => '新建入库单', 'path' => 'InventoryInOut/inbound', 'authdata' => 'InventoryInOut/inbound'];
        $component_kucunjinxiaocun[] = ['name' => '拆单管理', 'path' => 'SplitManagement/index', 'authdata' => 'SplitManagement/*'];
        $component_kucunjinxiaocun[] = ['name' => '新建拆单', 'path' => 'SplitManagement/create', 'authdata' => 'SplitManagement/create'];
        $component_kucunjinxiaocun[] = ['name' => '组合单管理', 'path' => 'CombinationManagement/index', 'authdata' => 'CombinationManagement/*'];
        $component_kucunjinxiaocun[] = ['name' => '新建组合单', 'path' => 'CombinationManagement/create', 'authdata' => 'CombinationManagement/create'];
        

        $component_child[] = ['name'=>'库存进销存','child'=>$component_kucunjinxiaocun];
        
         
        $component_renzhengset=[];
        if($isadmin){
        $component_renzhengset[] = ['name'=>'认证设置','path'=>'Renzhengset/index','authdata'=>'Renzhengset/*'];
        $component_renzhengset[] = ['name'=>'签约审核','path'=>'Renzhengset/miaoshauserqianyue','authdata'=>'Renzhengset/*'];
		$component_renzhengset[] = ['name'=>'签约列表','path'=>'Renzhengset/miaoshauserqianyuelist','authdata'=>'Renzhengset/*'];
           
        }
        
        $component_child[] = ['name'=>'认证中心','child'=>$component_renzhengset];
        
         $component_ep[] = ['name'=>'EP列表','path'=>'ShouMai/index','authdata'=>'ShouMai/*'];
        $component_ep[] = ['name'=>'通道列表','path'=>'ShouMai/corridorIndex','authdata'=>'ShouMai/*'];
        $component_ep[] = ['name'=>'售卖设置','path'=>'ShouMai/saleSettings','authdata'=>'ShouMai/*'];
        $component_child[] = ['name'=>'现金EP','child' => $component_ep];
         
        
                   $component_yunkucun=[];
				   if($isadmin){
					$component_yunkucun[] = ['name'=>'分红点设置','path'=>'Fenhongdian/setting','authdata'=>'Fenhongdian/setting'];
					$component_yunkucun[] = ['name'=>'手动分红','path'=>'Fenhongdian/manual','authdata'=>'Fenhongdian/manual'];
					$component_yunkucun[] = ['name'=>'自动分红设置','path'=>'Fenhongdian/autosetting','authdata'=>'Fenhongdian/autosetting'];
					$component_yunkucun[] = ['name'=>'自动分红日志','path'=>'Fenhongdian/autologs','authdata'=>'Fenhongdian/autologs'];
					$component_yunkucun[] = ['name'=>'分红点日志','path'=>'Fenhongdian/logs','authdata'=>'Fenhongdian/logs'];
					$component_yunkucun[] = ['name'=>'奖励记录','path'=>'Fenhongdian/rewardlogs','authdata'=>'Fenhongdian/rewardlogs'];
					$component_yunkucun[] = ['name'=>t('销售分红发放').'记录','path'=>'Commission/sendxsjl','authdata'=>'Commission/*'];
					
				 }
        
        // $component_yunkucun[] = ['name'=>'免单金额明细','path'=>'Paidui/score3mingxi','authdata'=>'Paidui/*'];
        
        
        
        
        $component_child[] = ['name'=>'分红点','child'=>$component_yunkucun];
        
        
                   $component_yunkucun=[];
        if($isadmin){
           $component_yunkucun[] = ['name'=>'消费值设置','path'=>'Backstage/setfenhongdian','authdata'=>'Backstage/setfenhongdian'];
           	$component_yunkucun[] = ['name'=>'消费值明细','path'=>'Score/scorelogxiaofeizhi','authdata'=>'Score/*'];
           	$component_yunkucun[] = ['name'=>'绿积分明细','path'=>'Score/scoreloglv','authdata'=>'Score/*'];
           	$component_yunkucun[] = ['name'=>'打赏明细','path'=>'Score/scorelogdashang','authdata'=>'Score/*'];
           
        }
        
        // $component_yunkucun[] = ['name'=>'免单金额明细','path'=>'Paidui/score3mingxi','authdata'=>'Paidui/*'];
        
        $component_child[] = ['name'=>'天天打赏','child'=>$component_yunkucun];
        
                        $component_yunkucun=[];
        if($isadmin){
           $component_yunkucun[] = ['name'=>'创业值设置','path'=>'Backstage/setfenhongdian','authdata'=>'Backstage/setfenhongdian'];
           $component_yunkucun[] = ['name'=>t('创业值').'明细','path'=>'Money/chuangyelog','authdata'=>'Money/chuangyelog,Money/moneylog,Money/moneylogexcel,Money/moneylogsetst,Money/moneylogdel'];
           
        }
        
        // $component_yunkucun[] = ['name'=>'免单金额明细','path'=>'Paidui/score3mingxi','authdata'=>'Paidui/*'];
        
        $component_child[] = ['name'=>'创业值','child'=>$component_yunkucun];
        
        
                           $component_yunkucun=[];
        if($isadmin){
           $component_yunkucun[] = ['name'=>t('贡献值').'设置','path'=>'Gongxianzhiset/index','authdata'=>'Gongxianzhiset/*'];
             $component_yunkucun[] = ['name'=>t('贡献值').'明细','path'=>'Gxz/index2','authdata'=>'Gxz/*'];
            $component_yunkucun[] = ['name'=>t('贡献值').'分红','path'=>'Gxz/index','authdata'=>'Gxz/*'];
           
        }
        
        // $component_yunkucun[] = ['name'=>'免单金额明细','path'=>'Paidui/score3mingxi','authdata'=>'Paidui/*'];
        
        $component_child[] = ['name'=>t('贡献值'),'child'=>$component_yunkucun];
        
        
        
        // 添加黄积分菜单
        $component_scorehuang = [];
        if($isadmin){
            $component_scorehuang[] = ['name'=>t('黄积分').'设置','path'=>'ScoreHuang/index','authdata'=>'ScoreHuang/*'];
            $component_scorehuang[] = ['name'=>t('黄积分').'记录','path'=>'ScoreHuang/record','authdata'=>'ScoreHuang/record'];
            $component_scorehuang[] = ['name'=>t('黄积分').'明细','path'=>'ScoreHuang/scorelog','authdata'=>'ScoreHuang/scorelog,ScoreHuang/scorelogexcel,ScoreHuang/scorelogdel'];
            $component_scorehuang[] = ['name'=>t('黄积分').'提现记录','path'=>'ScoreHuang/withdrawlog','authdata'=>'ScoreHuang/*'];
        }
        $component_child[] = ['name'=>t('黄积分'),'child'=>$component_scorehuang];
        
        // 添加周期服务菜单
        $component_periodicservice = [];
        $component_periodicservice[] = ['name'=>'周期服务项目','path'=>'PeriodicServiceProduct/index','authdata'=>'PeriodicServiceProduct/*']; // 新增：管理周期服务项目定义
        $component_periodicservice[] = ['name'=>'周期服务单','path'=>'PeriodicServiceOrder/index','authdata'=>'PeriodicServiceOrder/*'];
        $component_periodicservice[] = ['name'=>'服务工单','path'=>'PeriodicServiceOrder/stageIndex','authdata'=>'PeriodicServiceStage/*']; // 修改这里的 PeriodicServiceStage 为 PeriodicServiceOrder
        $component_child[] = ['name'=>'周期服务','child'=>$component_periodicservice];
        
        // 添加消息通知系统菜单
        $component_message_notify = [];
        if($isadmin){
            $component_message_notify[] = ['name'=>'消息通知管理','path'=>'MessageNotify/index','authdata'=>'MessageNotify/*'];
            $component_message_notify[] = ['name'=>'消息分类管理','path'=>'MessageCategory/index','authdata'=>'MessageCategory/*'];
            $component_message_notify[] = ['name'=>'评论管理','path'=>'MessagePinglun/index','authdata'=>'MessagePinglun/*'];
            $component_message_notify[] = ['name'=>'系统设置','path'=>'MessageSet/index','authdata'=>'MessageSet/*'];
        }else{
            $component_message_notify[] = ['name'=>'消息通知管理','path'=>'MessageNotify/index','authdata'=>'MessageNotify/*'];
            $component_message_notify[] = ['name'=>'消息分类管理','path'=>'MessageCategory/index','authdata'=>'MessageCategory/*'];
            $component_message_notify[] = ['name'=>'评论管理','path'=>'MessagePinglun/index','authdata'=>'MessagePinglun/*'];
            $component_message_notify[] = ['name'=>'系统设置','path'=>'MessageSet/index','authdata'=>'MessageSet/*'];
        }
        $component_child[] = ['name'=>'消息通知','child'=>$component_message_notify];
        
        // 添加智能诊疗模块菜单（2025-01-17 扩展支持面诊和综合诊疗）
        $component_shezhen = [];
        if($isadmin){
            $component_shezhen[] = ['name'=>'系统设置','path'=>'SheZhenSet/index','authdata'=>'SheZhenSet/*'];
            $component_shezhen[] = ['name'=>'舌诊记录','path'=>'SheZhen/index','authdata'=>'SheZhen/*'];
            $component_shezhen[] = ['name'=>'面诊记录','path'=>'SheZhen/faceIndex','authdata'=>'SheZhen/faceIndex'];
            $component_shezhen[] = ['name'=>'综合诊疗记录','path'=>'SheZhen/comprehensiveIndex','authdata'=>'SheZhen/comprehensiveIndex'];
            $component_shezhen[] = ['name'=>'诊疗报告','path'=>'SheZhen/report','authdata'=>'SheZhen/report'];
            $component_shezhen[] = ['name'=>'商品推荐管理','path'=>'SheZhen/recommendManager','authdata'=>'SheZhen/recommendManager'];
            $component_shezhen[] = ['name'=>'数据统计','path'=>'SheZhen/statistics','authdata'=>'SheZhen/statistics'];
			$component_shezhen[] = ['name'=>'诊疗次数充值记录','path'=>'MianzhenRecharge/index','authdata'=>'MianzhenRecharge/*'];
        }else{
            $component_shezhen[] = ['name'=>'舌诊记录','path'=>'SheZhen/index','authdata'=>'SheZhen/*'];
            $component_shezhen[] = ['name'=>'面诊记录','path'=>'SheZhen/faceIndex','authdata'=>'SheZhen/faceIndex'];
            $component_shezhen[] = ['name'=>'综合诊疗记录','path'=>'SheZhen/comprehensiveIndex','authdata'=>'SheZhen/comprehensiveIndex'];
            $component_shezhen[] = ['name'=>'诊疗报告','path'=>'SheZhen/report','authdata'=>'SheZhen/report'];
        }
        $component_child[] = ['name'=>'智能诊疗','child'=>$component_shezhen];
        
        // 节日管理模块
        $component_holiday = [];
        if($isadmin){
            $component_holiday[] = ['name'=>'节日管理','path'=>'Holiday/index','authdata'=>'Holiday/*'];
            $component_holiday[] = ['name'=>'添加节日','path'=>'Holiday/edit','authdata'=>'Holiday/*'];
        }
        $component_child[] = ['name'=>'节日管理','child'=>$component_holiday];
        
        // 上香供奉许愿模块
        $component_shangxiang = [];
        if($isadmin){
            $component_shangxiang[] = ['name'=>'系统设置','path'=>'Shangxiang/index','authdata'=>'Shangxiang/*'];
            $component_shangxiang[] = ['name'=>'许愿记录管理','path'=>'Shangxiang/wishList','authdata'=>'Shangxiang/*'];
            $component_shangxiang[] = ['name'=>'数据统计','path'=>'Shangxiang/statistics','authdata'=>'Shangxiang/*'];
        }
        $component_child[] = ['name'=>'上香供奉许愿','child'=>$component_shangxiang];
        
        // 嘉联支付商户进件管理模块
        $component_jlpay = [];
        if($isadmin){
            $component_jlpay[] = ['name'=>'商户进件管理','path'=>'JlPayMerchant/index','authdata'=>'JlPayMerchant/*'];
            $component_jlpay[] = ['name'=>'申请状态查询','path'=>'JlPayMerchant/query','authdata'=>'JlPayMerchant/*'];
            $component_jlpay[] = ['name'=>'申请修改','path'=>'JlPayMerchant/modify','authdata'=>'JlPayMerchant/*'];
            $component_jlpay[] = ['name'=>'系统设置','path'=>'JlPayMerchant/set','authdata'=>'JlPayMerchant/*'];
        }else{
            $component_jlpay[] = ['name'=>'商户进件管理','path'=>'JlPayMerchant/index','authdata'=>'JlPayMerchant/*'];
            $component_jlpay[] = ['name'=>'申请状态查询','path'=>'JlPayMerchant/query','authdata'=>'JlPayMerchant/*'];
            $component_jlpay[] = ['name'=>'申请修改','path'=>'JlPayMerchant/modify','authdata'=>'JlPayMerchant/*'];
        }
        $component_child[] = ['name'=>'嘉联支付进件','child'=>$component_jlpay];
        
        $menudata['component'] = ['name'=>'扩展','fullname'=>'扩展功能','icon'=>'my-icon my-icon-kuozhan','child'=>$component_child];

		if(getcustom('restaurant')){
			$menudata['restaurant'] = \app\custom\Restaurant::getmenu($isadmin);
		}
		if(getcustom('hotel')){
			$text = \app\model\Hotel::gettext(aid);
            $hotel_child = [];
            $hotel_child[] = ['name'=>$text['酒店'].'设置','path'=>'Hotel/index','authdata'=>'Hotel/*'];
			if($isadmin){
			  $hotel_child[] = ['name'=>$text['酒店'].'类型','path'=>'HotelCategory/index','authdata'=>'HotelCategory/*'];
			}
			$hotel_child[] = ['name'=>$text['酒店'].'相册','path'=>'HotelPhotos/index','authdata'=>'HotelPhotos/*'];
            $hotel_child[] = ['name'=>'房型列表1','path'=>'HotelRoom/index','authdata'=>'HotelRoom/*'];
			$hotel_child[] = ['name'=>'房型分组','path'=>'HotelGroup/index','authdata'=>'HotelGroup/*'];
            $hotel_child[] = ['name'=>'房态房价','path'=>'HotelRoomPrices/index','authdata'=>'HotelRoomPrices/*'];
            $hotel_child[] = ['name'=>$text['酒店'].'订单','path'=>'HotelOrder/index','authdata'=>'HotelOrder/*'];
		    $hotel_child[] = ['name'=>'押金列表','path'=>'HotelOrderYajin/index','authdata'=>'HotelOrderYajin/*'];
		    $hotel_child[] = ['name'=>'订单评价','path'=>'HotelComment/index','authdata'=>'HotelComment/*'];
			$hotel_child[] = ['name'=>'优惠券','path'=>'HotelCoupon/index','authdata'=>'HotelCoupon/*'];
			if($isadmin){
				$hotel_child[] = ['name'=>'系统设置','path'=>'HotelSet/index','authdata'=>'HotelSet/*'];
			}
			$menudata['hotel'] = ['name'=>$text['酒店'],'fullname'=>$text['酒店'].'管理','icon'=>'my-icon my-icon-kuozhan','child'=>$hotel_child];
        }
        $jiemian_child = [];
        if(false){}else{
			$jiemian_child[] = ['name'=>'页面设计','path'=>'DesignerPage/index','authdata'=>'DesignerPage/*'];
			$jiemian_child[] = ['name'=>'页面分类','path'=>'DesignerCategory/index','authdata'=>'DesignerCategory/*'];
		}
		 
		if($isadmin){
            $jiemian_child[] = ['name'=>'底部导航','path'=>'DesignerMenu/index','authdata'=>'DesignerMenu/*'];
            $jiemian_child[] = ['name'=>'内页导航','path'=>'DesignerMenu/menu2','authdata'=>'DesignerMenu/*'];
            $jiemian_child[] = ['name'=>'开屏配置','path'=>'DesignerMenu/splash','authdata'=>'DesignerMenu/*'];
            $jiemian_child[] = ['name'=>'开屏管理','path'=>'DesignerMenu/splashmanage','authdata'=>'DesignerMenu/*'];
			$jiemian_child[] = ['name'=>'商品详情','path'=>'DesignerMenuShopdetail/shopdetail','authdata'=>'DesignerMenuShopdetail/*'];
        }else{
            $jiemian_child[] = ['name'=>'底部导航','path'=>'DesignerMenu/menu2','authdata'=>'DesignerMenu/*'];
            $jiemian_child[] = ['name'=>'开屏配置','path'=>'DesignerMenu/splash','authdata'=>'DesignerMenu/*'];
            $jiemian_child[] = ['name'=>'开屏管理','path'=>'DesignerMenu/splashmanage','authdata'=>'DesignerMenu/*'];
			$jiemian_child[] = ['name'=>'商品详情','path'=>'DesignerMenuShopdetail/shopdetail','authdata'=>'DesignerMenuShopdetail/*'];
        }
		if($isadmin){
        	$jiemian_child[] = ['name'=>'登录页面','path'=>'DesignerLogin/index','authdata'=>'DesignerLogin/*'];
            $jiemian_child[] = ['name'=>'移动端后台','path'=>'DesignerMobile/index','authdata'=>'DesignerMobile/*'];
        }
        $jiemian_child[] = ['name'=>'分享设置','path'=>'DesignerShare/index','authdata'=>'DesignerShare/*'];
        $jiemian_child[] = ['name'=>'链接地址','path'=>'DesignerPage/chooseurl','params'=>'/type/geturl','authdata'=>'DesignerPage/chooseurl'];
        $menudata['jiemian'] = ['name'=>'设计','fullname'=>'界面设计','icon'=>'my-icon my-icon-sheji','child'=>$jiemian_child];

		if($isadmin){
			$pingtai_child = [];
			if(in_array('mp',$platform)){
				$pingtai_child_mp = [];
				$pingtai_child_mp[] = ['name'=>'公众号绑定','path'=>'Binding/index','authdata'=>'Binding/*'];
				$pingtai_child_mp[] = ['name'=>'菜单设置','path'=>'Mpmenu/index','authdata'=>'Mpmenu/*'];
				$pingtai_child_mp[] = ['name'=>'支付设置','path'=>'Mppay/set','authdata'=>'Mppay/*'];
				$pingtai_child_mp[] = ['name'=>'模板消息设置','path'=>'Mptmpl/tmplset','authdata'=>'Mptmpl/*'];
				$pingtai_child_mp[] = ['name'=>'类目模板消息','path'=>'Mptmpl/tmplsetNew','authdata'=>'Mptmpl/tmplsetNew'];
				$pingtai_child_mp[] = ['name'=>'已添加的模板','path'=>'Mptmpl/mytmpl','authdata'=>'Mptmpl/*'];
				$pingtai_child_mp[] = ['name'=>'被关注回复','path'=>'Mpkeyword/subscribe','authdata'=>'Mpkeyword/*'];
				$pingtai_child_mp[] = ['name'=>'关键字回复','path'=>'Mpkeyword/index','authdata'=>'Mpkeyword/*'];
				$pingtai_child_mp[] = ['name'=>'粉丝列表','path'=>'Mpfans/fanslist','authdata'=>'Mpfans/*'];
				$pingtai_child_mp[] = ['name'=>'素材管理','path'=>'Mpfans/sourcelist','authdata'=>'Mpfans/*'];
				$pingtai_child_mp[] = ['name'=>'模板消息群发','path'=>'Mpfans/tmplsend','authdata'=>'Mpfans/*'];
				$pingtai_child_mp[] = ['name'=>'活跃粉丝群发','path'=>'Mpfans/kfmsgsend','authdata'=>'Mpfans/*'];
				$pingtai_child[] = ['name'=>'微信公众号','child'=>$pingtai_child_mp];
				$pingtai_child_mpcard = [];
				$pingtai_child_mpcard[] = ['name'=>'领取记录','path'=>'Membercard/record','authdata'=>'Membercard/record'];
				$pingtai_child_mpcard[] = ['name'=>'会员卡/创建','path'=>'Membercard/index','authdata'=>'Membercard/*'];
                $pingtai_child[] = ['name'=>'微信会员卡','child'=>$pingtai_child_mpcard];
			}
			if(in_array('wx',$platform)){
				$pingtai_child_wx = [];
				$pingtai_child_wx[] = ['name'=>'小程序绑定','path'=>'Binding/index','authdata'=>'Binding/*'];
				$pingtai_child_wx[] = ['name'=>'支付设置','path'=>'Wxpay/set','authdata'=>'Wxpay/*'];
				$pingtai_child_wx[] = ['name'=>'订阅消息设置','path'=>'Wxtmpl/tmplset','authdata'=>'Wxtmpl/*'];
				$pingtai_child_wx[] = ['name'=>'服务类目','path'=>'Wxleimu/index','authdata'=>'Wxleimu/*'];
				$pingtai_child_wx[] = ['name'=>'外部链接','path'=>'Wxurl/index','authdata'=>'Wxurl/*'];
				//$pingtai_child_wx[] = ['name'=>'关键字回复','path'=>'Wxkeyword/index','authdata'=>'Wxkeyword/*'];
				$pingtai_child[] = ['name'=>'微信小程序','child'=>$pingtai_child_wx];
			}
			if(in_array('alipay',$platform)){
				$pingtai_child[] = ['name'=>'支付宝小程序','path'=>'Binding/alipay','authdata'=>'Binding/*'];
			}
			if(in_array('baidu',$platform)){
				$pingtai_child[] = ['name'=>'百度小程序','path'=>'Binding/baidu','authdata'=>'Binding/*'];
			}
			if(in_array('toutiao',$platform)){
				$pingtai_child[] = ['name'=>'头条小程序','path'=>'Binding/toutiao','authdata'=>'Binding/*'];
			}
			if(in_array('qq',$platform)){
				$pingtai_child[] = ['name'=>'QQ小程序','path'=>'Binding/qq','authdata'=>'Binding/*'];
			}
			if(in_array('h5',$platform)){
				$pingtai_child[] = ['name'=>'手机H5','path'=>'Binding/h5','authdata'=>'Binding/*'];
			}
			if(in_array('app',$platform)){
				$pingtai_child[] = ['name'=>'手机APP','path'=>'Binding/app','authdata'=>'Binding/*'];
			}
			
			$menudata['pingtai'] = ['name'=>'平台','fullname'=>'平台设置','icon'=>'my-icon my-icon-pingtai','child'=>$pingtai_child];
		}

		$system_child = [];
		$system_child[] = ['name'=>'系统配置','path'=>'Backstage/sysset','authdata'=>'Backstage/sysset'];
		$system_child[] = ['name'=>'数据统计1','path'=>'Backstage/welcome3','authdata'=>'Backstage/welcome3'];
		$system_child[] = ['name'=>'数据大屏','path'=>'Backstage/dashboard','authdata'=>'Backstage/dashboard'];
		$system_child[] = ['name'=>'提示音配置','path'=>'Backstage/setorder','authdata'=>'Backstage/setorder'];
		$system_child[] = ['name'=>'提示订单处理','path'=>'Backstage/orderchuli','authdata'=>'Backstage/orderchuli'];
		$system_child[] = ['name'=>'门店管理','path'=>'Mendian/index','authdata'=>'Mendian/*'];
		// $system_child[] = ['name'=>'门店管理设置','path'=>'MendianSet/index','authdata'=>'MendianSet/*'];
		$system_child[] = ['name'=>'管理员列表','path'=>'User/index','authdata'=>'User/*,UserGroup/*'];
		$system_child[] = ['name'=>'配送方式','path'=>'Freight/index','authdata'=>'Freight/*'];
		$system_child[] = ['name'=>'小票打印机','path'=>'Wifiprint/index','authdata'=>'Wifiprint/*'];
		$system_child[] = ['name'=>t('支付宝提现'),'path'=>'Money/alipay','authdata'=>'Money/*'];
		if($isadmin){
			$system_child[] = ['name'=>'短信设置','path'=>'Sms/set','authdata'=>'Sms/*'];
		}else{
			$system_child[] = ['name'=>'店铺评价','path'=>'BusinessComment/index','authdata'=>'BusinessComment/*'];
		}
        if($isadmin) {
        //    $system_child[] = ['name' => '慧动企微', 'path' => 'Backstage/huidong', 'authdata' => 'Backstage/huidong'];
        }

		$system_child[] = ['name'=>'操作日志','path'=>'Backstage/plog','authdata'=>'Backstage/plog'];
		$menudata['system'] = ['name'=>'系统','fullname'=>'系统设置','icon'=>'my-icon my-icon-sysset','child'=>$system_child];

        
		if($user && $user['auth_type']==0){
		    
		    
			if($user['groupid']){
				$user['auth_data'] = Db::name('admin_user_group')->where('id',$user['groupid'])->value('auth_data');
			}

			$auth_data = json_decode($user['auth_data'],true);
			
			
			foreach($menudata as $k=>$v){
				if($v['child']){
					foreach($v['child'] as $k1=>$v1){
						if(!$v1['authdata'] && $v1['child']){
							$path = array();
							foreach($v1['child'] as $k2=>$v2){
								if(!in_array($v2['path'].','.$v2['authdata'],$auth_data)){
									unset($menudata[$k]['child'][$k1]['child'][$k2]);
								}
							}
							if(count($menudata[$k]['child'][$k1]['child'])==0){
								unset($menudata[$k]['child'][$k1]);
							}
						}else{
							if(!in_array($v1['path'].','.$v1['authdata'],$auth_data)){
								unset($menudata[$k]['child'][$k1]);
							}
						}
					}
					if(count($menudata[$k]['child'])==0){
						unset($menudata[$k]);
					}
				}else{
					if(!in_array($v['path'].','.$v['authdata'],$auth_data)){
						unset($menudata[$k]);
					}
				}
			}
			
		}
		// 调试输出处理后的菜单数据
// var_dump($menudata);
// die(); // 
		return $menudata;
		
	}
	//白名单 不校验权限
	public static function blacklist(){
		$data = [];
		$data[] = 'Backstage/index';
		$data[] = 'Backstage/welcome';
		$data[] = 'Backstage/setpwd';
		$data[] = 'Backstage/about';
		$data[] = 'Help/*';
		$data[] = 'Upload/*';
		$data[] = 'DesignerPage/chooseurl';
		$data[] = 'Peisong/getpeisonguser';
		$data[] = 'Peisong/peisong';
		$data[] = 'Miandan/addorder';
		$data[] = 'Wxset/*';
		$data[] = 'Notice/*';
		$data[] = 'notice/*';
		$data[] = 'SxpayIncome/*';
		$data[] = 'Member/inputlockpwd';
		$data[] = 'MemberLevel/inputlockpwd';
		$data[] = 'ShopProduct/inputlockpwd';
		$data[] = 'Member/dolock';
		$data[] = 'MemberLevel/dolock';
		$data[] = 'ShopProduct/dolock';
		return $data;
	}

}
