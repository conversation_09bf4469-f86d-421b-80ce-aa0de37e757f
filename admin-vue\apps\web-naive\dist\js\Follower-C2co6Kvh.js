import{ay as ut,a7 as H,o as D,a as C,Z as F,az as dt,aA as pt,aB as ht,aC as ft,aD as T,aE as mt,aF as vt,aG as ct,ao as bt,aH as wt}from"./bootstrap-B_sue86n.js";import{j as _,f as X,g as O,b as y,d as L,P as xt,U as yt,a4 as Z,h as N,w as z,E as V,z as Mt}from"../jse/index-index-UaL0SrHU.js";let E=[];const J=new WeakMap;function $t(){E.forEach(n=>n(...J.get(n))),E=[]}function gt(n,...t){J.set(n,t),!E.includes(n)&&E.push(n)===1&&requestAnimationFrame($t)}let $,g;const zt=()=>{var n,t;$=ut?(t=(n=document)===null||n===void 0?void 0:n.fonts)===null||t===void 0?void 0:t.ready:void 0,g=!1,$!==void 0?$.then(()=>{g=!0}):g=!0};zt();function Bt(n){if(g)return;let t=!1;_(()=>{g||$==null||$.then(()=>{t||n()})}),X(()=>{t=!0})}const _t=H("n-internal-select-menu"),At=H("n-internal-select-menu-body"),Q="__disabled__";function R(n){const t=y(dt,null),e=y(pt,null),l=y(ht,null),a=y(At,null),m=O();if(typeof document!="undefined"){m.value=document.fullscreenElement;const u=()=>{m.value=document.fullscreenElement};_(()=>{D("fullscreenchange",document,u)}),X(()=>{C("fullscreenchange",document,u)})}return F(()=>{var u;const{to:h}=n;return h!==void 0?h===!1?Q:h===!0?m.value||"body":h:t!=null&&t.value?(u=t.value.$el)!==null&&u!==void 0?u:t.value:e!=null&&e.value?e.value:l!=null&&l.value?l.value:a!=null&&a.value?a.value:h!=null?h:m.value||"body"})}R.tdkey=Q;R.propTo={type:[String,Object,Boolean],default:void 0};let x=null;function tt(){if(x===null&&(x=document.getElementById("v-binder-view-measurer"),x===null)){x=document.createElement("div"),x.id="v-binder-view-measurer";const{style:n}=x;n.position="fixed",n.left="0",n.right="0",n.top="0",n.bottom="0",n.pointerEvents="none",n.visibility="hidden",document.body.appendChild(x)}return x.getBoundingClientRect()}function Ct(n,t){const e=tt();return{top:t,left:n,height:0,width:0,right:e.width-n,bottom:e.height-t}}function j(n){const t=n.getBoundingClientRect(),e=tt();return{left:t.left-e.left,top:t.top-e.top,bottom:e.height+e.top-t.bottom,right:e.width+e.left-t.right,width:t.width,height:t.height}}function Ot(n){return n.nodeType===9?null:n.parentNode}function et(n){if(n===null)return null;const t=Ot(n);if(t===null)return null;if(t.nodeType===9)return document;if(t.nodeType===1){const{overflow:e,overflowX:l,overflowY:a}=getComputedStyle(t);if(/(auto|scroll|overlay)/.test(e+a+l))return t}return et(t)}const Lt=L({name:"Binder",props:{syncTargetWithParent:Boolean,syncTarget:{type:Boolean,default:!0}},setup(n){var t;xt("VBinder",(t=yt())===null||t===void 0?void 0:t.proxy);const e=y("VBinder",null),l=O(null),a=r=>{l.value=r,e&&n.syncTargetWithParent&&e.setTargetRef(r)};let m=[];const u=()=>{let r=l.value;for(;r=et(r),r!==null;)m.push(r);for(const c of m)D("scroll",c,b,!0)},h=()=>{for(const r of m)C("scroll",r,b,!0);m=[]},i=new Set,v=r=>{i.size===0&&u(),i.has(r)||i.add(r)},w=r=>{i.has(r)&&i.delete(r),i.size===0&&h()},b=()=>{gt(o)},o=()=>{i.forEach(r=>r())},s=new Set,p=r=>{s.size===0&&D("resize",window,f),s.has(r)||s.add(r)},d=r=>{s.has(r)&&s.delete(r),s.size===0&&C("resize",window,f)},f=()=>{s.forEach(r=>r())};return X(()=>{C("resize",window,f),h()}),{targetRef:l,setTargetRef:a,addScrollListener:v,removeScrollListener:w,addResizeListener:p,removeResizeListener:d}},render(){return ft("binder",this.$slots)}}),Wt=L({name:"Target",setup(){const{setTargetRef:n,syncTarget:t}=y("VBinder");return{syncTarget:t,setTargetDirective:{mounted:n,updated:n}}},render(){const{syncTarget:n,setTargetDirective:t}=this;return n?Z(T("follower",this.$slots),[[t]]):T("follower",this.$slots)}}),{c:B}=mt(),Et="vueuc-style",A={top:"bottom",bottom:"top",left:"right",right:"left"},U={start:"end",center:"center",end:"start"},S={top:"height",bottom:"height",left:"width",right:"width"},Xt={"bottom-start":"top left",bottom:"top center","bottom-end":"top right","top-start":"bottom left",top:"bottom center","top-end":"bottom right","right-start":"top left",right:"center left","right-end":"bottom left","left-start":"top right",left:"center right","left-end":"bottom right"},Yt={"bottom-start":"bottom left",bottom:"bottom center","bottom-end":"bottom right","top-start":"top left",top:"top center","top-end":"top right","right-start":"top right",right:"center right","right-end":"bottom right","left-start":"top left",left:"center left","left-end":"bottom left"},It={"bottom-start":"right","bottom-end":"left","top-start":"right","top-end":"left","right-start":"bottom","right-end":"top","left-start":"bottom","left-end":"top"},q={top:!0,bottom:!1,left:!0,right:!1},G={top:"end",bottom:"start",left:"end",right:"start"};function Nt(n,t,e,l,a,m){if(!a||m)return{placement:n,top:0,left:0};const[u,h]=n.split("-");let i=h!=null?h:"center",v={top:0,left:0};const w=(s,p,d)=>{let f=0,r=0;const c=e[s]-t[p]-t[s];return c>0&&l&&(d?r=q[p]?c:-c:f=q[p]?c:-c),{left:f,top:r}},b=u==="left"||u==="right";if(i!=="center"){const s=It[n],p=A[s],d=S[s];if(e[d]>t[d]){if(t[s]+t[d]<e[d]){const f=(e[d]-t[d])/2;t[s]<f||t[p]<f?t[s]<t[p]?(i=U[h],v=w(d,p,b)):v=w(d,s,b):i="center"}}else e[d]<t[d]&&t[p]<0&&t[s]>t[p]&&(i=U[h])}else{const s=u==="bottom"||u==="top"?"left":"top",p=A[s],d=S[s],f=(e[d]-t[d])/2;(t[s]<f||t[p]<f)&&(t[s]>t[p]?(i=G[s],v=w(d,s,b)):(i=G[p],v=w(d,p,b)))}let o=u;return t[u]<e[S[u]]&&t[u]<t[A[u]]&&(o=A[u]),{placement:i!=="center"?`${o}-${i}`:o,left:v.left,top:v.top}}function Vt(n,t){return t?Yt[n]:Xt[n]}function jt(n,t,e,l,a,m){if(m)switch(n){case"bottom-start":return{top:`${Math.round(e.top-t.top+e.height)}px`,left:`${Math.round(e.left-t.left)}px`,transform:"translateY(-100%)"};case"bottom-end":return{top:`${Math.round(e.top-t.top+e.height)}px`,left:`${Math.round(e.left-t.left+e.width)}px`,transform:"translateX(-100%) translateY(-100%)"};case"top-start":return{top:`${Math.round(e.top-t.top)}px`,left:`${Math.round(e.left-t.left)}px`,transform:""};case"top-end":return{top:`${Math.round(e.top-t.top)}px`,left:`${Math.round(e.left-t.left+e.width)}px`,transform:"translateX(-100%)"};case"right-start":return{top:`${Math.round(e.top-t.top)}px`,left:`${Math.round(e.left-t.left+e.width)}px`,transform:"translateX(-100%)"};case"right-end":return{top:`${Math.round(e.top-t.top+e.height)}px`,left:`${Math.round(e.left-t.left+e.width)}px`,transform:"translateX(-100%) translateY(-100%)"};case"left-start":return{top:`${Math.round(e.top-t.top)}px`,left:`${Math.round(e.left-t.left)}px`,transform:""};case"left-end":return{top:`${Math.round(e.top-t.top+e.height)}px`,left:`${Math.round(e.left-t.left)}px`,transform:"translateY(-100%)"};case"top":return{top:`${Math.round(e.top-t.top)}px`,left:`${Math.round(e.left-t.left+e.width/2)}px`,transform:"translateX(-50%)"};case"right":return{top:`${Math.round(e.top-t.top+e.height/2)}px`,left:`${Math.round(e.left-t.left+e.width)}px`,transform:"translateX(-100%) translateY(-50%)"};case"left":return{top:`${Math.round(e.top-t.top+e.height/2)}px`,left:`${Math.round(e.left-t.left)}px`,transform:"translateY(-50%)"};case"bottom":default:return{top:`${Math.round(e.top-t.top+e.height)}px`,left:`${Math.round(e.left-t.left+e.width/2)}px`,transform:"translateX(-50%) translateY(-100%)"}}switch(n){case"bottom-start":return{top:`${Math.round(e.top-t.top+e.height+l)}px`,left:`${Math.round(e.left-t.left+a)}px`,transform:""};case"bottom-end":return{top:`${Math.round(e.top-t.top+e.height+l)}px`,left:`${Math.round(e.left-t.left+e.width+a)}px`,transform:"translateX(-100%)"};case"top-start":return{top:`${Math.round(e.top-t.top+l)}px`,left:`${Math.round(e.left-t.left+a)}px`,transform:"translateY(-100%)"};case"top-end":return{top:`${Math.round(e.top-t.top+l)}px`,left:`${Math.round(e.left-t.left+e.width+a)}px`,transform:"translateX(-100%) translateY(-100%)"};case"right-start":return{top:`${Math.round(e.top-t.top+l)}px`,left:`${Math.round(e.left-t.left+e.width+a)}px`,transform:""};case"right-end":return{top:`${Math.round(e.top-t.top+e.height+l)}px`,left:`${Math.round(e.left-t.left+e.width+a)}px`,transform:"translateY(-100%)"};case"left-start":return{top:`${Math.round(e.top-t.top+l)}px`,left:`${Math.round(e.left-t.left+a)}px`,transform:"translateX(-100%)"};case"left-end":return{top:`${Math.round(e.top-t.top+e.height+l)}px`,left:`${Math.round(e.left-t.left+a)}px`,transform:"translateX(-100%) translateY(-100%)"};case"top":return{top:`${Math.round(e.top-t.top+l)}px`,left:`${Math.round(e.left-t.left+e.width/2+a)}px`,transform:"translateY(-100%) translateX(-50%)"};case"right":return{top:`${Math.round(e.top-t.top+e.height/2+l)}px`,left:`${Math.round(e.left-t.left+e.width+a)}px`,transform:"translateY(-50%)"};case"left":return{top:`${Math.round(e.top-t.top+e.height/2+l)}px`,left:`${Math.round(e.left-t.left+a)}px`,transform:"translateY(-50%) translateX(-100%)"};case"bottom":default:return{top:`${Math.round(e.top-t.top+e.height+l)}px`,left:`${Math.round(e.left-t.left+e.width/2+a)}px`,transform:"translateX(-50%)"}}}const St=B([B(".v-binder-follower-container",{position:"absolute",left:"0",right:"0",top:"0",height:"0",pointerEvents:"none",zIndex:"auto"}),B(".v-binder-follower-content",{position:"absolute",zIndex:"auto"},[B("> *",{pointerEvents:"all"})])]),kt=L({name:"Follower",inheritAttrs:!1,props:{show:Boolean,enabled:{type:Boolean,default:void 0},placement:{type:String,default:"bottom"},syncTrigger:{type:Array,default:["resize","scroll"]},to:[String,Object],flip:{type:Boolean,default:!0},internalShift:Boolean,x:Number,y:Number,width:String,minWidth:String,containerClass:String,teleportDisabled:Boolean,zindexable:{type:Boolean,default:!0},zIndex:Number,overlap:Boolean},setup(n){const t=y("VBinder"),e=F(()=>n.enabled!==void 0?n.enabled:n.show),l=O(null),a=O(null),m=()=>{const{syncTrigger:o}=n;o.includes("scroll")&&t.addScrollListener(i),o.includes("resize")&&t.addResizeListener(i)},u=()=>{t.removeScrollListener(i),t.removeResizeListener(i)};_(()=>{e.value&&(i(),m())});const h=bt();St.mount({id:"vueuc/binder",head:!0,anchorMetaName:Et,ssr:h}),X(()=>{u()}),Bt(()=>{e.value&&i()});const i=()=>{if(!e.value)return;const o=l.value;if(o===null)return;const s=t.targetRef,{x:p,y:d,overlap:f}=n,r=p!==void 0&&d!==void 0?Ct(p,d):j(s);o.style.setProperty("--v-target-width",`${Math.round(r.width)}px`),o.style.setProperty("--v-target-height",`${Math.round(r.height)}px`);const{width:c,minWidth:Y,placement:W,internalShift:nt,flip:ot}=n;o.setAttribute("v-placement",W),f?o.setAttribute("v-overlap",""):o.removeAttribute("v-overlap");const{style:M}=o;c==="target"?M.width=`${r.width}px`:c!==void 0?M.width=c:M.width="",Y==="target"?M.minWidth=`${r.width}px`:Y!==void 0?M.minWidth=Y:M.minWidth="";const rt=j(o),it=j(a.value),{left:k,top:K,placement:I}=Nt(W,r,rt,nt,ot,f),P=Vt(I,f),{left:lt,top:st,transform:at}=jt(I,it,r,K,k,f);o.setAttribute("v-placement",I),o.style.setProperty("--v-offset-left",`${Math.round(k)}px`),o.style.setProperty("--v-offset-top",`${Math.round(K)}px`),o.style.transform=`translateX(${lt}) translateY(${st}) ${at}`,o.style.setProperty("--v-transform-origin",P),o.style.transformOrigin=P};z(e,o=>{o?(m(),v()):u()});const v=()=>{Mt().then(i).catch(o=>console.error(o))};["placement","x","y","internalShift","flip","width","overlap","minWidth"].forEach(o=>{z(V(n,o),i)}),["teleportDisabled"].forEach(o=>{z(V(n,o),v)}),z(V(n,"syncTrigger"),o=>{o.includes("resize")?t.addResizeListener(i):t.removeResizeListener(i),o.includes("scroll")?t.addScrollListener(i):t.removeScrollListener(i)});const w=wt(),b=F(()=>{const{to:o}=n;if(o!==void 0)return o;w.value});return{VBinder:t,mergedEnabled:e,offsetContainerRef:a,followerRef:l,mergedTo:b,syncPosition:i}},render(){return N(vt,{show:this.show,to:this.mergedTo,disabled:this.teleportDisabled},{default:()=>{var n,t;const e=N("div",{class:["v-binder-follower-container",this.containerClass],ref:"offsetContainerRef"},[N("div",{class:"v-binder-follower-content",ref:"followerRef"},(t=(n=this.$slots).default)===null||t===void 0?void 0:t.call(n))]);return this.zindexable?Z(e,[[ct,{enabled:this.mergedEnabled,zIndex:this.zIndex}]]):e}})}});export{Lt as B,Wt as V,Et as a,gt as b,B as c,kt as d,At as e,_t as i,R as u};
