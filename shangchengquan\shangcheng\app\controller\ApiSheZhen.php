<?php
/**
 * 鲸犀商城 - 微信公众号小程序商城系统!
 * Copyright © 2020 鲸犀网络科技有限公司 保留所有权利
 * =========================================================
 * 版本：供应链版本
 * 授权主体：无限制版本
 * 授权域名：@@@
     
 * ----------------------------------------------
 * 您只能在商业授权范围内使用，不可二次转售、分发、分享、传播
 * 任何企业和个人不得对代码以任何目的任何形式的再发布
 * =========================================================
 */

// +----------------------------------------------------------------------
// | 舌诊API接口
// +----------------------------------------------------------------------
namespace app\controller;
use think\facade\Db;
use think\facade\Log;

class ApiSheZhen extends ApiCommon
{
    protected $bid = 0; // 添加bid属性定义
    
    public function initialize(){
        parent::initialize();
        // 2025-01-03 22:55:53,565-INFO-[ApiSheZhen][initialize_001] 初始化舌诊API控制器
        $this->checklogin(); // 统一检查登录状态
    
    }

    // 2025-01-03 22:55:53,565-INFO-[ApiSheZhen][getConfig_001] 获取舌诊配置信息
    // 2025-01-17 扩展：支持面诊和综合诊疗配置
    public function getConfig(){
        $diagnosisType = input('param.diagnosis_type/d', 1); // 默认舌诊

        // 2025-01-03 22:55:53,565-INFO-[ApiSheZhen][getConfig_002] 获取配置数据
        $config = Db::name('shezhen_set')->where('aid', $this->aid)->find();
        if(!$config){
            return $this->json(['code'=>0,'msg'=>'诊疗功能未开启']);
        }

        // 2025-01-17 根据诊疗类型检查是否启用
        $isEnabled = false;
        switch($diagnosisType) {
            case 1: // 舌诊
                $isEnabled = $config['is_open'] == 1;
                break;
            case 2: // 面诊
                $isEnabled = isset($config['face_diagnosis_enable']) && $config['face_diagnosis_enable'] == 1;
                break;
            case 3: // 综合诊疗
                $isEnabled = isset($config['comprehensive_diagnosis_enable']) && $config['comprehensive_diagnosis_enable'] == 1;
                break;
        }

        if(!$isEnabled){
            $typeName = \app\common\SheZhen::getDiagnosisTypeName($diagnosisType);
            return $this->json(['code'=>0,'msg'=>$typeName.'功能已关闭']);
        }
        
        // 2025-01-17 根据诊疗类型获取对应配置
        $diagnosisConfig = \app\common\SheZhen::getDiagnosisConfig($this->aid, $diagnosisType);
        if (!$diagnosisConfig) {
            return $this->json(['code'=>0,'msg'=>'获取诊疗配置失败']);
        }

        // 2025-01-17 处理用户等级权限
        $userLevel = $this->member['levelid'] ?? 0;
        $freeLevel = explode(',', $diagnosisConfig['free_level']);
        $isFree = in_array($userLevel, $freeLevel);

        // 2025-01-17 检查今日免费次数（按诊疗类型统计）
        $todayStart = strtotime(date('Y-m-d'));
        $todayCount = Db::name('shezhen_record')
            ->where('aid', $this->aid)
            ->where('mid', $this->mid)
            ->where('diagnosis_type', $diagnosisType)
            ->where('is_free', 1)
            ->where('createtime', '>=', $todayStart)
            ->count();

        $dailyFreeCount = intval($diagnosisConfig['daily_free_count']) ?: 1;
        $canUseFree = $isFree && $todayCount < $dailyFreeCount;
        
        // 2025-01-03 22:55:53,565-INFO-[ApiSheZhen][getConfig_005] 检查接口配置类型
        $hasOwnAliyun = !empty($config['aliyun_access_key']) && !empty($config['aliyun_secret_key']) && !empty($config['aliyun_endpoint']);
        $usePublicApi = !$hasOwnAliyun;
        
        // 2025-01-03 22:55:53,565-INFO-[ApiSheZhen][getConfig_006] 如果使用公共接口，检查平台次数
        $platformCallsRemaining = 0;
        if($usePublicApi){
            $adminInfo = Db::name('admin')->where('id', $this->aid)->field('mianzhen_num')->find();
            $platformCallsRemaining = $adminInfo ? intval($adminInfo['mianzhen_num']) : 0;
        }
        
        $typeName = \app\common\SheZhen::getDiagnosisTypeName($diagnosisType);
        $result = [
            'diagnosis_type' => $diagnosisType,
            'diagnosis_type_name' => $typeName,
            'price' => $diagnosisConfig['price'],
            'free_times' => $dailyFreeCount,
            'today_used' => $todayCount,
            'can_use_free' => $canUseFree,
            'is_vip' => $isFree,
            'description' => $config['description'] ?? '专业'.$typeName.'分析，了解您的体质状况',
            'use_own_aliyun' => $hasOwnAliyun,
            'use_public_api' => $usePublicApi,
            'platform_calls_remaining' => $platformCallsRemaining,
            'recharge_url' => $config['recharge_url'] ?? '/pagesExb/money/recharge',
            // 2025-01-27 新增前端显示设置 - 控制各模块是否显示
            'display_settings' => [
                'show_score' => isset($config['show_score']) ? intval($config['show_score']) : 1,
                'show_score_value' => isset($config['show_score_value']) ? intval($config['show_score_value']) : 1,
                'show_symptoms' => isset($config['show_symptoms']) ? intval($config['show_symptoms']) : 1,
                'show_tongue_analysis' => isset($config['show_tongue_analysis']) ? intval($config['show_tongue_analysis']) : 1,
                'show_care_advice' => isset($config['show_care_advice']) ? intval($config['show_care_advice']) : 1,
                'show_product_recommend' => isset($config['show_product_recommend']) ? intval($config['show_product_recommend']) : 1
            ]
        ];
        
        return $this->json(['code'=>1,'data'=>$result]);
    }

    // 2025-01-03 22:55:53,565-INFO-[ApiSheZhen][analyze_001] 舌诊分析
    // 2025-01-17 扩展：支持面诊和综合诊疗
    public function analyze(){
        $diagnosisType = input('post.diagnosis_type/d', 1); // 诊疗类型
        $tongueImageUrl = input('post.tongue_image_url'); // 舌头图片
        $faceImageUrl = input('post.face_image_url'); // 面部图片
        $sublingualImageUrl = input('post.sublingual_image_url'); // 舌下脉络图片
        $useFree = input('post.use_free/d', 0);

        // 2025-07-17 兼容性处理：支持旧版本的image_url参数
        $legacyImageUrl = input('post.image_url'); // 兼容旧版本参数
        if ($legacyImageUrl && !$tongueImageUrl) {
            $tongueImageUrl = $legacyImageUrl; // 将旧参数映射为舌头图片
            \think\facade\Log::info('2025-07-17 INFO-[ApiSheZhen][analyze_legacy] 使用兼容模式，将image_url映射为tongue_image_url: ' . $legacyImageUrl);
        }

        // 2025-01-17 构建图片数据
        $imageData = [];
        if ($tongueImageUrl) $imageData['tf_image'] = $tongueImageUrl;
        if ($faceImageUrl) $imageData['ff_image'] = $faceImageUrl;
        if ($sublingualImageUrl) $imageData['tb_image'] = $sublingualImageUrl;

        // 2025-01-17 验证图片参数
        $validationResult = \app\common\SheZhen::validateImagesByDiagnosisType($imageData, $diagnosisType);
        if ($validationResult['status'] == 0) {
            // 2025-07-17 添加操作日志
            \app\common\System::plog('舌诊分析失败：' . $validationResult['msg']);
            return $this->json(['code'=>0,'msg'=>$validationResult['msg']]);
        }
        
        // 2025-01-17 获取配置信息
        $config = Db::name('shezhen_set')->where('aid', $this->aid)->find();
        if(!$config){
            // 2025-07-17 添加操作日志
            \app\common\System::plog('舌诊分析失败：诊疗功能未开启');
            return $this->json(['code'=>0,'msg'=>'诊疗功能未开启']);
        }

        // 2025-01-17 检查对应诊疗类型是否启用
        $diagnosisConfig = \app\common\SheZhen::getDiagnosisConfig($this->aid, $diagnosisType);
        if (!$diagnosisConfig || !$diagnosisConfig['is_enable']) {
            $typeName = \app\common\SheZhen::getDiagnosisTypeName($diagnosisType);
            return $this->json(['code'=>0,'msg'=>$typeName.'功能未开启']);
        }
        
        // 2025-01-03 22:55:53,565-INFO-[ApiSheZhen][analyze_002_1] 判断使用自有阿里云还是公共接口
        $hasOwnAliyun = !empty($config['aliyun_access_key']) && !empty($config['aliyun_secret_key']) && !empty($config['aliyun_endpoint']);
        $usePublicApi = !$hasOwnAliyun;
        
        // 2025-01-03 22:55:53,565-INFO-[ApiSheZhen][analyze_002_2] 如果使用公共接口，检查平台次数限制
        if($usePublicApi){
            $limitCheck = \app\common\SheZhen::checkCallLimit($this->aid);
            if ($limitCheck['status'] == 0) {
                return $this->json(['code'=>0,'msg'=>$limitCheck['msg']]);
            }
        }
        
        // 2025-01-17 检查免费权限（按诊疗类型）
        $price = $diagnosisConfig['price'];
        $isFree = false;

        if($useFree){
            $userLevel = $this->member['levelid'] ?? 0;
            $freeLevel = explode(',', $diagnosisConfig['free_level']);

            if(in_array($userLevel, $freeLevel)){
                $todayStart = strtotime(date('Y-m-d'));
                $todayCount = Db::name('shezhen_record')
                    ->where('aid', $this->aid)
                    ->where('mid', $this->mid)
                    ->where('diagnosis_type', $diagnosisType)
                    ->where('is_free', 1)
                    ->where('createtime', '>=', $todayStart)
                    ->count();

                if($todayCount < $diagnosisConfig['daily_free_count']){
                    $isFree = true;
                    $price = 0;
                }
            }
        }
        
        // 2025-01-03 22:55:53,565-INFO-[ApiSheZhen][analyze_004] 检查用户余额
        if(!$isFree && $price > 0){
            if($this->member['money'] < $price){
                return $this->json(['code'=>0,'msg'=>'余额不足，请先充值']);
            }
        }
        
        // 2025-01-17 调用对应的API进行分析
        if($hasOwnAliyun){
            // 使用自有阿里云配置
            $analysisResult = $this->callOwnAliyunApiMultiImage($imageData, $config, $diagnosisType);
        }else{
            // 使用公共接口
            $analysisResult = $this->callPublicApiMultiImage($imageData, $config, $diagnosisType);
        }
        
        if(!$analysisResult['success']){
            return $this->json(['code'=>0,'msg'=>$analysisResult['message']]);
        }
        
        // 2025-01-03 22:55:53,565-INFO-[ApiSheZhen][analyze_006] 生成订单号
        $orderNo = 'SZ' . date('YmdHis') . rand(1000, 9999);
        
        // 2025-01-17 保存分析记录（支持多图片）
        $recordData = [
            'aid' => $this->aid,
            'bid' => $this->bid,
            'mid' => $this->mid,
            'order_no' => $orderNo,
            'tongue_image' => $imageData['tf_image'] ?? '',
            'face_image' => $imageData['ff_image'] ?? '',
            'sublingual_image' => $imageData['tb_image'] ?? '',
            'diagnosis_type' => $diagnosisType,
            'analysis_result' => json_encode($analysisResult['data'], JSON_UNESCAPED_UNICODE),
            'constitution_type' => $analysisResult['data']['constitution_type'] ?? '未知',
            'constitution_score' => $analysisResult['data']['constitution_score'] ?? 0,
            'health_suggestions' => $analysisResult['data']['health_suggestions'] ?? '',
            'diet_suggestions' => $analysisResult['data']['diet_suggestions'] ?? '',
            'exercise_suggestions' => $analysisResult['data']['exercise_suggestions'] ?? '',
            'price' => $price,
            'is_free' => $isFree ? 1 : 0,
            'api_type' => $hasOwnAliyun ? 'own_aliyun' : 'public_api', // 记录使用的接口类型
            'status' => 1,
            'createtime' => time(),
            'updatetime' => time()
        ];
        
        Db::startTrans();
        try {
            $recordId = Db::name('shezhen_record')->insertGetId($recordData);
          
            // 2025-01-27 16:30:00,001-INFO-[ApiSheZhen][analyze_008] 扣除用户余额（如果不是免费）
            if(!$isFree && $price > 0){
                \app\common\Member::addmoney($this->aid, $this->mid, -$price, '舌诊分析服务,订单号: '.$orderNo, $recordId);
            }
        
            // 2025-01-03 22:55:53,565-INFO-[ApiSheZhen][analyze_008_1] 如果使用公共接口，扣除站点面诊次数
            if($usePublicApi){
                $deductResult = \app\common\SheZhen::deductMianzhenNum($this->aid);
                if ($deductResult['status'] == 0) {
                    Db::rollback();
                    return $this->json(['code'=>0,'msg'=>$deductResult['msg']]);
                }
              
            }
            
            // 2025-01-03 22:55:53,565-INFO-[ApiSheZhen][analyze_009] 生成详细报告
            $this->generateReport($recordId, $analysisResult['data']);
            
            Db::commit();

            // 2025-07-17 添加操作日志
            \app\common\System::plog('舌诊分析成功，订单号：' . $orderNo . '，记录ID：' . $recordId);

            return $this->json(['code'=>1,'data'=>['record_id'=>$recordId,'order_no'=>$orderNo,'api_type'=>$hasOwnAliyun ? 'own_aliyun' : 'public_api'],'msg'=>'分析完成']);
            
        } catch (\Exception $e) {
            Db::rollback();

            // 2025-07-17 添加操作日志
            \app\common\System::plog('舌诊分析异常：' . $e->getMessage());

            return $this->json(['code'=>0,'msg'=>'分析失败：'.$e->getMessage()]);
        }
    }

    // 2025-01-03 22:55:53,565-INFO-[ApiSheZhen][getRecord_001] 获取分析记录
    public function getRecord(){
        $recordId = input('param.id/d');
        $includeRecommend = input('param.include_recommend/d', 1); // 默认包含推荐商品
        
        if(!$recordId){
            return $this->json(['code'=>0,'msg'=>'参数错误']);
        }
        
        // 2025-01-03 22:55:53,565-INFO-[ApiSheZhen][getRecord_002] 查询记录详情
        $record = Db::name('shezhen_record')
            ->where('aid', $this->aid)
            ->where('mid', $this->mid)
            ->where('id', $recordId)
            ->where('status', 1)
            ->find();
            
        if(!$record){
            return $this->json(['code'=>0,'msg'=>'记录不存在']);
        }
        
        // 2025-01-27 获取显示设置配置
        $config = Db::name('shezhen_set')->where('aid', $this->aid)->find();
        $record['display_settings'] = [
            'show_score' => isset($config['show_score']) ? intval($config['show_score']) : 1, // 默认显示评分
            'show_score_value' => isset($config['show_score_value']) ? intval($config['show_score_value']) : 1, // 2025-01-27 默认显示评分分值
            'show_symptoms' => isset($config['show_symptoms']) ? intval($config['show_symptoms']) : 1, // 默认显示体征
            'show_tongue_analysis' => isset($config['show_tongue_analysis']) ? intval($config['show_tongue_analysis']) : 1, // 默认显示舌象分析
            'show_care_advice' => isset($config['show_care_advice']) ? intval($config['show_care_advice']) : 1, // 默认显示调理建议
            'show_product_recommend' => isset($config['show_product_recommend']) ? intval($config['show_product_recommend']) : 1 // 默认显示商品推荐
        ];
        
        // 2025-01-17 处理图片URL（支持面诊和综合诊疗）
        if ($record['tongue_image']) {
            $record['tongue_image'] = $this->getFullImageUrl($record['tongue_image']);
        }
        if (isset($record['face_image']) && $record['face_image']) {
            $record['face_image'] = $this->getFullImageUrl($record['face_image']);
        }
        if (isset($record['sublingual_image']) && $record['sublingual_image']) {
            $record['sublingual_image'] = $this->getFullImageUrl($record['sublingual_image']);
        }

        // 2025-01-17 获取诊疗类型名称
        $record['diagnosis_type_name'] = \app\common\SheZhen::getDiagnosisTypeName($record['diagnosis_type'] ?? 1);

        // 2025-01-03 22:55:53,565-INFO-[ApiSheZhen][getRecord_003] 处理分析结果
        if($record['analysis_result']){
            $record['analysis_result'] = json_decode($record['analysis_result'], true);
        }
        
        // 2025-01-03 22:55:53,565-INFO-[ApiSheZhen][getRecord_004] 获取报告信息
        $report = Db::name('shezhen_report')->where('record_id', $recordId)->find();
        $record['report'] = $report;
        
        // 格式化时间
        $record['createtime_format'] = date('Y-m-d H:i:s', $record['createtime']);
        
        // 2025-01-03 22:55:53,565-INFO-[ApiSheZhen][getRecord_005] 获取推荐商品（基于显示设置）
        $record['recommend_products'] = [];
        if($includeRecommend && $record['display_settings']['show_product_recommend'] && ($record['constitution_type'] || $record['constitution_score'])){
            $recommendResult = $this->getRecommendProductsData(
                $record['constitution_type'], 
                intval($record['constitution_score'])
            );
            if($recommendResult['code'] == 1){
                $record['recommend_products'] = $recommendResult['data'];
            }
        }
        
        return $this->json(['code'=>1,'data'=>$record]);
    }

    // 2025-01-03 22:55:53,565-INFO-[ApiSheZhen][getRecommendProductsData_001] 内部获取推荐商品数据方法
    private function getRecommendProductsData($constitutionType, $constitutionScore){
        if(!$constitutionType && !$constitutionScore){
            return ['code'=>0, 'msg'=>'请提供体质类型或得分', 'data'=>[]];
        }
        
        // 获取舌诊设置
        $set = Db::name('shezhen_set')->where('aid', $this->aid)->find();
        if(!$set || $set['is_recommend_open'] != 1){
            return ['code'=>0, 'msg'=>'推荐功能未开启', 'data'=>[]];
        }
        
        // 2025-01-06 11:45:00,039-INFO-[ApiSheZhen][getRecommendProductsData_002] 获取用户会员等级
        $userLevelId = 0;
        if($this->member && isset($this->member['levelid'])){
            $userLevelId = intval($this->member['levelid']);
        }
       
        
        $where = [
            ['aid', '=', $this->aid],
            ['status', '=', 1]
        ];
        if($this->bid > 0){
            $where[] = ['bid', '=', $this->bid];
        }
        
        // 构建推荐商品查询条件
        $orWhere = [];
        
        // 2025-01-14 修复：处理多体质类型匹配和体质名称映射
        $constitutionTypes = [];
        if($constitutionType){
            // 体质类型映射表，解决不同名称对应同一体质的问题
            $constitutionMapping = [
                '火热体质' => '湿热质',
                '火热质' => '湿热质',
                '寒湿体质' => '痰湿质',
                '寒湿质' => '痰湿质',
                '血瘀体质' => '血瘀质',
                '血瘀质' => '血瘀质',
                '气虚体质' => '气虚质',
                '气虚质' => '气虚质',
                '阳虚体质' => '阳虚质',
                '阳虚质' => '阳虚质',
                '阴虚体质' => '阴虚质',
                '阴虚质' => '阴虚质',
                '痰湿体质' => '痰湿质',
                '痰湿质' => '痰湿质',
                '湿热体质' => '湿热质',
                '湿热质' => '湿热质',
                '气郁体质' => '气郁质',
                '气郁质' => '气郁质',
                '特禀体质' => '特禀质',
                '特禀质' => '特禀质',
                '平和体质' => '平和质',
                '平和质' => '平和质'
            ];
            
            // 分割多个体质类型
            $types = explode('、', $constitutionType);
            if(count($types) == 1) {
                $types = explode(',', $constitutionType);
            }
            
            foreach($types as $type){
                $type = trim($type);
                if($type){
                    // 先添加原始类型
                    $constitutionTypes[] = $type;
                    
                    // 检查映射表
                    if(isset($constitutionMapping[$type])){
                        $constitutionTypes[] = $constitutionMapping[$type];
                    }
                    
                    // 生成格式变换版本
                    if(strpos($type, '体质') !== false){
                        $constitutionTypes[] = str_replace('体质', '质', $type);
                    } elseif(strpos($type, '质') !== false && strpos($type, '体质') === false){
                        $constitutionTypes[] = str_replace('质', '体质', $type);
                    }
                }
            }
            $constitutionTypes = array_unique($constitutionTypes);
        }
        
        // 记录体质类型转换结果
        \think\facade\Log::info('ApiSheZhen体质类型转换，原始: ' . $constitutionType . '，转换后: ' . json_encode($constitutionTypes, JSON_UNESCAPED_UNICODE));
        
        // 2025-01-14 增加会员等级调试信息
        \think\facade\Log::info('ApiSheZhen用户会员等级信息，userLevelId: ' . $userLevelId . '，member信息: ' . json_encode($this->member, JSON_UNESCAPED_UNICODE));
        
        // 1. 按体质类型推荐（recommend_type = 1）
        if(!empty($constitutionTypes)){
            foreach($constitutionTypes as $type){
                $orWhere[] = [
                    ['recommend_type', '=', 1],
                    ['constitution_type', '=', $type]
                ];
                // 增加模糊匹配，以防数据库中存储的格式稍有不同
                $orWhere[] = [
                    ['recommend_type', '=', 1],
                    ['constitution_type', 'like', '%' . $type . '%']
                ];
            }
        }
        
        // 2. 按得分推荐（recommend_type = 2）
        if($constitutionScore > 0){
            $orWhere[] = [
                ['recommend_type', '=', 2],
                ['min_score', '<=', $constitutionScore],
                ['max_score', '>=', $constitutionScore]
            ];
        }
        
        // 3. 按体质+得分推荐（recommend_type = 3）
        if(!empty($constitutionTypes) && $constitutionScore > 0){
            foreach($constitutionTypes as $type){
                $orWhere[] = [
                    ['recommend_type', '=', 3],
                    ['constitution_type', '=', $type],
                    ['min_score', '<=', $constitutionScore],
                    ['max_score', '>=', $constitutionScore]
                ];
                // 增加模糊匹配
                $orWhere[] = [
                    ['recommend_type', '=', 3],
                    ['constitution_type', 'like', '%' . $type . '%'],
                    ['min_score', '<=', $constitutionScore],
                    ['max_score', '>=', $constitutionScore]
                ];
            }
        }
        
        // 4. 按会员等级推荐（recommend_type = 4）
        if($userLevelId > 0){
            $orWhere[] = [
                ['recommend_type', '=', 4],
                ['member_level_ids', '=', $userLevelId]
            ];
            $orWhere[] = [
                ['recommend_type', '=', 4],
                ['member_level_ids', 'like', $userLevelId . ',%']
            ];
            $orWhere[] = [
                ['recommend_type', '=', 4],
                ['member_level_ids', 'like', '%,' . $userLevelId . ',%']
            ];
            $orWhere[] = [
                ['recommend_type', '=', 4],
                ['member_level_ids', 'like', '%,' . $userLevelId]
            ];
        }
        
        // 5. 按体质+会员等级推荐（recommend_type = 5）
        if(!empty($constitutionTypes) && $userLevelId > 0){
            foreach($constitutionTypes as $type){
                // 精确匹配等级
                $orWhere[] = [
                    ['recommend_type', '=', 5],
                    ['constitution_type', '=', $type],
                    ['member_level_ids', '=', $userLevelId]
                ];
                // 开头匹配
                $orWhere[] = [
                    ['recommend_type', '=', 5],
                    ['constitution_type', '=', $type],
                    ['member_level_ids', 'like', $userLevelId . ',%']
                ];
                // 中间匹配
                $orWhere[] = [
                    ['recommend_type', '=', 5],
                    ['constitution_type', '=', $type],
                    ['member_level_ids', 'like', '%,' . $userLevelId . ',%']
                ];
                // 结尾匹配
                $orWhere[] = [
                    ['recommend_type', '=', 5],
                    ['constitution_type', '=', $type],
                    ['member_level_ids', 'like', '%,' . $userLevelId]
                ];
                
                // 增加模糊匹配体质类型
                $orWhere[] = [
                    ['recommend_type', '=', 5],
                    ['constitution_type', 'like', '%' . $type . '%'],
                    ['member_level_ids', '=', $userLevelId]
                ];
                $orWhere[] = [
                    ['recommend_type', '=', 5],
                    ['constitution_type', 'like', '%' . $type . '%'],
                    ['member_level_ids', 'like', $userLevelId . ',%']
                ];
                $orWhere[] = [
                    ['recommend_type', '=', 5],
                    ['constitution_type', 'like', '%' . $type . '%'],
                    ['member_level_ids', 'like', '%,' . $userLevelId . ',%']
                ];
                $orWhere[] = [
                    ['recommend_type', '=', 5],
                    ['constitution_type', 'like', '%' . $type . '%'],
                    ['member_level_ids', 'like', '%,' . $userLevelId]
                ];
            }
        }
        
        // 6. 按得分+会员等级推荐（recommend_type = 6）
        if($constitutionScore > 0 && $userLevelId > 0){
            $orWhere[] = [
                ['recommend_type', '=', 6],
                ['min_score', '<=', $constitutionScore],
                ['max_score', '>=', $constitutionScore],
                ['member_level_ids', '=', $userLevelId]
            ];
            $orWhere[] = [
                ['recommend_type', '=', 6],
                ['min_score', '<=', $constitutionScore],
                ['max_score', '>=', $constitutionScore],
                ['member_level_ids', 'like', $userLevelId . ',%']
            ];
            $orWhere[] = [
                ['recommend_type', '=', 6],
                ['min_score', '<=', $constitutionScore],
                ['max_score', '>=', $constitutionScore],
                ['member_level_ids', 'like', '%,' . $userLevelId . ',%']
            ];
            $orWhere[] = [
                ['recommend_type', '=', 6],
                ['min_score', '<=', $constitutionScore],
                ['max_score', '>=', $constitutionScore],
                ['member_level_ids', 'like', '%,' . $userLevelId]
            ];
        }
        
        // 7. 按体质+得分+会员等级推荐（recommend_type = 7）
        if(!empty($constitutionTypes) && $constitutionScore > 0 && $userLevelId > 0){
            foreach($constitutionTypes as $type){
                $orWhere[] = [
                    ['recommend_type', '=', 7],
                    ['constitution_type', '=', $type],
                    ['min_score', '<=', $constitutionScore],
                    ['max_score', '>=', $constitutionScore],
                    ['member_level_ids', '=', $userLevelId]
                ];
                $orWhere[] = [
                    ['recommend_type', '=', 7],
                    ['constitution_type', '=', $type],
                    ['min_score', '<=', $constitutionScore],
                    ['max_score', '>=', $constitutionScore],
                    ['member_level_ids', 'like', $userLevelId . ',%']
                ];
                $orWhere[] = [
                    ['recommend_type', '=', 7],
                    ['constitution_type', '=', $type],
                    ['min_score', '<=', $constitutionScore],
                    ['max_score', '>=', $constitutionScore],
                    ['member_level_ids', 'like', '%,' . $userLevelId . ',%']
                ];
                $orWhere[] = [
                    ['recommend_type', '=', 7],
                    ['constitution_type', '=', $type],
                    ['min_score', '<=', $constitutionScore],
                    ['max_score', '>=', $constitutionScore],
                    ['member_level_ids', 'like', '%,' . $userLevelId]
                ];
                
                // 增加模糊匹配
                $orWhere[] = [
                    ['recommend_type', '=', 7],
                    ['constitution_type', 'like', '%' . $type . '%'],
                    ['min_score', '<=', $constitutionScore],
                    ['max_score', '>=', $constitutionScore],
                    ['member_level_ids', '=', $userLevelId]
                ];
                $orWhere[] = [
                    ['recommend_type', '=', 7],
                    ['constitution_type', 'like', '%' . $type . '%'],
                    ['min_score', '<=', $constitutionScore],
                    ['max_score', '>=', $constitutionScore],
                    ['member_level_ids', 'like', $userLevelId . ',%']
                ];
                $orWhere[] = [
                    ['recommend_type', '=', 7],
                    ['constitution_type', 'like', '%' . $type . '%'],
                    ['min_score', '<=', $constitutionScore],
                    ['max_score', '>=', $constitutionScore],
                    ['member_level_ids', 'like', '%,' . $userLevelId . ',%']
                ];
                $orWhere[] = [
                    ['recommend_type', '=', 7],
                    ['constitution_type', 'like', '%' . $type . '%'],
                    ['min_score', '<=', $constitutionScore],
                    ['max_score', '>=', $constitutionScore],
                    ['member_level_ids', 'like', '%,' . $userLevelId]
                ];
            }
        }
        
        if(empty($orWhere)){
            return ['code'=>0, 'msg'=>'无符合条件的推荐商品', 'data'=>[]];
        }
        
        $recommendLimit = isset($set['recommend_count']) ? intval($set['recommend_count']) : 3;
        
        // 使用whereOr查询多个条件
        $query = Db::name('shezhen_recommend_product')->where($where);
        
        // 添加OR条件组
        $query->where(function($query) use ($orWhere) {
            foreach($orWhere as $index => $conditions) {
                if($index == 0) {
                    $query->where($conditions);
                } else {
                    $query->whereOr($conditions);
                }
            }
        });
        
        // 2025-01-14 增加调试日志，记录匹配的体质类型和查询条件
        \think\facade\Log::info('ApiSheZhen推荐商品查询，原始体质类型: ' . $constitutionType . '，解析后体质类型: ' . json_encode($constitutionTypes, JSON_UNESCAPED_UNICODE) . '，查询条件数量: ' . count($orWhere));
        
        // 2025-01-14 临时调试：查看数据库中血瘀质和湿热质的推荐配置
        $debugQuery = Db::name('shezhen_recommend_product')
            ->where('aid', $this->aid)
            ->where('status', 1)
            ->where(function($query) {
                $query->where('constitution_type', 'like', '%血瘀%')
                      ->whereOr('constitution_type', 'like', '%湿热%')
                      ->whereOr('constitution_type', 'like', '%火热%');
            })
            ->select()
            ->toArray();
        \think\facade\Log::info('ApiSheZhen调试查询血瘀质湿热质推荐配置: ' . json_encode($debugQuery, JSON_UNESCAPED_UNICODE));
        
        // 2025-01-14 临时调试：先尝试简单的精确匹配查询
        $simpleQuery = Db::name('shezhen_recommend_product')
            ->where('aid', $this->aid)
            ->where('status', 1)
            ->where('recommend_type', 5)
            ->where('member_level_ids', $userLevelId)
            ->where(function($query) use ($constitutionTypes) {
                foreach($constitutionTypes as $index => $type) {
                    if($index == 0) {
                        $query->where('constitution_type', $type);
                    } else {
                        $query->whereOr('constitution_type', $type);
                    }
                }
            })
            ->select()
            ->toArray();
        \think\facade\Log::info('ApiSheZhen简单精确匹配查询结果: ' . json_encode($simpleQuery, JSON_UNESCAPED_UNICODE));
        
        // 2025-01-14 临时使用简化查询，避免复杂OR条件干扰
        if(!empty($simpleQuery)) {
            $list = $simpleQuery;
            \think\facade\Log::info('ApiSheZhen使用简化查询结果，数量: ' . count($list));
        } else {
            $list = $query->order('sort desc, id desc')
                ->limit($recommendLimit)
                ->select()
                ->toArray();
            \think\facade\Log::info('ApiSheZhen使用复杂查询结果，数量: ' . count($list));
        }
        
        // 2025-01-14 记录查询结果数量
        \think\facade\Log::info('ApiSheZhen推荐商品查询结果数量: ' . count($list));
        
        // 2025-01-14 记录查询到的推荐配置详情
        \think\facade\Log::info('ApiSheZhen查询到的推荐配置详情: ' . json_encode($list, JSON_UNESCAPED_UNICODE));
        
        // 获取商品详情
        $productList = [];
        foreach($list as $item){
            if($item['product_type'] == 1){ // 商品
                $product = Db::name('shop_product')
                    ->where('id', $item['product_id'])
                    ->where('status', 1)
                    ->field('id,name,sell_price as price,pic,sell_price as original_price,stock,detail')
                    ->find();
                
                if($product){
                    $product['type'] = 'product';
                    $product['recommend_reason'] = $this->getRecommendReason($item, $constitutionType, $constitutionScore, $userLevelId);
                    $product['recommend_type'] = $item['recommend_type'];
                    $product['sort'] = $item['sort'];
                    $productList[] = $product;
                }
            }else{ // 课程
                $course = Db::name('kecheng_list')
                    ->where('id', $item['product_id'])
                    ->where('status', 1)
                    ->field('id,name,price,pic,market_price,detail')
                    ->find();
                
                if($course){
                    $course['type'] = 'course';
                    $course['original_price'] = $course['market_price'] ?: $course['price'];
                    $course['recommend_reason'] = $this->getRecommendReason($item, $constitutionType, $constitutionScore, $userLevelId);
                    $course['recommend_type'] = $item['recommend_type'];
                    $course['sort'] = $item['sort'];
                    $productList[] = $course;
                }
            }
        }
        
        $data = [
            'recommend_title' => $set['recommend_title'] ?: '根据您的舌诊结果，为您推荐以下产品',
            'products' => $productList,
            'total_count' => count($productList)
        ];
        
        return ['code'=>1, 'msg'=>'获取成功', 'data'=>$data];
    }

    // 2025-01-03 22:55:53,565-INFO-[ApiSheZhen][getRecordList_001] 获取用户记录列表
    public function getRecordList(){
        $page = input('param.page/d', 1);
        $limit = input('param.limit/d', 10);
        
        // 2025-01-03 22:55:53,565-INFO-[ApiSheZhen][getRecordList_002] 查询用户记录
        $where = [
            ['aid','=', $this->aid],
            ['mid','=', $this->mid],
            ['status','=', 1]
        ];
        
        $count = Db::name('shezhen_record')->where($where)->count();
        $list = Db::name('shezhen_record')
            ->where($where)
            ->field('id,order_no,tongue_image,constitution_type,constitution_score,price,is_free,api_type,createtime')
            ->page($page, $limit)
            ->order('id desc')
            ->select()
            ->toArray();
        
        // 2025-01-03 22:55:53,565-INFO-[ApiSheZhen][getRecordList_003] 格式化数据
        foreach($list as $k => $v){
            $list[$k]['createtime_format'] = date('Y-m-d H:i:s', $v['createtime']);
            $list[$k]['price_format'] = number_format($v['price'], 2);
            $list[$k]['api_type_text'] = $v['api_type'] == 'own_aliyun' ? '自有阿里云' : '公共接口';
        }
        
        return $this->json(['code'=>1,'data'=>['list'=>$list,'count'=>$count,'page'=>$page,'limit'=>$limit]]);
    }

    // 2025-01-03 22:55:53,565-INFO-[ApiSheZhen][getReport_001] 获取详细报告
    public function getReport(){
        $recordId = input('param.record_id/d');
        if(!$recordId){
            return $this->json(['code'=>0,'msg'=>'参数错误']);
        }
        
        // 2025-01-03 22:55:53,565-INFO-[ApiSheZhen][getReport_002] 验证记录权限
        $record = Db::name('shezhen_record')
            ->where('aid', $this->aid)
            ->where('mid', $this->mid)
            ->where('id', $recordId)
            ->where('status', 1)
            ->find();
            
        if(!$record){
            return $this->json(['code'=>0,'msg'=>'记录不存在']);
        }
        
        // 2025-01-03 22:55:53,565-INFO-[ApiSheZhen][getReport_003] 获取报告详情
        $report = Db::name('shezhen_report')->where('record_id', $recordId)->find();
        if(!$report){
            return $this->json(['code'=>0,'msg'=>'报告不存在']);
        }
        
        // 处理JSON字段
        if($report['tongue_features']){
            $report['tongue_features'] = json_decode($report['tongue_features'], true);
        }
        if($report['improvement_plan']){
            $report['improvement_plan'] = json_decode($report['improvement_plan'], true);
        }
        
        $report['createtime_format'] = date('Y-m-d H:i:s', $report['createtime']);
        
        return $this->json(['code'=>1,'data'=>$report]);
    }

    // 2025-01-03 22:55:53,565-INFO-[ApiSheZhen][callOwnAliyunApi_001] 调用自有阿里云API
    private function callOwnAliyunApi($imageUrl, $config){
        try {
            // 2025-01-03 22:55:53,565-INFO-[ApiSheZhen][callOwnAliyunApi_002] 准备自有阿里云API请求参数
            $accessKeyId = $config['ali_access_key_id'];
            $accessKeySecret = $config['ali_access_key_secret'];
            $endpoint = $config['ali_endpoint'];
            
            if(empty($accessKeyId) || empty($accessKeySecret) || empty($endpoint)){
                return ['success'=>false,'message'=>'自有阿里云配置不完整'];
            }
            
            // 2025-01-03 22:55:53,565-INFO-[ApiSheZhen][callOwnAliyunApi_003] 这里应该调用真实的阿里云API
            // 现在返回模拟数据，实际项目中需要集成阿里云SDK
            $mockResult = [
                'constitution_type' => $this->getRandomConstitution(),
                'constitution_score' => rand(60, 95),
                'health_suggestions' => '建议多喝温水，注意休息，保持良好的作息习惯。',
                'diet_suggestions' => '饮食宜清淡，多食用新鲜蔬菜水果，避免辛辣刺激食物。',
                'exercise_suggestions' => '适量运动，建议每天散步30分钟，可以练习太极拳或瑜伽。',
                'tongue_color' => '淡红',
                'tongue_coating' => '薄白',
                'tongue_shape' => '正常',
                'tongue_texture' => '润泽',
                'api_source' => 'own_aliyun'
            ];
            
            return ['success'=>true,'data'=>$mockResult];
            
        } catch (\Exception $e) {
            return ['success'=>false,'message'=>'自有阿里云API调用失败：'.$e->getMessage()];
        }
    }

    // 2025-01-03 22:55:53,565-INFO-[ApiSheZhen][callPublicApi_001] 调用公共接口API
    private function callPublicApi($imageUrl, $config){
        try {
            // 2025-01-13 修复：使用新的舌诊检测接口，将image_url映射为tf_image
            $imageData = [
                'tf_image' => $imageUrl,  // 将传入的图片URL作为舌头图片
                'gender' => '男'  // 默认性别，可以后续扩展为用户输入
            ];
            
            // 2025-01-13 调用舌诊检测接口
            $result = \app\common\SheZhen::callDetectApi($imageData, $this->aid);
            
            if($result['status'] == 1){
                // 2025-01-13 检测成功，获取session_id
                $sessionId = $result['session_id'] ?? '';
                
                if(empty($sessionId)) {
                    return ['success'=>false,'message'=>'检测成功但未获取到会话ID'];
                }
                
                // 2025-01-13 调用报告生成API获取真实分析结果
                \think\facade\Log::info('调用报告生成API，session_id: ' . $sessionId);
                
                // 构建问题答案（可以根据需要扩展）
                $answers = [];
                
                // 调用报告生成接口
                $reportResult = \app\common\SheZhen::callReportApi($sessionId, $answers, $this->aid);
                
                if($reportResult['status'] == 1 && isset($reportResult['data']['data'])){
                    // 报告生成成功，直接使用API返回的原始数据
                    $reportData = $reportResult['data']['data'];
                    \think\facade\Log::info('报告生成成功，数据: ' . json_encode($reportData, JSON_UNESCAPED_UNICODE));
                    
                    // 直接返回API的原始数据，保持数据完整性
                    $formattedResult = [
                        'api_source' => 'public_api',
                        'session_id' => $sessionId,
                        'session_record_id' => $result['session_record_id'] ?? 0,
                        'report_url' => $reportResult['report_url'] ?? '',
                        // 直接保存完整的API返回数据
                        'raw_report_data' => $reportData,
                        // 为了兼容现有接口，提取一些常用字段
                        'constitution_type' => $reportData['physique_name'] ?? '未知体质',
                        'constitution_score' => $reportData['score'] ?? 0,
                        'health_suggestions' => $reportData['physique_analysis'] ?? '',
                        'diet_suggestions' => isset($reportData['advices']['food']) ? json_encode($reportData['advices']['food'], JSON_UNESCAPED_UNICODE) : '',
                        'exercise_suggestions' => isset($reportData['advices']['sport']) ? json_encode($reportData['advices']['sport'], JSON_UNESCAPED_UNICODE) : '',
                        'tongue_features' => isset($reportData['features']) ? json_encode($reportData['features'], JSON_UNESCAPED_UNICODE) : '',
                        'typical_symptoms' => $reportData['typical_symptom'] ?? '',
                        'risk_warning' => $reportData['risk_warning'] ?? '',
                        'syndrome_name' => $reportData['syndrome_name'] ?? '',
                        'syndrome_introduction' => $reportData['syndrome_introduction'] ?? '',
                        'all_advices' => isset($reportData['advices']) ? json_encode($reportData['advices'], JSON_UNESCAPED_UNICODE) : ''
                    ];
                    
                    return ['success'=>true,'data'=>$formattedResult];
                } else {
                    // 报告生成失败，记录错误但返回检测结果
                    \think\facade\Log::error('报告生成失败: ' . ($reportResult['msg'] ?? '未知错误'));
                    
                    // 返回基本检测结果和错误信息
                    $formattedResult = [
                        'constitution_type' => '检测中',
                        'constitution_score' => 0,
                        'health_suggestions' => '检测成功，报告生成中，请稍后查看详细结果。',
                        'diet_suggestions' => '建议保持健康饮食习惯。',
                        'exercise_suggestions' => '建议适量运动。',
                        'api_source' => 'public_api',
                        'session_id' => $sessionId,
                        'session_record_id' => $result['session_record_id'] ?? 0,
                        'report_error' => $reportResult['msg'] ?? '报告生成失败'
                    ];
                    
                    return ['success'=>true,'data'=>$formattedResult];
                }
                
            } else {
                // 2025-01-13 检测失败
                return ['success'=>false,'message'=>$result['msg']];
            }
            
        } catch (\Exception $e) {
            \think\facade\Log::error('调用公共API异常: ' . $e->getMessage());
            return ['success'=>false,'message'=>'API调用异常: ' . $e->getMessage()];
        }
    }

    // 2025-01-03 22:55:53,565-INFO-[ApiSheZhen][generateReport_001] 生成详细报告
    private function generateReport($recordId, $analysisData){
        // 记录报告生成开始
        \think\facade\Log::info('ApiSheZhen生成详细报告开始，recordId:' . $recordId . ', analysisData:' . json_encode($analysisData, JSON_UNESCAPED_UNICODE));
        
        // 获取基础用户信息
        $record = Db::name('shezhen_record')->where('id', $recordId)->find();
        if(!$record) return false;
        
        // 构建报告数据 - 支持完整的API数据
        $reportData = [
            'aid' => $record['aid'],
            'bid' => $record['bid'],
            'mid' => $record['mid'],
            'record_id' => $recordId,
            'report_title' => '舌诊分析报告 - ' . ($analysisData['constitution_type'] ?? '未知'),
            'api_source' => $analysisData['api_source'] ?? 'unknown',
            'status' => 1,
            'createtime' => time(),
            'updatetime' => time()
        ];
        
        // 如果有完整的API原始数据，直接保存
        if (isset($analysisData['raw_report_data'])) {
            $rawData = $analysisData['raw_report_data'];
            
            // 保存完整的原始报告数据
            $reportData['raw_report_json'] = json_encode($rawData, JSON_UNESCAPED_UNICODE);
            
            // 提取关键字段用于查询和显示
            $reportData['constitution_type'] = $rawData['physique_name'] ?? '';
            $reportData['constitution_score'] = $rawData['score'] ?? 0;
            $reportData['health_status'] = $this->getHealthStatus($rawData['score'] ?? 0);
            $reportData['constitution_analysis'] = $rawData['physique_analysis'] ?? '';
            $reportData['typical_symptoms'] = $rawData['typical_symptom'] ?? '';
            $reportData['risk_assessment'] = $rawData['risk_warning'] ?? '';
            $reportData['syndrome_name'] = $rawData['syndrome_name'] ?? '';
            $reportData['syndrome_introduction'] = $rawData['syndrome_introduction'] ?? '';
            
            // 处理舌诊特征
            if (isset($rawData['features']) && is_array($rawData['features'])) {
                $tongueFeatures = [];
                foreach ($rawData['features'] as $feature) {
                    if (isset($feature['feature_category']) && $feature['feature_category'] === '舌部') {
                        $tongueFeatures[] = [
                            'group' => $feature['feature_group'] ?? '',
                            'name' => $feature['feature_name'] ?? '',
                            'situation' => $feature['feature_situation'] ?? '',
                            'interpret' => $feature['feature_interpret'] ?? ''
                        ];
                    }
                }
                $reportData['tongue_features'] = json_encode($tongueFeatures, JSON_UNESCAPED_UNICODE);
            }
            
            // 处理改善建议
            if (isset($rawData['advices']) && is_array($rawData['advices'])) {
                $improvementPlan = [];
                
                // 饮食建议
                if (isset($rawData['advices']['food'])) {
                    $dietAdvice = [];
                    foreach ($rawData['advices']['food'] as $food) {
                        $dietAdvice[] = $food['title'] . ': ' . $food['advice'];
                    }
                    $improvementPlan['diet'] = implode(' | ', $dietAdvice);
                }
                
                // 运动建议
                if (isset($rawData['advices']['sport'])) {
                    $sportAdvice = [];
                    foreach ($rawData['advices']['sport'] as $sport) {
                        $sportAdvice[] = $sport['title'] . ': ' . $sport['advice'];
                    }
                    $improvementPlan['exercise'] = implode(' | ', $sportAdvice);
                }
                
                // 生活建议
                if (isset($rawData['advices']['sleep'])) {
                    $lifestyleAdvice = [];
                    foreach ($rawData['advices']['sleep'] as $sleep) {
                        $lifestyleAdvice[] = $sleep['title'] . ': ' . $sleep['advice'];
                    }
                    $improvementPlan['lifestyle'] = implode(' | ', $lifestyleAdvice);
                }
                
                // 治疗建议
                if (isset($rawData['advices']['treatment'])) {
                    $treatmentAdvice = [];
                    foreach ($rawData['advices']['treatment'] as $treatment) {
                        $treatmentAdvice[] = $treatment['title'] . ': ' . $treatment['advice'];
                    }
                    $improvementPlan['treatment'] = implode(' | ', $treatmentAdvice);
                }
                
                $reportData['improvement_plan'] = json_encode($improvementPlan, JSON_UNESCAPED_UNICODE);
            }
            
            // 保存所有建议的完整JSON
            if (isset($rawData['advices'])) {
                $reportData['all_advices'] = json_encode($rawData['advices'], JSON_UNESCAPED_UNICODE);
            }
            
        } else {
            // 兼容旧格式数据
            $reportData['constitution_type'] = $analysisData['constitution_type'] ?? '';
            $reportData['constitution_score'] = $analysisData['constitution_score'] ?? 0;
            $reportData['health_status'] = $this->getHealthStatus($analysisData['constitution_score'] ?? 0);
            $reportData['constitution_analysis'] = $this->getConstitutionAnalysis($analysisData['constitution_type'] ?? '');
            
            // 构建舌诊特征JSON
            if (isset($analysisData['tongue_color']) || isset($analysisData['tongue_coating'])) {
                $tongueFeatures = [
                    'color' => $analysisData['tongue_color'] ?? '',
                    'coating' => $analysisData['tongue_coating'] ?? '',
                    'shape' => $analysisData['tongue_shape'] ?? '',
                    'texture' => $analysisData['tongue_texture'] ?? ''
                ];
                $reportData['tongue_features'] = json_encode($tongueFeatures, JSON_UNESCAPED_UNICODE);
            }
            
            // 构建改善计划JSON
            $improvementPlan = [
                'diet' => $analysisData['diet_suggestions'] ?? '',
                'exercise' => $analysisData['exercise_suggestions'] ?? '',
                'lifestyle' => $analysisData['health_suggestions'] ?? ''
            ];
            $reportData['improvement_plan'] = json_encode($improvementPlan, JSON_UNESCAPED_UNICODE);
            $reportData['risk_assessment'] = $this->getRiskAssessment($analysisData['constitution_score'] ?? 0);
        }
        
        $reportData['follow_up_advice'] = '建议定期进行舌诊检查，关注体质变化，及时调整生活方式。';
        
        // 记录完整的报告数据结构
        \think\facade\Log::info('ApiSheZhen生成报告数据结构:' . json_encode($reportData, JSON_UNESCAPED_UNICODE));
        
        // 插入报告数据
        return Db::name('shezhen_report')->insert($reportData);
    }

    // 2025-01-03 22:55:53,565-INFO-[ApiSheZhen][getRandomConstitution_001] 获取随机体质类型
    private function getRandomConstitution(){
        $constitutions = ['平和质','气虚质','阳虚质','阴虚质','痰湿质','湿热质','血瘀质','气郁质','特禀质'];
        return $constitutions[array_rand($constitutions)];
    }

    // 2025-01-03 22:55:53,565-INFO-[ApiSheZhen][getHealthStatus_001] 根据得分获取健康状态
    private function getHealthStatus($score){
        if($score >= 90) return '健康状态良好';
        if($score >= 80) return '健康状态较好';
        if($score >= 70) return '健康状态一般';
        if($score >= 60) return '健康状态偏差';
        return '健康状态较差';
    }

    // 2025-01-03 22:55:53,565-INFO-[ApiSheZhen][getConstitutionAnalysis_001] 获取体质分析
    private function getConstitutionAnalysis($type){
        $analysis = [
            '平和质' => '体质平和，身体健康，精力充沛，适应能力强。',
            '气虚质' => '元气不足，容易疲劳，免疫力较低，需要补气调理。',
            '阳虚质' => '阳气不足，畏寒怕冷，手脚冰凉，需要温阳补肾。',
            '阴虚质' => '阴液亏少，容易上火，口干舌燥，需要滋阴润燥。',
            '痰湿质' => '痰湿内盛，身体沉重，容易困倦，需要化痰除湿。',
            '湿热质' => '湿热内蕴，容易长痘，口苦口臭，需要清热利湿。',
            '血瘀质' => '血液循环不畅，容易淤血，需要活血化瘀。',
            '气郁质' => '气机郁滞，情绪不稳，容易抑郁，需要疏肝理气。',
            '特禀质' => '先天禀赋不足，容易过敏，需要调理体质。'
        ];
        
        return $analysis[$type] ?? '体质特征分析中，请咨询专业医师。';
    }

    // 2025-01-03 22:55:53,565-INFO-[ApiSheZhen][getRiskAssessment_001] 获取风险评估
    private function getRiskAssessment($score){
        if($score >= 85) return '低风险：体质状况良好，继续保持健康生活方式。';
        if($score >= 70) return '中低风险：体质状况尚可，建议适当调理。';
        if($score >= 60) return '中等风险：体质偏差，建议及时调理改善。';
        return '较高风险：体质状况较差，建议咨询专业医师。';
    }

    // 2025-01-03 22:55:53,565-INFO-[ApiSheZhen][getRecommendProducts_001] 获取推荐商品API接口
    public function getRecommendProducts(){
        $constitutionType = input('param.constitution_type');
        $constitutionScore = intval(input('param.constitution_score')) ?: 0;
        
        if(!$constitutionType && !$constitutionScore){
            return $this->json(['code'=>0, 'msg'=>'请提供体质类型或得分']);
        }
        
        // 获取舌诊设置
        $set = Db::name('shezhen_set')->where('aid', $this->aid)->find();
        if(!$set || $set['is_recommend_open'] != 1){
            return $this->json(['code'=>0, 'msg'=>'推荐功能未开启']);
        }
        
        // 2025-01-06 11:45:00,040-INFO-[ApiSheZhen][getRecommendProducts_002] 获取用户会员等级
        $userLevelId = 0;
        if($this->member && isset($this->member['levelid'])){
            $userLevelId = intval($this->member['levelid']);
        }
    
        
        $where = [
            ['aid', '=', $this->aid],
            ['status', '=', 1]
        ];
        if($this->bid > 0){
            $where[] = ['bid', '=', $this->bid];
        }
        
        // 构建推荐商品查询条件
        $orWhere = [];
        
        // 2025-01-14 修复：处理多体质类型匹配和体质名称映射
        $constitutionTypes = [];
        if($constitutionType){
            // 体质类型映射表，解决不同名称对应同一体质的问题
            $constitutionMapping = [
                '火热体质' => '湿热质',
                '火热质' => '湿热质',
                '寒湿体质' => '痰湿质',
                '寒湿质' => '痰湿质',
                '血瘀体质' => '血瘀质',
                '血瘀质' => '血瘀质',
                '气虚体质' => '气虚质',
                '气虚质' => '气虚质',
                '阳虚体质' => '阳虚质',
                '阳虚质' => '阳虚质',
                '阴虚体质' => '阴虚质',
                '阴虚质' => '阴虚质',
                '痰湿体质' => '痰湿质',
                '痰湿质' => '痰湿质',
                '湿热体质' => '湿热质',
                '湿热质' => '湿热质',
                '气郁体质' => '气郁质',
                '气郁质' => '气郁质',
                '特禀体质' => '特禀质',
                '特禀质' => '特禀质',
                '平和体质' => '平和质',
                '平和质' => '平和质'
            ];
            
            // 分割多个体质类型
            $types = explode('、', $constitutionType);
            if(count($types) == 1) {
                $types = explode(',', $constitutionType);
            }
            
            foreach($types as $type){
                $type = trim($type);
                if($type){
                    // 先添加原始类型
                    $constitutionTypes[] = $type;
                    
                    // 检查映射表
                    if(isset($constitutionMapping[$type])){
                        $constitutionTypes[] = $constitutionMapping[$type];
                    }
                    
                    // 生成格式变换版本
                    if(strpos($type, '体质') !== false){
                        $constitutionTypes[] = str_replace('体质', '质', $type);
                    } elseif(strpos($type, '质') !== false && strpos($type, '体质') === false){
                        $constitutionTypes[] = str_replace('质', '体质', $type);
                    }
                }
            }
            $constitutionTypes = array_unique($constitutionTypes);
        }
        
        // 记录体质类型转换结果
        \think\facade\Log::info('ApiSheZhen体质类型转换，原始: ' . $constitutionType . '，转换后: ' . json_encode($constitutionTypes, JSON_UNESCAPED_UNICODE));
        
        // 2025-01-14 增加会员等级调试信息
        \think\facade\Log::info('ApiSheZhen用户会员等级信息，userLevelId: ' . $userLevelId . '，member信息: ' . json_encode($this->member, JSON_UNESCAPED_UNICODE));
        
        // 1. 按体质类型推荐（recommend_type = 1）
        if(!empty($constitutionTypes)){
            foreach($constitutionTypes as $type){
                $orWhere[] = [
                    ['recommend_type', '=', 1],
                    ['constitution_type', '=', $type]
                ];
                // 增加模糊匹配，以防数据库中存储的格式稍有不同
                $orWhere[] = [
                    ['recommend_type', '=', 1],
                    ['constitution_type', 'like', '%' . $type . '%']
                ];
            }
        }
        
        // 2. 按得分推荐（recommend_type = 2）
        if($constitutionScore > 0){
            $orWhere[] = [
                ['recommend_type', '=', 2],
                ['min_score', '<=', $constitutionScore],
                ['max_score', '>=', $constitutionScore]
            ];
        }
        
        // 3. 按体质+得分推荐（recommend_type = 3）
        if(!empty($constitutionTypes) && $constitutionScore > 0){
            foreach($constitutionTypes as $type){
                $orWhere[] = [
                    ['recommend_type', '=', 3],
                    ['constitution_type', '=', $type],
                    ['min_score', '<=', $constitutionScore],
                    ['max_score', '>=', $constitutionScore]
                ];
                // 增加模糊匹配
                $orWhere[] = [
                    ['recommend_type', '=', 3],
                    ['constitution_type', 'like', '%' . $type . '%'],
                    ['min_score', '<=', $constitutionScore],
                    ['max_score', '>=', $constitutionScore]
                ];
            }
        }
        
        // 4. 按会员等级推荐（recommend_type = 4）
        if($userLevelId > 0){
            $orWhere[] = [
                ['recommend_type', '=', 4],
                ['member_level_ids', '=', $userLevelId]
            ];
            $orWhere[] = [
                ['recommend_type', '=', 4],
                ['member_level_ids', 'like', $userLevelId . ',%']
            ];
            $orWhere[] = [
                ['recommend_type', '=', 4],
                ['member_level_ids', 'like', '%,' . $userLevelId . ',%']
            ];
            $orWhere[] = [
                ['recommend_type', '=', 4],
                ['member_level_ids', 'like', '%,' . $userLevelId]
            ];
        }
        
        // 5. 按体质+会员等级推荐（recommend_type = 5）
        if(!empty($constitutionTypes) && $userLevelId > 0){
            foreach($constitutionTypes as $type){
                // 精确匹配等级
                $orWhere[] = [
                    ['recommend_type', '=', 5],
                    ['constitution_type', '=', $type],
                    ['member_level_ids', '=', $userLevelId]
                ];
                // 开头匹配
                $orWhere[] = [
                    ['recommend_type', '=', 5],
                    ['constitution_type', '=', $type],
                    ['member_level_ids', 'like', $userLevelId . ',%']
                ];
                // 中间匹配
                $orWhere[] = [
                    ['recommend_type', '=', 5],
                    ['constitution_type', '=', $type],
                    ['member_level_ids', 'like', '%,' . $userLevelId . ',%']
                ];
                // 结尾匹配
                $orWhere[] = [
                    ['recommend_type', '=', 5],
                    ['constitution_type', '=', $type],
                    ['member_level_ids', 'like', '%,' . $userLevelId]
                ];
                
                // 增加模糊匹配体质类型
                $orWhere[] = [
                    ['recommend_type', '=', 5],
                    ['constitution_type', 'like', '%' . $type . '%'],
                    ['member_level_ids', '=', $userLevelId]
                ];
                $orWhere[] = [
                    ['recommend_type', '=', 5],
                    ['constitution_type', 'like', '%' . $type . '%'],
                    ['member_level_ids', 'like', $userLevelId . ',%']
                ];
                $orWhere[] = [
                    ['recommend_type', '=', 5],
                    ['constitution_type', 'like', '%' . $type . '%'],
                    ['member_level_ids', 'like', '%,' . $userLevelId . ',%']
                ];
                $orWhere[] = [
                    ['recommend_type', '=', 5],
                    ['constitution_type', 'like', '%' . $type . '%'],
                    ['member_level_ids', 'like', '%,' . $userLevelId]
                ];
            }
        }
        
        // 6. 按得分+会员等级推荐（recommend_type = 6）
        if($constitutionScore > 0 && $userLevelId > 0){
            $orWhere[] = [
                ['recommend_type', '=', 6],
                ['min_score', '<=', $constitutionScore],
                ['max_score', '>=', $constitutionScore],
                ['member_level_ids', '=', $userLevelId]
            ];
            $orWhere[] = [
                ['recommend_type', '=', 6],
                ['min_score', '<=', $constitutionScore],
                ['max_score', '>=', $constitutionScore],
                ['member_level_ids', 'like', $userLevelId . ',%']
            ];
            $orWhere[] = [
                ['recommend_type', '=', 6],
                ['min_score', '<=', $constitutionScore],
                ['max_score', '>=', $constitutionScore],
                ['member_level_ids', 'like', '%,' . $userLevelId . ',%']
            ];
            $orWhere[] = [
                ['recommend_type', '=', 6],
                ['min_score', '<=', $constitutionScore],
                ['max_score', '>=', $constitutionScore],
                ['member_level_ids', 'like', '%,' . $userLevelId]
            ];
        }
        
        // 7. 按体质+得分+会员等级推荐（recommend_type = 7）
        if(!empty($constitutionTypes) && $constitutionScore > 0 && $userLevelId > 0){
            foreach($constitutionTypes as $type){
                $orWhere[] = [
                    ['recommend_type', '=', 7],
                    ['constitution_type', '=', $type],
                    ['min_score', '<=', $constitutionScore],
                    ['max_score', '>=', $constitutionScore],
                    ['member_level_ids', '=', $userLevelId]
                ];
                $orWhere[] = [
                    ['recommend_type', '=', 7],
                    ['constitution_type', '=', $type],
                    ['min_score', '<=', $constitutionScore],
                    ['max_score', '>=', $constitutionScore],
                    ['member_level_ids', 'like', $userLevelId . ',%']
                ];
                $orWhere[] = [
                    ['recommend_type', '=', 7],
                    ['constitution_type', '=', $type],
                    ['min_score', '<=', $constitutionScore],
                    ['max_score', '>=', $constitutionScore],
                    ['member_level_ids', 'like', '%,' . $userLevelId . ',%']
                ];
                $orWhere[] = [
                    ['recommend_type', '=', 7],
                    ['constitution_type', '=', $type],
                    ['min_score', '<=', $constitutionScore],
                    ['max_score', '>=', $constitutionScore],
                    ['member_level_ids', 'like', '%,' . $userLevelId]
                ];
                
                // 增加模糊匹配
                $orWhere[] = [
                    ['recommend_type', '=', 7],
                    ['constitution_type', 'like', '%' . $type . '%'],
                    ['min_score', '<=', $constitutionScore],
                    ['max_score', '>=', $constitutionScore],
                    ['member_level_ids', '=', $userLevelId]
                ];
                $orWhere[] = [
                    ['recommend_type', '=', 7],
                    ['constitution_type', 'like', '%' . $type . '%'],
                    ['min_score', '<=', $constitutionScore],
                    ['max_score', '>=', $constitutionScore],
                    ['member_level_ids', 'like', $userLevelId . ',%']
                ];
                $orWhere[] = [
                    ['recommend_type', '=', 7],
                    ['constitution_type', 'like', '%' . $type . '%'],
                    ['min_score', '<=', $constitutionScore],
                    ['max_score', '>=', $constitutionScore],
                    ['member_level_ids', 'like', '%,' . $userLevelId . ',%']
                ];
                $orWhere[] = [
                    ['recommend_type', '=', 7],
                    ['constitution_type', 'like', '%' . $type . '%'],
                    ['min_score', '<=', $constitutionScore],
                    ['max_score', '>=', $constitutionScore],
                    ['member_level_ids', 'like', '%,' . $userLevelId]
                ];
            }
        }
        
        if(empty($orWhere)){
            return $this->json(['code'=>0, 'msg'=>'无符合条件的推荐商品']);
        }
        
        $recommendLimit = isset($set['recommend_count']) ? intval($set['recommend_count']) : 3;
        
        // 使用whereOr查询多个条件
        $query = Db::name('shezhen_recommend_product')->where($where);
        
        // 添加OR条件组
        $query->where(function($query) use ($orWhere) {
            foreach($orWhere as $index => $conditions) {
                if($index == 0) {
                    $query->where($conditions);
                } else {
                    $query->whereOr($conditions);
                }
            }
        });
        
        // 2025-01-14 增加调试日志，记录匹配的体质类型和查询条件
        \think\facade\Log::info('ApiSheZhen推荐商品查询，原始体质类型: ' . $constitutionType . '，解析后体质类型: ' . json_encode($constitutionTypes, JSON_UNESCAPED_UNICODE) . '，查询条件数量: ' . count($orWhere));
        
        // 2025-01-14 临时调试：查看数据库中血瘀质和湿热质的推荐配置
        $debugQuery = Db::name('shezhen_recommend_product')
            ->where('aid', $this->aid)
            ->where('status', 1)
            ->where(function($query) {
                $query->where('constitution_type', 'like', '%血瘀%')
                      ->whereOr('constitution_type', 'like', '%湿热%')
                      ->whereOr('constitution_type', 'like', '%火热%');
            })
            ->select()
            ->toArray();
        \think\facade\Log::info('ApiSheZhen调试查询血瘀质湿热质推荐配置: ' . json_encode($debugQuery, JSON_UNESCAPED_UNICODE));
        
        // 2025-01-14 临时调试：先尝试简单的精确匹配查询
        $simpleQuery = Db::name('shezhen_recommend_product')
            ->where('aid', $this->aid)
            ->where('status', 1)
            ->where('recommend_type', 5)
            ->where('member_level_ids', $userLevelId)
            ->where(function($query) use ($constitutionTypes) {
                foreach($constitutionTypes as $index => $type) {
                    if($index == 0) {
                        $query->where('constitution_type', $type);
                    } else {
                        $query->whereOr('constitution_type', $type);
                    }
                }
            })
            ->select()
            ->toArray();
        \think\facade\Log::info('ApiSheZhen简单精确匹配查询结果: ' . json_encode($simpleQuery, JSON_UNESCAPED_UNICODE));
        
        // 2025-01-14 临时使用简化查询，避免复杂OR条件干扰
        if(!empty($simpleQuery)) {
            $list = $simpleQuery;
            \think\facade\Log::info('ApiSheZhen使用简化查询结果，数量: ' . count($list));
        } else {
            $list = $query->order('sort desc, id desc')
                ->limit($recommendLimit)
                ->select()
                ->toArray();
            \think\facade\Log::info('ApiSheZhen使用复杂查询结果，数量: ' . count($list));
        }
        
        // 2025-01-14 记录查询结果数量
        \think\facade\Log::info('ApiSheZhen推荐商品查询结果数量: ' . count($list));
        
        // 2025-01-14 记录查询到的推荐配置详情
        \think\facade\Log::info('ApiSheZhen查询到的推荐配置详情: ' . json_encode($list, JSON_UNESCAPED_UNICODE));
        
        // 获取商品详情
        $productList = [];
        foreach($list as $item){
            if($item['product_type'] == 1){ // 商品
                $product = Db::name('shop_product')
                    ->where('id', $item['product_id'])
                    ->where('status', 1)
                    ->field('id,name,sell_price as price,pic,sell_price as original_price,stock,detail')
                    ->find();
                
                if($product){
                    $product['type'] = 'product';
                    $product['recommend_reason'] = $this->getRecommendReason($item, $constitutionType, $constitutionScore, $userLevelId);
                    $product['recommend_type'] = $item['recommend_type'];
                    $product['sort'] = $item['sort'];
                    $productList[] = $product;
                }
            }else{ // 课程
                $course = Db::name('kecheng_list')
                    ->where('id', $item['product_id'])
                    ->where('status', 1)
                    ->field('id,name,price,pic,market_price,detail')
                    ->find();
                
                if($course){
                    $course['type'] = 'course';
                    $course['original_price'] = $course['market_price'] ?: $course['price'];
                    $course['recommend_reason'] = $this->getRecommendReason($item, $constitutionType, $constitutionScore, $userLevelId);
                    $course['recommend_type'] = $item['recommend_type'];
                    $course['sort'] = $item['sort'];
                    $productList[] = $course;
                }
            }
        }
        
        $data = [
            'recommend_title' => $set['recommend_title'] ?: '根据您的舌诊结果，为您推荐以下产品',
            'products' => $productList,
            'total_count' => count($productList)
        ];
        
        return $this->json(['code'=>1, 'msg'=>'获取成功', 'data'=>$data]);
    }
    
    // 2025-01-03 22:55:53,565-INFO-[ApiSheZhen][getRecommendReason_001] 获取推荐理由
    private function getRecommendReason($item, $constitutionType, $constitutionScore, $userLevelId = 0){
        // 2025-01-06 11:45:00,041-INFO-[ApiSheZhen][getRecommendReason_002] 获取会员等级名称
        $levelName = '';
        if($userLevelId > 0){
            $level = Db::name('member_level')->where('id', $userLevelId)->field('name')->find();
            $levelName = $level ? $level['name'] : '';
        }
        
        switch($item['recommend_type']){
            case 1:
                return '适合' . $constitutionType . '体质';
            case 2:
                return '适合' . $item['min_score'] . '-' . $item['max_score'] . '分人群';
            case 3:
                return '适合' . $item['constitution_type'] . '体质（' . $item['min_score'] . '-' . $item['max_score'] . '分）';
            case 4:
                return $levelName ? $levelName . '会员专享' : '会员专享';
            case 5:
                return ($levelName ? $levelName . '会员' : '会员') . '·' . $item['constitution_type'] . '体质专享';
            case 6:
                return ($levelName ? $levelName . '会员' : '会员') . '·适合' . $item['min_score'] . '-' . $item['max_score'] . '分人群';
            case 7:
                return ($levelName ? $levelName . '会员' : '会员') . '·' . $item['constitution_type'] . '体质（' . $item['min_score'] . '-' . $item['max_score'] . '分）';
            default:
                return '为您推荐';
        }
    }

    // 2025-01-26 12:00:00,001-INFO-[ApiSheZhen][getPayInfo_001] 获取舌诊支付信息
    public function getPayInfo(){
        $imageUrl = input('param.image_url');
        $useFree = input('param.use_free/d', 0);
        
        if(!$imageUrl){
            return $this->json(['code'=>0,'msg'=>'请提供舌头图片']);
        }
        
        // 2025-01-26 12:00:00,002-INFO-[ApiSheZhen][getPayInfo_002] 获取配置信息
        $config = Db::name('shezhen_set')->where('aid', $this->aid)->find();
        if(!$config || $config['is_open'] != 1){
            return $this->json(['code'=>0,'msg'=>'舌诊功能未开启']);
        }
        
        // 2025-01-26 12:00:00,003-INFO-[ApiSheZhen][getPayInfo_003] 检查免费权限
        $price = $config['single_price'];
        $isFree = false;
        
        if($useFree){
            $userLevel = $this->member['levelid'] ?? 0;
            $freeLevel = explode(',', $config['free_level']);
            
            if(in_array($userLevel, $freeLevel)){
                $todayStart = strtotime(date('Y-m-d'));
                $todayCount = Db::name('shezhen_record')
                    ->where('aid', $this->aid)
                    ->where('mid', $this->mid)
                    ->where('is_free', 1)
                    ->where('createtime', '>=', $todayStart)
                    ->count();
                
                if($todayCount < $config['daily_free_count']){
                    $isFree = true;
                    $price = 0;
                }
            }
        }
        
        // 2025-01-26 12:00:00,004-INFO-[ApiSheZhen][getPayInfo_004] 获取用户信息
        $adminset = Db::name('admin_set')->where('aid', $this->aid)->find();
        $userlevel = Db::name('member_level')->where('aid', $this->aid)->where('id', $this->member['levelid'])->find();
        
        $userinfo = [];
        $userinfo['discount'] = $userlevel['discount'] ?? 10;
        $userinfo['score'] = $this->member['score'];
        $userinfo['score2money'] = $adminset['score2money'] ?? 0;
        $userinfo['dkmoney'] = round($userinfo['score'] * $userinfo['score2money'], 2);
        $userinfo['scoredkmaxpercent'] = $adminset['scoredkmaxpercent'] ?? 0;
        $userinfo['money'] = $this->member['money'];
        
        // 2025-01-26 12:00:00,005-INFO-[ApiSheZhen][getPayInfo_005] 获取可用优惠券
        $couponList = Db::name('coupon_record')
            ->where('aid', $this->aid)
            ->where('mid', $this->mid)
            ->where('type', 1)
            ->where('status', 0)
            ->where('starttime', '<=', time())
            ->where('endtime', '>', time())
            ->order('id desc')
            ->select()
            ->toArray();
        
        if(!$couponList) $couponList = [];
        
        // 2025-01-26 12:00:00,006-INFO-[ApiSheZhen][getPayInfo_006] 过滤适用的优惠券
        $newcouponlist = [];
        foreach($couponList as $k => $v){
            $couponinfo = Db::name('coupon')->where('aid', $this->aid)->where('id', $v['couponid'])->find();
            if($couponinfo['fwtype'] !== 0){ // 仅全部可用
                continue;
            }
            if($couponinfo['isgive'] == 2){ // 仅赠送
                continue;
            }
            if($couponinfo['minprice'] > $price){ // 不满足最低消费
                continue;
            }
            $newcouponlist[] = $v;
        }
        $couponList = $newcouponlist;
        
        $rdata = [
            'price' => $price,
            'is_free' => $isFree,
            'userinfo' => $userinfo,
            'couponList' => $couponList
        ];
        
        return $this->json(['code'=>1,'data'=>$rdata]);
    }

    // 2025-01-26 12:00:00,007-INFO-[ApiSheZhen][createPayOrder_001] 创建舌诊支付订单
    public function createPayOrder(){
        if(!request()->isPost()){
            return $this->json(['code'=>0,'msg'=>'请求方式错误']);
        }
        
        $post = input('post.');
        $imageUrl = $post['image_url'] ?? '';
        $useFree = intval($post['use_free'] ?? 0);
        $couponrid = intval($post['couponrid'] ?? 0);
        $usescore = intval($post['usescore'] ?? 0);
        
        if(!$imageUrl){
            return $this->json(['code'=>0,'msg'=>'请提供舌头图片']);
        }
        
        // 2025-01-26 12:00:00,008-INFO-[ApiSheZhen][createPayOrder_002] 获取配置信息
        $config = Db::name('shezhen_set')->where('aid', $this->aid)->find();
        if(!$config || $config['is_open'] != 1){
            return $this->json(['code'=>0,'msg'=>'舌诊功能未开启']);
        }
        
        // 2025-01-26 12:00:00,009-INFO-[ApiSheZhen][createPayOrder_003] 计算价格
        $money = $config['single_price'];
        $paymoney = $money;
        
        // 2025-01-26 12:00:00,010-INFO-[ApiSheZhen][createPayOrder_004] 会员折扣
        $disprice = 0;
        $userlevel = Db::name('member_level')->where('aid', $this->aid)->where('id', $this->member['levelid'])->find();
        if($userlevel && $userlevel['discount'] > 0 && $userlevel['discount'] < 10){
            $disprice = $paymoney * (1 - $userlevel['discount'] * 0.1);
        }
        $paymoney = $paymoney - $disprice;
        
        // 2025-01-26 12:00:00,011-INFO-[ApiSheZhen][createPayOrder_005] 优惠券处理
        $couponmoney = 0;
        if($couponrid > 0){
            $couponrecord = Db::name('coupon_record')->where('aid', $this->aid)->where('mid', $this->mid)->where('id', $couponrid)->find();
            if(!$couponrecord){
                return $this->json(['code'=>0,'msg'=>'该'.t('优惠券').'不存在']);
            }elseif($couponrecord['status'] != 0){
                return $this->json(['code'=>0,'msg'=>'该'.t('优惠券').'已使用过了']);
            }elseif($couponrecord['starttime'] > time()){
                return $this->json(['code'=>0,'msg'=>'该'.t('优惠券').'尚未开始使用']);    
            }elseif($couponrecord['endtime'] < time()){
                return $this->json(['code'=>0,'msg'=>'该'.t('优惠券').'已过期']);    
            }elseif($couponrecord['minprice'] > $money){
                return $this->json(['code'=>0,'msg'=>'该'.t('优惠券').'不符合条件']);
            }elseif($couponrecord['type'] != 1){
                return $this->json(['code'=>0,'msg'=>'该'.t('优惠券').'不符合条件']);    
            }
            $couponmoney = $couponrecord['money'];
            if($couponmoney > $money) $couponmoney = $money;
        }
        $paymoney = $paymoney - $couponmoney;
        if($paymoney < 0) $paymoney = 0;
        
        // 2025-01-26 12:00:00,012-INFO-[ApiSheZhen][createPayOrder_006] 积分抵扣
        $scoredk = 0;
        $decscore = 0;
        if($usescore == 1){
            $adminset = Db::name('admin_set')->where('aid', $this->aid)->find();
            $score2money = $adminset['score2money'] ?? 0;
            $scoredkmaxpercent = $adminset['scoredkmaxpercent'] ?? 0;
            $scoredk = $this->member['score'] * $score2money;
            if($scoredk > $paymoney) $scoredk = $paymoney;
            if($scoredkmaxpercent >= 0 && $scoredkmaxpercent < 100 && $scoredk > 0 && $scoredk > $paymoney * $scoredkmaxpercent * 0.01){
                $scoredk = $paymoney * $scoredkmaxpercent * 0.01;
            }
            $paymoney = $paymoney - $scoredk;
            $paymoney = round($paymoney * 100) / 100;
            if($paymoney < 0) $paymoney = 0;
            if($scoredk > 0){
                $decscore = $scoredk / $score2money;
            }
        }
        
        // 2025-01-26 12:00:00,013-INFO-[ApiSheZhen][createPayOrder_007] 检查免费权限
        $isFree = false;
        if($useFree){
            $userLevel = $this->member['levelid'] ?? 0;
            $freeLevel = explode(',', $config['free_level']);
            
            if(in_array($userLevel, $freeLevel)){
                $todayStart = strtotime(date('Y-m-d'));
                $todayCount = Db::name('shezhen_record')
                    ->where('aid', $this->aid)
                    ->where('mid', $this->mid)
                    ->where('is_free', 1)
                    ->where('createtime', '>=', $todayStart)
                    ->count();
                
                if($todayCount < $config['daily_free_count']){
                    $isFree = true;
                    $paymoney = 0;
                }
            }
        }
        
        // 2025-01-26 12:00:00,014-INFO-[ApiSheZhen][createPayOrder_008] 创建订单
        $ordernum = 'SZ' . date('ymdHis') . $this->aid . rand(1000, 9999);
        
        $order = [];
        $order['order_no'] = $ordernum;  // 订单号
        $order['aid'] = $this->aid;
        $order['bid'] = $this->bid;
        $order['mid'] = $this->mid;
        $order['record_id'] = 0;  // 舌诊记录ID，订单创建时为0，支付成功后更新
        $order['pay_type'] = '';  // 支付方式，支付时更新
        $order['pay_amount'] = $paymoney;  // 支付金额
        $order['original_price'] = $money;  // 原价
        $order['discount_amount'] = $disprice + $couponmoney + $scoredk;  // 优惠金额总计
        $order['pay_status'] = 0;  // 支付状态：0未支付
        $order['pay_time'] = 0;  // 支付时间，支付成功后更新
        $order['refund_status'] = 0;  // 退款状态：0未退款
        $order['refund_time'] = 0;  // 退款时间
        $order['refund_reason'] = '';  // 退款原因
        $order['payorderid'] = 0;  // 支付订单ID，支付时更新
        $order['status'] = 1;  // 状态：1正常
        $order['createtime'] = time();
        $order['updatetime'] = time();
        
        \think\facade\Log::write('舌诊支付订单创建开始: ordernum=' . $ordernum . ', money=' . $money . ', paymoney=' . $paymoney);
        
        Db::startTrans();
        
        // 2025-01-27 修复语法错误：使用if语句替代try-catch
        $orderid = Db::name('shezhen_order')->insertGetId($order);
        
        if($orderid){
            // 2025-01-26 12:00:00,015-INFO-[ApiSheZhen][createPayOrder_009] 创建支付订单
            $payorderid = \app\model\Payorder::createorder(
                $this->aid,
                $this->bid,
                $this->mid,
                'shezhen',
                $orderid,
                $ordernum,
                'AI智能舌诊分析服务',  // 直接使用固定标题
                $order['pay_amount'],  // 使用正确的字段名
                $decscore,  // 使用原变量名
                'shezhen-' . $orderid
            );
            
            if($payorderid){
                Db::commit();
                \think\facade\Log::write('舌诊支付订单创建成功: orderid=' . $orderid . ', payorderid=' . $payorderid);
                return $this->json(['code'=>1,'data'=>['payorderid'=>$payorderid, 'orderid'=>$orderid]]);
            } else {
                Db::rollback();
                \think\facade\Log::write('舌诊支付订单创建失败: 支付订单创建失败');
                return $this->json(['code'=>0,'msg'=>'支付订单创建失败']);
            }
        } else {
            Db::rollback();
            \think\facade\Log::write('舌诊支付订单创建失败: 订单数据插入失败');
            return $this->json(['code'=>0,'msg'=>'订单创建失败']);
        }
    }

    // 2025-01-26 12:00:00,016-INFO-[ApiSheZhen][getOrderDetail_001] 获取舌诊订单详情
    public function getOrderDetail(){
        $id = input('param.id/d');
        
        if(!$id){
            return $this->json(['code'=>0,'msg'=>'参数错误']);
        }
        
        // 2025-01-26 12:00:00,017-INFO-[ApiSheZhen][getOrderDetail_002] 查询订单详情
        $detail = Db::name('shezhen_order')
            ->where('aid', $this->aid)
            ->where('mid', $this->mid)
            ->where('id', $id)
            ->find();
            
        if(!$detail){
            return $this->json(['code'=>0,'msg'=>'订单不存在']);
        }
        
        // 格式化支付时间
        if($detail['pay_time']){
            $detail['paytime'] = date('Y-m-d H:i:s', $detail['pay_time']);
        }
        
        // 2025-01-26 12:00:00,018-INFO-[ApiSheZhen][getOrderDetail_003] 获取优惠券信息
        $couponrecord = false;
        if($detail['couponrid']){
            $couponrecord = Db::name('coupon_record')
                ->where('aid', $this->aid)
                ->where('mid', $this->mid)
                ->where('id', $detail['couponrid'])
                ->find();
        }
        
        // 2025-01-26 12:00:00,019-INFO-[ApiSheZhen][getOrderDetail_004] 查找关联的分析记录
        $record = Db::name('shezhen_record')
            ->where('aid', $this->aid)
            ->where('mid', $this->mid)
            ->where('order_no', $detail['order_no'])
            ->find();
        
        if($record){
            $detail['record_id'] = $record['id'];
        }
        
        $rdata = [
            'detail' => $detail,
            'couponrecord' => $couponrecord
        ];
        
        return $this->json(['code'=>1,'data'=>$rdata]);
    }

    // 2025-01-26 12:00:00,020-INFO-[ApiSheZhen][getOrderList_001] 获取舌诊订单列表
    public function getOrderList(){
        $pagenum = input('param.pagenum/d', 1);
        $pernum = 20;
        
        $where = [];
        $where[] = ['aid', '=', $this->aid];
        $where[] = ['mid', '=', $this->mid];
        
        if(input('param.keyword')){
            $where[] = ['order_no', 'like', '%'.input('param.keyword').'%'];
        }
        
        $datalist = Db::name('shezhen_order')
            ->field('*,from_unixtime(pay_time) as paytime')
            ->where($where)
            ->page($pagenum, $pernum)
            ->order('id desc')
            ->select()
            ->toArray();
            
        if(!$datalist) $datalist = [];
        
        if(request()->isPost()){
            return $this->json(['code'=>1,'data'=>$datalist]);
        }
        
        $count = Db::name('shezhen_order')->where($where)->count();
        
        $rdata = [
            'count' => $count,
            'datalist' => $datalist,
            'pernum' => $pernum
        ];
        
        return $this->json(['code'=>1,'data'=>$rdata]);
    }

    /**
     * 调用自有阿里云API（多图片支持）
     * 2025-01-17 新增方法 - 支持面诊和综合诊疗
     * @param array $imageData 图片数据
     * @param array $config 配置信息
     * @param int $diagnosisType 诊疗类型
     * @return array 分析结果
     */
    private function callOwnAliyunApiMultiImage($imageData, $config, $diagnosisType)
    {
        try {
            // 2025-01-17 使用公共类的API调用方法
            $result = \app\common\SheZhen::callDetectApi($imageData, $this->aid, $diagnosisType);

            if ($result['status'] == 1) {
                return [
                    'success' => true,
                    'data' => $result['data'],
                    'message' => '分析成功'
                ];
            } else {
                return [
                    'success' => false,
                    'message' => $result['msg'] ?? '分析失败'
                ];
            }
        } catch (\Exception $e) {
            \think\facade\Log::error('2025-01-17 ERROR-[ApiSheZhen][callOwnAliyunApiMultiImage] ' . $e->getMessage());
            return [
                'success' => false,
                'message' => '分析服务异常，请稍后重试'
            ];
        }
    }

    /**
     * 调用公共接口API（多图片支持）
     * 2025-01-17 新增方法 - 支持面诊和综合诊疗
     * @param array $imageData 图片数据
     * @param array $config 配置信息
     * @param int $diagnosisType 诊疗类型
     * @return array 分析结果
     */
    private function callPublicApiMultiImage($imageData, $config, $diagnosisType)
    {
        try {
            // 2025-01-17 使用公共类的API调用方法
            $result = \app\common\SheZhen::callDetectApi($imageData, $this->aid, $diagnosisType);

            if ($result['status'] == 1) {
                return [
                    'success' => true,
                    'data' => $result['data'],
                    'message' => '分析成功'
                ];
            } else {
                return [
                    'success' => false,
                    'message' => $result['msg'] ?? '分析失败'
                ];
            }
        } catch (\Exception $e) {
            \think\facade\Log::error('2025-01-17 ERROR-[ApiSheZhen][callPublicApiMultiImage] ' . $e->getMessage());
            return [
                'success' => false,
                'message' => '分析服务异常，请稍后重试'
            ];
        }
    }

    /**
     * 获取完整图片URL
     * 2025-01-17 新增方法 - 处理图片URL
     * @param string $imageUrl 图片URL
     * @return string 完整URL
     */
    private function getFullImageUrl($imageUrl)
    {
        if (empty($imageUrl)) {
            return '';
        }

        // 如果已经是完整URL，直接返回
        if (strpos($imageUrl, 'http') === 0) {
            return $imageUrl;
        }

        // 拼接域名
        $domain = request()->domain();
        return $domain . $imageUrl;
    }
}