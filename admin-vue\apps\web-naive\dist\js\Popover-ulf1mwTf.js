import{a as ie,o as se,c0 as te,c1 as oe,c2 as ar,c3 as ir,c4 as sr,c5 as be,c6 as L,c7 as Xe,c8 as Se,bB as lr,c9 as qe,ca as U,cb as ge,cc as Te,cd as ur,ce as Pe,cf as Ee,cg as Y,ch as dr,ci as xe,cj as fr,ck as cr,cl as vr,cm as hr,cn as gr,co as pr,cp as wr,q as D,g as B,i as le,j as ue,k as ee,cq as mr,aN as yr,l as br,m as $e,bb as Re,bD as Sr,n as $r,bc as Me,cr as Ar,t as Be,cs as Cr,r as de,b8 as Or,aB as _r,aA as Tr,az as Pr,ct as Er,aG as xr,bS as Rr,aH as Mr,Z as Ie,y as Z}from"./bootstrap-B_sue86n.js";import{d as Br,u as re,B as Ir,V as Dr}from"./Follower-C2co6Kvh.js";import{c as K,d as Ze,h as S,g as F,V as Je,b as zr,f as Fr,w as Lr,E as Q,O as Nr,a4 as Ye,F as Hr,P as V,ap as Wr,aq as Gr}from"../jse/index-index-UaL0SrHU.js";import{u as Kr}from"./use-merged-state-lZNesr9e.js";function Ur(e,r){return K(()=>{for(const n of r)if(e[n]!==void 0)return e[n];return e[r[r.length-1]]})}const G="@@mmoContext",kr={mounted(e,{value:r}){e[G]={handler:void 0},typeof r=="function"&&(e[G].handler=r,se("mousemoveoutside",e,r))},updated(e,{value:r}){const n=e[G];typeof r=="function"?n.handler?n.handler!==r&&(ie("mousemoveoutside",e,n.handler),n.handler=r,se("mousemoveoutside",e,r)):(e[G].handler=r,se("mousemoveoutside",e,r)):n.handler&&(ie("mousemoveoutside",e,n.handler),n.handler=void 0)},unmounted(e){const{handler:r}=e[G];r&&ie("mousemoveoutside",e,r),e[G].handler=void 0}},jr=/^(\d|\.)+$/,De=/(\d|\.)+/;function fe(e,{c:r=1,offset:n=0,attachPx:t=!0}={}){if(typeof e=="number"){const a=(e+n)*r;return a===0?"0":`${a}px`}else if(typeof e=="string")if(jr.test(e)){const a=(Number(e)+n)*r;return t?a===0?"0":`${a}px`:`${a}`}else{const a=De.exec(e);return a?e.replace(De,String((Number(a[0])+n)*r)):e}return e}let ce;function Xr(){return ce===void 0&&(ce=navigator.userAgent.includes("Node.js")||navigator.userAgent.includes("jsdom")),ce}var pe=te(oe,"WeakMap"),qr=ar(Object.keys,Object),Zr=Object.prototype,Jr=Zr.hasOwnProperty;function Yr(e){if(!ir(e))return qr(e);var r=[];for(var n in Object(e))Jr.call(e,n)&&n!="constructor"&&r.push(n);return r}function Ae(e){return be(e)?sr(e):Yr(e)}var Qr=/\.|\[(?:[^[\]]*|(["'])(?:(?!\1)[^\\]|\\.)*?\1)\]/,Vr=/^\w*$/;function Ce(e,r){if(L(e))return!1;var n=typeof e;return n=="number"||n=="symbol"||n=="boolean"||e==null||Xe(e)?!0:Vr.test(e)||!Qr.test(e)||r!=null&&e in Object(r)}var en="Expected a function";function Oe(e,r){if(typeof e!="function"||r!=null&&typeof r!="function")throw new TypeError(en);var n=function(){var t=arguments,a=r?r.apply(this,t):t[0],o=n.cache;if(o.has(a))return o.get(a);var i=e.apply(this,t);return n.cache=o.set(a,i)||o,i};return n.cache=new(Oe.Cache||Se),n}Oe.Cache=Se;var rn=500;function nn(e){var r=Oe(e,function(t){return n.size===rn&&n.clear(),t}),n=r.cache;return r}var tn=/[^.[\]]+|\[(?:(-?\d+(?:\.\d+)?)|(["'])((?:(?!\2)[^\\]|\\.)*?)\2)\]|(?=(?:\.|\[\])(?:\.|\[\]|$))/g,on=/\\(\\)?/g,an=nn(function(e){var r=[];return e.charCodeAt(0)===46&&r.push(""),e.replace(tn,function(n,t,a,o){r.push(a?o.replace(on,"$1"):t||n)}),r});function Qe(e,r){return L(e)?e:Ce(e,r)?[e]:an(lr(e))}function ae(e){if(typeof e=="string"||Xe(e))return e;var r=e+"";return r=="0"&&1/e==-1/0?"-0":r}function Ve(e,r){r=Qe(r,e);for(var n=0,t=r.length;e!=null&&n<t;)e=e[ae(r[n++])];return n&&n==t?e:void 0}function sn(e,r,n){var t=e==null?void 0:Ve(e,r);return t===void 0?n:t}function ln(e,r){for(var n=-1,t=r.length,a=e.length;++n<t;)e[a+n]=r[n];return e}function un(e,r){for(var n=-1,t=e==null?0:e.length,a=0,o=[];++n<t;){var i=e[n];r(i,n,e)&&(o[a++]=i)}return o}function dn(){return[]}var fn=Object.prototype,cn=fn.propertyIsEnumerable,ze=Object.getOwnPropertySymbols,vn=ze?function(e){return e==null?[]:(e=Object(e),un(ze(e),function(r){return cn.call(e,r)}))}:dn;function hn(e,r,n){var t=r(e);return L(e)?t:ln(t,n(e))}function Fe(e){return hn(e,Ae,vn)}var we=te(oe,"DataView"),me=te(oe,"Promise"),ye=te(oe,"Set"),Le="[object Map]",gn="[object Object]",Ne="[object Promise]",He="[object Set]",We="[object WeakMap]",Ge="[object DataView]",pn=U(we),wn=U(ge),mn=U(me),yn=U(ye),bn=U(pe),z=qe;(we&&z(new we(new ArrayBuffer(1)))!=Ge||ge&&z(new ge)!=Le||me&&z(me.resolve())!=Ne||ye&&z(new ye)!=He||pe&&z(new pe)!=We)&&(z=function(e){var r=qe(e),n=r==gn?e.constructor:void 0,t=n?U(n):"";if(t)switch(t){case pn:return Ge;case wn:return Le;case mn:return Ne;case yn:return He;case bn:return We}return r});var Sn="__lodash_hash_undefined__";function $n(e){return this.__data__.set(e,Sn),this}function An(e){return this.__data__.has(e)}function ne(e){var r=-1,n=e==null?0:e.length;for(this.__data__=new Se;++r<n;)this.add(e[r])}ne.prototype.add=ne.prototype.push=$n;ne.prototype.has=An;function Cn(e,r){for(var n=-1,t=e==null?0:e.length;++n<t;)if(r(e[n],n,e))return!0;return!1}function On(e,r){return e.has(r)}var _n=1,Tn=2;function er(e,r,n,t,a,o){var i=n&_n,s=e.length,u=r.length;if(s!=u&&!(i&&u>s))return!1;var d=o.get(e),c=o.get(r);if(d&&c)return d==r&&c==e;var p=-1,g=!0,m=n&Tn?new ne:void 0;for(o.set(e,r),o.set(r,e);++p<s;){var y=e[p],f=r[p];if(t)var T=i?t(f,y,p,r,e,o):t(y,f,p,e,r,o);if(T!==void 0){if(T)continue;g=!1;break}if(m){if(!Cn(r,function(A,C){if(!On(m,C)&&(y===A||a(y,A,n,t,o)))return m.push(C)})){g=!1;break}}else if(!(y===f||a(y,f,n,t,o))){g=!1;break}}return o.delete(e),o.delete(r),g}function Pn(e){var r=-1,n=Array(e.size);return e.forEach(function(t,a){n[++r]=[a,t]}),n}function En(e){var r=-1,n=Array(e.size);return e.forEach(function(t){n[++r]=t}),n}var xn=1,Rn=2,Mn="[object Boolean]",Bn="[object Date]",In="[object Error]",Dn="[object Map]",zn="[object Number]",Fn="[object RegExp]",Ln="[object Set]",Nn="[object String]",Hn="[object Symbol]",Wn="[object ArrayBuffer]",Gn="[object DataView]",Ke=Te?Te.prototype:void 0,ve=Ke?Ke.valueOf:void 0;function Kn(e,r,n,t,a,o,i){switch(n){case Gn:if(e.byteLength!=r.byteLength||e.byteOffset!=r.byteOffset)return!1;e=e.buffer,r=r.buffer;case Wn:return!(e.byteLength!=r.byteLength||!o(new Pe(e),new Pe(r)));case Mn:case Bn:case zn:return ur(+e,+r);case In:return e.name==r.name&&e.message==r.message;case Fn:case Nn:return e==r+"";case Dn:var s=Pn;case Ln:var u=t&xn;if(s||(s=En),e.size!=r.size&&!u)return!1;var d=i.get(e);if(d)return d==r;t|=Rn,i.set(e,r);var c=er(s(e),s(r),t,a,o,i);return i.delete(e),c;case Hn:if(ve)return ve.call(e)==ve.call(r)}return!1}var Un=1,kn=Object.prototype,jn=kn.hasOwnProperty;function Xn(e,r,n,t,a,o){var i=n&Un,s=Fe(e),u=s.length,d=Fe(r),c=d.length;if(u!=c&&!i)return!1;for(var p=u;p--;){var g=s[p];if(!(i?g in r:jn.call(r,g)))return!1}var m=o.get(e),y=o.get(r);if(m&&y)return m==r&&y==e;var f=!0;o.set(e,r),o.set(r,e);for(var T=i;++p<u;){g=s[p];var A=e[g],C=r[g];if(t)var N=i?t(C,A,g,r,e,o):t(A,C,g,e,r,o);if(!(N===void 0?A===C||a(A,C,n,t,o):N)){f=!1;break}T||(T=g=="constructor")}if(f&&!T){var x=e.constructor,E=r.constructor;x!=E&&"constructor"in e&&"constructor"in r&&!(typeof x=="function"&&x instanceof x&&typeof E=="function"&&E instanceof E)&&(f=!1)}return o.delete(e),o.delete(r),f}var qn=1,Ue="[object Arguments]",ke="[object Array]",J="[object Object]",Zn=Object.prototype,je=Zn.hasOwnProperty;function Jn(e,r,n,t,a,o){var i=L(e),s=L(r),u=i?ke:z(e),d=s?ke:z(r);u=u==Ue?J:u,d=d==Ue?J:d;var c=u==J,p=d==J,g=u==d;if(g&&Ee(e)){if(!Ee(r))return!1;i=!0,c=!1}if(g&&!c)return o||(o=new Y),i||dr(e)?er(e,r,n,t,a,o):Kn(e,r,u,n,t,a,o);if(!(n&qn)){var m=c&&je.call(e,"__wrapped__"),y=p&&je.call(r,"__wrapped__");if(m||y){var f=m?e.value():e,T=y?r.value():r;return o||(o=new Y),a(f,T,n,t,o)}}return g?(o||(o=new Y),Xn(e,r,n,t,a,o)):!1}function _e(e,r,n,t,a){return e===r?!0:e==null||r==null||!xe(e)&&!xe(r)?e!==e&&r!==r:Jn(e,r,n,t,_e,a)}var Yn=1,Qn=2;function Vn(e,r,n,t){var a=n.length,o=a;if(e==null)return!o;for(e=Object(e);a--;){var i=n[a];if(i[2]?i[1]!==e[i[0]]:!(i[0]in e))return!1}for(;++a<o;){i=n[a];var s=i[0],u=e[s],d=i[1];if(i[2]){if(u===void 0&&!(s in e))return!1}else{var c=new Y,p;if(!(p===void 0?_e(d,u,Yn|Qn,t,c):p))return!1}}return!0}function rr(e){return e===e&&!fr(e)}function et(e){for(var r=Ae(e),n=r.length;n--;){var t=r[n],a=e[t];r[n]=[t,a,rr(a)]}return r}function nr(e,r){return function(n){return n==null?!1:n[e]===r&&(r!==void 0||e in Object(n))}}function rt(e){var r=et(e);return r.length==1&&r[0][2]?nr(r[0][0],r[0][1]):function(n){return n===e||Vn(n,e,r)}}function nt(e,r){return e!=null&&r in Object(e)}function tt(e,r,n){r=Qe(r,e);for(var t=-1,a=r.length,o=!1;++t<a;){var i=ae(r[t]);if(!(o=e!=null&&n(e,i)))break;e=e[i]}return o||++t!=a?o:(a=e==null?0:e.length,!!a&&cr(a)&&vr(i,a)&&(L(e)||hr(e)))}function ot(e,r){return e!=null&&tt(e,r,nt)}var at=1,it=2;function st(e,r){return Ce(e)&&rr(r)?nr(ae(e),r):function(n){var t=sn(n,e);return t===void 0&&t===r?ot(n,e):_e(r,t,at|it)}}function lt(e){return function(r){return r==null?void 0:r[e]}}function ut(e){return function(r){return Ve(r,e)}}function dt(e){return Ce(e)?lt(ae(e)):ut(e)}function ft(e){return typeof e=="function"?e:e==null?gr:typeof e=="object"?L(e)?st(e[0],e[1]):rt(e):dt(e)}function ct(e,r){return e&&pr(e,r,Ae)}function vt(e,r){return function(n,t){if(n==null)return n;if(!be(n))return e(n,t);for(var a=n.length,o=-1,i=Object(n);++o<a&&t(i[o],o,i)!==!1;);return n}}var ht=vt(ct);function gt(e,r){var n=-1,t=be(e)?Array(e.length):[];return ht(e,function(a,o,i){t[++n]=r(a,o,i)}),t}function pt(e,r){var n=L(e)?wr:gt;return n(e,ft(r))}const he={top:"bottom",bottom:"top",left:"right",right:"left"},w="var(--n-arrow-height) * 1.414",wt=D([B("popover",`
 transition:
 box-shadow .3s var(--n-bezier),
 background-color .3s var(--n-bezier),
 color .3s var(--n-bezier);
 position: relative;
 font-size: var(--n-font-size);
 color: var(--n-text-color);
 box-shadow: var(--n-box-shadow);
 word-break: break-word;
 `,[D(">",[B("scrollbar",`
 height: inherit;
 max-height: inherit;
 `)]),le("raw",`
 background-color: var(--n-color);
 border-radius: var(--n-border-radius);
 `,[le("scrollable",[le("show-header-or-footer","padding: var(--n-padding);")])]),ue("header",`
 padding: var(--n-padding);
 border-bottom: 1px solid var(--n-divider-color);
 transition: border-color .3s var(--n-bezier);
 `),ue("footer",`
 padding: var(--n-padding);
 border-top: 1px solid var(--n-divider-color);
 transition: border-color .3s var(--n-bezier);
 `),ee("scrollable, show-header-or-footer",[ue("content",`
 padding: var(--n-padding);
 `)])]),B("popover-shared",`
 transform-origin: inherit;
 `,[B("popover-arrow-wrapper",`
 position: absolute;
 overflow: hidden;
 pointer-events: none;
 `,[B("popover-arrow",`
 transition: background-color .3s var(--n-bezier);
 position: absolute;
 display: block;
 width: calc(${w});
 height: calc(${w});
 box-shadow: 0 0 8px 0 rgba(0, 0, 0, .12);
 transform: rotate(45deg);
 background-color: var(--n-color);
 pointer-events: all;
 `)]),D("&.popover-transition-enter-from, &.popover-transition-leave-to",`
 opacity: 0;
 transform: scale(.85);
 `),D("&.popover-transition-enter-to, &.popover-transition-leave-from",`
 transform: scale(1);
 opacity: 1;
 `),D("&.popover-transition-enter-active",`
 transition:
 box-shadow .3s var(--n-bezier),
 background-color .3s var(--n-bezier),
 color .3s var(--n-bezier),
 opacity .15s var(--n-bezier-ease-out),
 transform .15s var(--n-bezier-ease-out);
 `),D("&.popover-transition-leave-active",`
 transition:
 box-shadow .3s var(--n-bezier),
 background-color .3s var(--n-bezier),
 color .3s var(--n-bezier),
 opacity .15s var(--n-bezier-ease-in),
 transform .15s var(--n-bezier-ease-in);
 `)]),P("top-start",`
 top: calc(${w} / -2);
 left: calc(${M("top-start")} - var(--v-offset-left));
 `),P("top",`
 top: calc(${w} / -2);
 transform: translateX(calc(${w} / -2)) rotate(45deg);
 left: 50%;
 `),P("top-end",`
 top: calc(${w} / -2);
 right: calc(${M("top-end")} + var(--v-offset-left));
 `),P("bottom-start",`
 bottom: calc(${w} / -2);
 left: calc(${M("bottom-start")} - var(--v-offset-left));
 `),P("bottom",`
 bottom: calc(${w} / -2);
 transform: translateX(calc(${w} / -2)) rotate(45deg);
 left: 50%;
 `),P("bottom-end",`
 bottom: calc(${w} / -2);
 right: calc(${M("bottom-end")} + var(--v-offset-left));
 `),P("left-start",`
 left: calc(${w} / -2);
 top: calc(${M("left-start")} - var(--v-offset-top));
 `),P("left",`
 left: calc(${w} / -2);
 transform: translateY(calc(${w} / -2)) rotate(45deg);
 top: 50%;
 `),P("left-end",`
 left: calc(${w} / -2);
 bottom: calc(${M("left-end")} + var(--v-offset-top));
 `),P("right-start",`
 right: calc(${w} / -2);
 top: calc(${M("right-start")} - var(--v-offset-top));
 `),P("right",`
 right: calc(${w} / -2);
 transform: translateY(calc(${w} / -2)) rotate(45deg);
 top: 50%;
 `),P("right-end",`
 right: calc(${w} / -2);
 bottom: calc(${M("right-end")} + var(--v-offset-top));
 `),...pt({top:["right-start","left-start"],right:["top-end","bottom-end"],bottom:["right-end","left-end"],left:["top-start","bottom-start"]},(e,r)=>{const n=["right","left"].includes(r),t=n?"width":"height";return e.map(a=>{const o=a.split("-")[1]==="end",s=`calc((${`var(--v-target-${t}, 0px)`} - ${w}) / 2)`,u=M(a);return D(`[v-placement="${a}"] >`,[B("popover-shared",[ee("center-arrow",[B("popover-arrow",`${r}: calc(max(${s}, ${u}) ${o?"+":"-"} var(--v-offset-${n?"left":"top"}));`)])])])})})]);function M(e){return["top","bottom"].includes(e.split("-")[0])?"var(--n-arrow-offset)":"var(--n-arrow-offset-vertical)"}function P(e,r){const n=e.split("-")[0],t=["top","bottom"].includes(n)?"height: var(--n-space-arrow);":"width: var(--n-space-arrow);";return D(`[v-placement="${e}"] >`,[B("popover-shared",`
 margin-${he[n]}: var(--n-space);
 `,[ee("show-arrow",`
 margin-${he[n]}: var(--n-space-arrow);
 `),ee("overlap",`
 margin: 0;
 `),mr("popover-arrow-wrapper",`
 right: 0;
 left: 0;
 top: 0;
 bottom: 0;
 ${n}: 100%;
 ${he[n]}: auto;
 ${t}
 `,[B("popover-arrow",r)])])])}const tr=Object.assign(Object.assign({},$e.props),{to:re.propTo,show:Boolean,trigger:String,showArrow:Boolean,delay:Number,duration:Number,raw:Boolean,arrowPointToCenter:Boolean,arrowClass:String,arrowStyle:[String,Object],arrowWrapperClass:String,arrowWrapperStyle:[String,Object],displayDirective:String,x:Number,y:Number,flip:Boolean,overlap:Boolean,placement:String,width:[Number,String],keepAliveOnHover:Boolean,scrollable:Boolean,contentClass:String,contentStyle:[Object,String],headerClass:String,headerStyle:[Object,String],footerClass:String,footerStyle:[Object,String],internalDeactivateImmediately:Boolean,animated:Boolean,onClickoutside:Function,internalTrapFocus:Boolean,internalOnAfterLeave:Function,minWidth:Number,maxWidth:Number});function mt({arrowClass:e,arrowStyle:r,arrowWrapperClass:n,arrowWrapperStyle:t,clsPrefix:a}){return S("div",{key:"__popover-arrow__",style:t,class:[`${a}-popover-arrow-wrapper`,n]},S("div",{class:[`${a}-popover-arrow`,e],style:r}))}const yt=Ze({name:"PopoverBody",inheritAttrs:!1,props:tr,setup(e,{slots:r,attrs:n}){const{namespaceRef:t,mergedClsPrefixRef:a,inlineThemeDisabled:o}=br(e),i=$e("Popover","-popover",wt,Ar,e,a),s=F(null),u=zr("NPopover"),d=F(null),c=F(e.show),p=F(!1);Je(()=>{const{show:v}=e;v&&!Xr()&&!e.internalDeactivateImmediately&&(p.value=!0)});const g=K(()=>{const{trigger:v,onClickoutside:$}=e,O=[],{positionManuallyRef:{value:h}}=u;return h||(v==="click"&&!$&&O.push([Re,x,void 0,{capture:!0}]),v==="hover"&&O.push([kr,N])),$&&O.push([Re,x,void 0,{capture:!0}]),(e.displayDirective==="show"||e.animated&&p.value)&&O.push([Sr,e.show]),O}),m=K(()=>{const{common:{cubicBezierEaseInOut:v,cubicBezierEaseIn:$,cubicBezierEaseOut:O},self:{space:h,spaceArrow:j,padding:X,fontSize:I,textColor:q,dividerColor:l,color:b,boxShadow:_,borderRadius:H,arrowHeight:W,arrowOffset:R,arrowOffsetVertical:or}}=i.value;return{"--n-box-shadow":_,"--n-bezier":v,"--n-bezier-ease-in":$,"--n-bezier-ease-out":O,"--n-font-size":I,"--n-text-color":q,"--n-color":b,"--n-divider-color":l,"--n-border-radius":H,"--n-arrow-height":W,"--n-arrow-offset":R,"--n-arrow-offset-vertical":or,"--n-padding":X,"--n-space":h,"--n-space-arrow":j}}),y=K(()=>{const v=e.width==="trigger"?void 0:fe(e.width),$=[];v&&$.push({width:v});const{maxWidth:O,minWidth:h}=e;return O&&$.push({maxWidth:fe(O)}),h&&$.push({maxWidth:fe(h)}),o||$.push(m.value),$}),f=o?$r("popover",void 0,m,e):void 0;u.setBodyInstance({syncPosition:T}),Fr(()=>{u.setBodyInstance(null)}),Lr(Q(e,"show"),v=>{e.animated||(v?c.value=!0:c.value=!1)});function T(){var v;(v=s.value)===null||v===void 0||v.syncPosition()}function A(v){e.trigger==="hover"&&e.keepAliveOnHover&&e.show&&u.handleMouseEnter(v)}function C(v){e.trigger==="hover"&&e.keepAliveOnHover&&u.handleMouseLeave(v)}function N(v){e.trigger==="hover"&&!E().contains(Me(v))&&u.handleMouseMoveOutside(v)}function x(v){(e.trigger==="click"&&!E().contains(Me(v))||e.onClickoutside)&&u.handleClickOutside(v)}function E(){return u.getTriggerElement()}V(_r,d),V(Tr,null),V(Pr,null);function k(){if(f==null||f.onRender(),!(e.displayDirective==="show"||e.show||e.animated&&p.value))return null;let $;const O=u.internalRenderBodyRef.value,{value:h}=a;if(O)$=O([`${h}-popover-shared`,f==null?void 0:f.themeClass.value,e.overlap&&`${h}-popover-shared--overlap`,e.showArrow&&`${h}-popover-shared--show-arrow`,e.arrowPointToCenter&&`${h}-popover-shared--center-arrow`],d,y.value,A,C);else{const{value:j}=u.extraClassRef,{internalTrapFocus:X}=e,I=!Be(r.header)||!Be(r.footer),q=()=>{var l,b;const _=I?S(Hr,null,de(r.header,R=>R?S("div",{class:[`${h}-popover__header`,e.headerClass],style:e.headerStyle},R):null),de(r.default,R=>R?S("div",{class:[`${h}-popover__content`,e.contentClass],style:e.contentStyle},r):null),de(r.footer,R=>R?S("div",{class:[`${h}-popover__footer`,e.footerClass],style:e.footerStyle},R):null)):e.scrollable?(l=r.default)===null||l===void 0?void 0:l.call(r):S("div",{class:[`${h}-popover__content`,e.contentClass],style:e.contentStyle},r),H=e.scrollable?S(Or,{contentClass:I?void 0:`${h}-popover__content ${(b=e.contentClass)!==null&&b!==void 0?b:""}`,contentStyle:I?void 0:e.contentStyle},{default:()=>_}):_,W=e.showArrow?mt({arrowClass:e.arrowClass,arrowStyle:e.arrowStyle,arrowWrapperClass:e.arrowWrapperClass,arrowWrapperStyle:e.arrowWrapperStyle,clsPrefix:h}):null;return[H,W]};$=S("div",Nr({class:[`${h}-popover`,`${h}-popover-shared`,f==null?void 0:f.themeClass.value,j.map(l=>`${h}-${l}`),{[`${h}-popover--scrollable`]:e.scrollable,[`${h}-popover--show-header-or-footer`]:I,[`${h}-popover--raw`]:e.raw,[`${h}-popover-shared--overlap`]:e.overlap,[`${h}-popover-shared--show-arrow`]:e.showArrow,[`${h}-popover-shared--center-arrow`]:e.arrowPointToCenter}],ref:d,style:y.value,onKeydown:u.handleKeydown,onMouseenter:A,onMouseleave:C},n),X?S(Cr,{active:e.show,autoFocus:!0},{default:q}):q())}return Ye($,g.value)}return{displayed:p,namespace:t,isMounted:u.isMountedRef,zIndex:u.zIndexRef,followerRef:s,adjustedTo:re(e),followerEnabled:c,renderContentNode:k}},render(){return S(Br,{ref:"followerRef",zIndex:this.zIndex,show:this.show,enabled:this.followerEnabled,to:this.adjustedTo,x:this.x,y:this.y,flip:this.flip,placement:this.placement,containerClass:this.namespace,overlap:this.overlap,width:this.width==="trigger"?"target":void 0,teleportDisabled:this.adjustedTo===re.tdkey},{default:()=>this.animated?S(yr,{name:"popover-transition",appear:this.isMounted,onEnter:()=>{this.followerEnabled=!0},onAfterLeave:()=>{var e;(e=this.internalOnAfterLeave)===null||e===void 0||e.call(this),this.followerEnabled=!1,this.displayed=!1}},{default:this.renderContentNode}):this.renderContentNode()})}}),bt=Object.keys(tr),St={focus:["onFocus","onBlur"],click:["onClick"],hover:["onMouseenter","onMouseleave"],manual:[],nested:["onFocus","onBlur","onMouseenter","onMouseleave","onClick"]};function $t(e,r,n){St[r].forEach(t=>{e.props?e.props=Object.assign({},e.props):e.props={};const a=e.props[t],o=n[t];a?e.props[t]=(...i)=>{a(...i),o(...i)}:e.props[t]=o})}const At={show:{type:Boolean,default:void 0},defaultShow:Boolean,showArrow:{type:Boolean,default:!0},trigger:{type:String,default:"hover"},delay:{type:Number,default:100},duration:{type:Number,default:100},raw:Boolean,placement:{type:String,default:"top"},x:Number,y:Number,arrowPointToCenter:Boolean,disabled:Boolean,getDisabled:Function,displayDirective:{type:String,default:"if"},arrowClass:String,arrowStyle:[String,Object],arrowWrapperClass:String,arrowWrapperStyle:[String,Object],flip:{type:Boolean,default:!0},animated:{type:Boolean,default:!0},width:{type:[Number,String],default:void 0},overlap:Boolean,keepAliveOnHover:{type:Boolean,default:!0},zIndex:Number,to:re.propTo,scrollable:Boolean,contentClass:String,contentStyle:[Object,String],headerClass:String,headerStyle:[Object,String],footerClass:String,footerStyle:[Object,String],onClickoutside:Function,"onUpdate:show":[Function,Array],onUpdateShow:[Function,Array],internalDeactivateImmediately:Boolean,internalSyncTargetWithParent:Boolean,internalInheritedEventHandlers:{type:Array,default:()=>[]},internalTrapFocus:Boolean,internalExtraClass:{type:Array,default:()=>[]},onShow:[Function,Array],onHide:[Function,Array],arrow:{type:Boolean,default:void 0},minWidth:Number,maxWidth:Number},Ct=Object.assign(Object.assign(Object.assign({},$e.props),At),{internalOnAfterLeave:Function,internalRenderBody:Function}),Et=Ze({name:"Popover",inheritAttrs:!1,props:Ct,slots:Object,__popover__:!0,setup(e){const r=Mr(),n=F(null),t=K(()=>e.show),a=F(e.defaultShow),o=Kr(t,a),i=Ie(()=>e.disabled?!1:o.value),s=()=>{if(e.disabled)return!0;const{getDisabled:l}=e;return!!(l!=null&&l())},u=()=>s()?!1:o.value,d=Ur(e,["arrow","showArrow"]),c=K(()=>e.overlap?!1:d.value);let p=null;const g=F(null),m=F(null),y=Ie(()=>e.x!==void 0&&e.y!==void 0);function f(l){const{"onUpdate:show":b,onUpdateShow:_,onShow:H,onHide:W}=e;a.value=l,b&&Z(b,l),_&&Z(_,l),l&&H&&Z(H,!0),l&&W&&Z(W,!1)}function T(){p&&p.syncPosition()}function A(){const{value:l}=g;l&&(window.clearTimeout(l),g.value=null)}function C(){const{value:l}=m;l&&(window.clearTimeout(l),m.value=null)}function N(){const l=s();if(e.trigger==="focus"&&!l){if(u())return;f(!0)}}function x(){const l=s();if(e.trigger==="focus"&&!l){if(!u())return;f(!1)}}function E(){const l=s();if(e.trigger==="hover"&&!l){if(C(),g.value!==null||u())return;const b=()=>{f(!0),g.value=null},{delay:_}=e;_===0?b():g.value=window.setTimeout(b,_)}}function k(){const l=s();if(e.trigger==="hover"&&!l){if(A(),m.value!==null||!u())return;const b=()=>{f(!1),m.value=null},{duration:_}=e;_===0?b():m.value=window.setTimeout(b,_)}}function v(){k()}function $(l){var b;u()&&(e.trigger==="click"&&(A(),C(),f(!1)),(b=e.onClickoutside)===null||b===void 0||b.call(e,l))}function O(){if(e.trigger==="click"&&!s()){A(),C();const l=!u();f(l)}}function h(l){e.internalTrapFocus&&l.key==="Escape"&&(A(),C(),f(!1))}function j(l){a.value=l}function X(){var l;return(l=n.value)===null||l===void 0?void 0:l.targetRef}function I(l){p=l}return V("NPopover",{getTriggerElement:X,handleKeydown:h,handleMouseEnter:E,handleMouseLeave:k,handleClickOutside:$,handleMouseMoveOutside:v,setBodyInstance:I,positionManuallyRef:y,isMountedRef:r,zIndexRef:Q(e,"zIndex"),extraClassRef:Q(e,"internalExtraClass"),internalRenderBodyRef:Q(e,"internalRenderBody")}),Je(()=>{o.value&&s()&&f(!1)}),{binderInstRef:n,positionManually:y,mergedShowConsideringDisabledProp:i,uncontrolledShow:a,mergedShowArrow:c,getMergedShow:u,setShow:j,handleClick:O,handleMouseEnter:E,handleMouseLeave:k,handleFocus:N,handleBlur:x,syncPosition:T}},render(){var e;const{positionManually:r,$slots:n}=this;let t,a=!1;if(!r&&(t=Er(n,"trigger"),t)){t=Wr(t),t=t.type===Gr?S("span",[t]):t;const o={onClick:this.handleClick,onMouseenter:this.handleMouseEnter,onMouseleave:this.handleMouseLeave,onFocus:this.handleFocus,onBlur:this.handleBlur};if(!((e=t.type)===null||e===void 0)&&e.__popover__)a=!0,t.props||(t.props={internalSyncTargetWithParent:!0,internalInheritedEventHandlers:[]}),t.props.internalSyncTargetWithParent=!0,t.props.internalInheritedEventHandlers?t.props.internalInheritedEventHandlers=[o,...t.props.internalInheritedEventHandlers]:t.props.internalInheritedEventHandlers=[o];else{const{internalInheritedEventHandlers:i}=this,s=[o,...i],u={onBlur:d=>{s.forEach(c=>{c.onBlur(d)})},onFocus:d=>{s.forEach(c=>{c.onFocus(d)})},onClick:d=>{s.forEach(c=>{c.onClick(d)})},onMouseenter:d=>{s.forEach(c=>{c.onMouseenter(d)})},onMouseleave:d=>{s.forEach(c=>{c.onMouseleave(d)})}};$t(t,i?"nested":r?"manual":this.trigger,u)}}return S(Ir,{ref:"binderInstRef",syncTarget:!a,syncTargetWithParent:this.internalSyncTargetWithParent},{default:()=>{this.mergedShowConsideringDisabledProp;const o=this.getMergedShow();return[this.internalTrapFocus&&o?Ye(S("div",{style:{position:"fixed",top:0,right:0,bottom:0,left:0}}),[[xr,{enabled:o,zIndex:this.zIndex}]]):null,r?null:S(Dr,null,{default:()=>t}),S(yt,Rr(this.$props,bt,Object.assign(Object.assign({},this.$attrs),{showArrow:this.mergedShowArrow,show:o})),{default:()=>{var i,s;return(s=(i=this.$slots).default)===null||s===void 0?void 0:s.call(i)},header:()=>{var i,s;return(s=(i=this.$slots).header)===null||s===void 0?void 0:s.call(i)},footer:()=>{var i,s;return(s=(i=this.$slots).footer)===null||s===void 0?void 0:s.call(i)}})]}})}});export{Et as N,fe as f,sn as g,At as p,mt as r,Ur as u};
