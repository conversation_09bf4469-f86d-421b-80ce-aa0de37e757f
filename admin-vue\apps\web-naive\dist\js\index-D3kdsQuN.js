var Jn=Object.defineProperty,er=Object.defineProperties;var tr=Object.getOwnPropertyDescriptors;var Ot=Object.getOwnPropertySymbols;var nr=Object.prototype.hasOwnProperty,rr=Object.prototype.propertyIsEnumerable;var Qe=(t,r,e)=>r in t?Jn(t,r,{enumerable:!0,configurable:!0,writable:!0,value:e}):t[r]=e,It=(t,r)=>{for(var e in r||(r={}))nr.call(r,e)&&Qe(t,e,r[e]);if(Ot)for(var e of Ot(r))rr.call(r,e)&&Qe(t,e,r[e]);return t},Pt=(t,r)=>er(t,tr(r));var l=(t,r,e)=>Qe(t,typeof r!="symbol"?r+"":r,e);import{a6 as ar,bm as h,bn as ce,ds as Ye,dt as jt,du as ir,a7 as sr,bL as or,ak as Re,I as Xe,q as fe,g as me,k as ye,ba as ur,j as Oe,i as cr,a4 as lr,aN as dr,bb as fr,l as mr,v as hr,m as Kt,n as Ct,aH as wr,bd as Yt,bc as gr,y as Z,dv as br}from"./bootstrap-B_sue86n.js";import{h as w,d as dt,b as yr,c as N,g as X,a4 as pr,w as Ze,E as vr,z as Ge,P as xr}from"../jse/index-index-UaL0SrHU.js";import{F as Dr,h as Tr}from"./FocusDetector-DeVNIRXA.js";import{B as kr,V as Mr,d as Nr,u as ot}from"./Follower-C2co6Kvh.js";import{N as Or}from"./Input-B6dOr09O.js";import{u as Ir}from"./use-locale-zaiRAV2Y.js";import{u as Pr}from"./use-keyboard-Bj6TfvCA.js";import{u as Cr}from"./use-merged-state-lZNesr9e.js";const Yr=ar("time",()=>w("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 512 512"},w("path",{d:"M256,64C150,64,64,150,64,256s86,192,192,192,192-86,192-192S362,64,256,64Z",style:`
        fill: none;
        stroke: currentColor;
        stroke-miterlimit: 10;
        stroke-width: 32px;
      `}),w("polyline",{points:"256 128 256 272 352 272",style:`
        fill: none;
        stroke: currentColor;
        stroke-linecap: round;
        stroke-linejoin: round;
        stroke-width: 32px;
      `})));function M(t,r){return t instanceof Date?new t.constructor(r):new Date(r)}function De(t,r){const e=h(t);return isNaN(r)?M(t,NaN):(r&&e.setDate(e.getDate()+r),e)}function ft(t,r){const e=h(t);if(isNaN(r))return M(t,NaN);if(!r)return e;const n=e.getDate(),a=M(t,e.getTime());a.setMonth(e.getMonth()+r+1,0);const i=a.getDate();return n>=i?a:(e.setFullYear(a.getFullYear(),a.getMonth(),n),e)}const Jt=6048e5,Fr=864e5,_r=6e4,Hr=36e5,Rr=1e3;function Te(t){return ce(t,{weekStartsOn:1})}function en(t){const r=h(t),e=r.getFullYear(),n=M(t,0);n.setFullYear(e+1,0,4),n.setHours(0,0,0,0);const a=Te(n),i=M(t,0);i.setFullYear(e,0,4),i.setHours(0,0,0,0);const o=Te(i);return r.getTime()>=a.getTime()?e+1:r.getTime()>=o.getTime()?e:e-1}function Ae(t){const r=h(t);return r.setHours(0,0,0,0),r}function Se(t){const r=h(t),e=new Date(Date.UTC(r.getFullYear(),r.getMonth(),r.getDate(),r.getHours(),r.getMinutes(),r.getSeconds(),r.getMilliseconds()));return e.setUTCFullYear(r.getFullYear()),+t-+e}function Er(t,r){const e=Ae(t),n=Ae(r),a=+e-Se(e),i=+n-Se(n);return Math.round((a-i)/Fr)}function Vr(t){const r=en(t),e=M(t,0);return e.setFullYear(r,0,4),e.setHours(0,0,0,0),Te(e)}function Ar(t,r){const e=r*3;return ft(t,e)}function Sr(t,r){return ft(t,r*12)}function zr(t,r){const e=Ae(t),n=Ae(r);return+e==+n}function qr(t){return t instanceof Date||typeof t=="object"&&Object.prototype.toString.call(t)==="[object Date]"}function mt(t){if(!qr(t)&&typeof t!="number")return!1;const r=h(t);return!isNaN(Number(r))}function $r(t){const r=h(t);return Math.trunc(r.getMonth()/3)+1}function Br(t){const r=h(t);return r.setSeconds(0,0),r}function Ft(t){const r=h(t),e=r.getMonth(),n=e-e%3;return r.setMonth(n,1),r.setHours(0,0,0,0),r}function Lr(t){const r=h(t);return r.setDate(1),r.setHours(0,0,0,0),r}function $e(t){const r=h(t),e=M(t,0);return e.setFullYear(r.getFullYear(),0,1),e.setHours(0,0,0,0),e}function Wr(t){const r=h(t);return Er(r,$e(r))+1}function tn(t){const r=h(t),e=+Te(r)-+Vr(r);return Math.round(e/Jt)+1}function ht(t,r){var g,p,_,v,O,H,$,B;const e=h(t),n=e.getFullYear(),a=Ye(),i=(B=($=(v=(_=r==null?void 0:r.firstWeekContainsDate)!=null?_:(p=(g=r==null?void 0:r.locale)==null?void 0:g.options)==null?void 0:p.firstWeekContainsDate)!=null?v:a.firstWeekContainsDate)!=null?$:(H=(O=a.locale)==null?void 0:O.options)==null?void 0:H.firstWeekContainsDate)!=null?B:1,o=M(t,0);o.setFullYear(n+1,0,i),o.setHours(0,0,0,0);const u=ce(o,r),d=M(t,0);d.setFullYear(n,0,i),d.setHours(0,0,0,0);const m=ce(d,r);return e.getTime()>=u.getTime()?n+1:e.getTime()>=m.getTime()?n:n-1}function Ur(t,r){var u,d,m,g,p,_,v,O;const e=Ye(),n=(O=(v=(g=(m=r==null?void 0:r.firstWeekContainsDate)!=null?m:(d=(u=r==null?void 0:r.locale)==null?void 0:u.options)==null?void 0:d.firstWeekContainsDate)!=null?g:e.firstWeekContainsDate)!=null?v:(_=(p=e.locale)==null?void 0:p.options)==null?void 0:_.firstWeekContainsDate)!=null?O:1,a=ht(t,r),i=M(t,0);return i.setFullYear(a,0,n),i.setHours(0,0,0,0),ce(i,r)}function nn(t,r){const e=h(t),n=+ce(e,r)-+Ur(e,r);return Math.round(n/Jt)+1}function D(t,r){const e=t<0?"-":"",n=Math.abs(t).toString().padStart(r,"0");return e+n}const ue={y(t,r){const e=t.getFullYear(),n=e>0?e:1-e;return D(r==="yy"?n%100:n,r.length)},M(t,r){const e=t.getMonth();return r==="M"?String(e+1):D(e+1,2)},d(t,r){return D(t.getDate(),r.length)},a(t,r){const e=t.getHours()/12>=1?"pm":"am";switch(r){case"a":case"aa":return e.toUpperCase();case"aaa":return e;case"aaaaa":return e[0];case"aaaa":default:return e==="am"?"a.m.":"p.m."}},h(t,r){return D(t.getHours()%12||12,r.length)},H(t,r){return D(t.getHours(),r.length)},m(t,r){return D(t.getMinutes(),r.length)},s(t,r){return D(t.getSeconds(),r.length)},S(t,r){const e=r.length,n=t.getMilliseconds(),a=Math.trunc(n*Math.pow(10,e-3));return D(a,r.length)}},pe={midnight:"midnight",noon:"noon",morning:"morning",afternoon:"afternoon",evening:"evening",night:"night"},_t={G:function(t,r,e){const n=t.getFullYear()>0?1:0;switch(r){case"G":case"GG":case"GGG":return e.era(n,{width:"abbreviated"});case"GGGGG":return e.era(n,{width:"narrow"});case"GGGG":default:return e.era(n,{width:"wide"})}},y:function(t,r,e){if(r==="yo"){const n=t.getFullYear(),a=n>0?n:1-n;return e.ordinalNumber(a,{unit:"year"})}return ue.y(t,r)},Y:function(t,r,e,n){const a=ht(t,n),i=a>0?a:1-a;if(r==="YY"){const o=i%100;return D(o,2)}return r==="Yo"?e.ordinalNumber(i,{unit:"year"}):D(i,r.length)},R:function(t,r){const e=en(t);return D(e,r.length)},u:function(t,r){const e=t.getFullYear();return D(e,r.length)},Q:function(t,r,e){const n=Math.ceil((t.getMonth()+1)/3);switch(r){case"Q":return String(n);case"QQ":return D(n,2);case"Qo":return e.ordinalNumber(n,{unit:"quarter"});case"QQQ":return e.quarter(n,{width:"abbreviated",context:"formatting"});case"QQQQQ":return e.quarter(n,{width:"narrow",context:"formatting"});case"QQQQ":default:return e.quarter(n,{width:"wide",context:"formatting"})}},q:function(t,r,e){const n=Math.ceil((t.getMonth()+1)/3);switch(r){case"q":return String(n);case"qq":return D(n,2);case"qo":return e.ordinalNumber(n,{unit:"quarter"});case"qqq":return e.quarter(n,{width:"abbreviated",context:"standalone"});case"qqqqq":return e.quarter(n,{width:"narrow",context:"standalone"});case"qqqq":default:return e.quarter(n,{width:"wide",context:"standalone"})}},M:function(t,r,e){const n=t.getMonth();switch(r){case"M":case"MM":return ue.M(t,r);case"Mo":return e.ordinalNumber(n+1,{unit:"month"});case"MMM":return e.month(n,{width:"abbreviated",context:"formatting"});case"MMMMM":return e.month(n,{width:"narrow",context:"formatting"});case"MMMM":default:return e.month(n,{width:"wide",context:"formatting"})}},L:function(t,r,e){const n=t.getMonth();switch(r){case"L":return String(n+1);case"LL":return D(n+1,2);case"Lo":return e.ordinalNumber(n+1,{unit:"month"});case"LLL":return e.month(n,{width:"abbreviated",context:"standalone"});case"LLLLL":return e.month(n,{width:"narrow",context:"standalone"});case"LLLL":default:return e.month(n,{width:"wide",context:"standalone"})}},w:function(t,r,e,n){const a=nn(t,n);return r==="wo"?e.ordinalNumber(a,{unit:"week"}):D(a,r.length)},I:function(t,r,e){const n=tn(t);return r==="Io"?e.ordinalNumber(n,{unit:"week"}):D(n,r.length)},d:function(t,r,e){return r==="do"?e.ordinalNumber(t.getDate(),{unit:"date"}):ue.d(t,r)},D:function(t,r,e){const n=Wr(t);return r==="Do"?e.ordinalNumber(n,{unit:"dayOfYear"}):D(n,r.length)},E:function(t,r,e){const n=t.getDay();switch(r){case"E":case"EE":case"EEE":return e.day(n,{width:"abbreviated",context:"formatting"});case"EEEEE":return e.day(n,{width:"narrow",context:"formatting"});case"EEEEEE":return e.day(n,{width:"short",context:"formatting"});case"EEEE":default:return e.day(n,{width:"wide",context:"formatting"})}},e:function(t,r,e,n){const a=t.getDay(),i=(a-n.weekStartsOn+8)%7||7;switch(r){case"e":return String(i);case"ee":return D(i,2);case"eo":return e.ordinalNumber(i,{unit:"day"});case"eee":return e.day(a,{width:"abbreviated",context:"formatting"});case"eeeee":return e.day(a,{width:"narrow",context:"formatting"});case"eeeeee":return e.day(a,{width:"short",context:"formatting"});case"eeee":default:return e.day(a,{width:"wide",context:"formatting"})}},c:function(t,r,e,n){const a=t.getDay(),i=(a-n.weekStartsOn+8)%7||7;switch(r){case"c":return String(i);case"cc":return D(i,r.length);case"co":return e.ordinalNumber(i,{unit:"day"});case"ccc":return e.day(a,{width:"abbreviated",context:"standalone"});case"ccccc":return e.day(a,{width:"narrow",context:"standalone"});case"cccccc":return e.day(a,{width:"short",context:"standalone"});case"cccc":default:return e.day(a,{width:"wide",context:"standalone"})}},i:function(t,r,e){const n=t.getDay(),a=n===0?7:n;switch(r){case"i":return String(a);case"ii":return D(a,r.length);case"io":return e.ordinalNumber(a,{unit:"day"});case"iii":return e.day(n,{width:"abbreviated",context:"formatting"});case"iiiii":return e.day(n,{width:"narrow",context:"formatting"});case"iiiiii":return e.day(n,{width:"short",context:"formatting"});case"iiii":default:return e.day(n,{width:"wide",context:"formatting"})}},a:function(t,r,e){const a=t.getHours()/12>=1?"pm":"am";switch(r){case"a":case"aa":return e.dayPeriod(a,{width:"abbreviated",context:"formatting"});case"aaa":return e.dayPeriod(a,{width:"abbreviated",context:"formatting"}).toLowerCase();case"aaaaa":return e.dayPeriod(a,{width:"narrow",context:"formatting"});case"aaaa":default:return e.dayPeriod(a,{width:"wide",context:"formatting"})}},b:function(t,r,e){const n=t.getHours();let a;switch(n===12?a=pe.noon:n===0?a=pe.midnight:a=n/12>=1?"pm":"am",r){case"b":case"bb":return e.dayPeriod(a,{width:"abbreviated",context:"formatting"});case"bbb":return e.dayPeriod(a,{width:"abbreviated",context:"formatting"}).toLowerCase();case"bbbbb":return e.dayPeriod(a,{width:"narrow",context:"formatting"});case"bbbb":default:return e.dayPeriod(a,{width:"wide",context:"formatting"})}},B:function(t,r,e){const n=t.getHours();let a;switch(n>=17?a=pe.evening:n>=12?a=pe.afternoon:n>=4?a=pe.morning:a=pe.night,r){case"B":case"BB":case"BBB":return e.dayPeriod(a,{width:"abbreviated",context:"formatting"});case"BBBBB":return e.dayPeriod(a,{width:"narrow",context:"formatting"});case"BBBB":default:return e.dayPeriod(a,{width:"wide",context:"formatting"})}},h:function(t,r,e){if(r==="ho"){let n=t.getHours()%12;return n===0&&(n=12),e.ordinalNumber(n,{unit:"hour"})}return ue.h(t,r)},H:function(t,r,e){return r==="Ho"?e.ordinalNumber(t.getHours(),{unit:"hour"}):ue.H(t,r)},K:function(t,r,e){const n=t.getHours()%12;return r==="Ko"?e.ordinalNumber(n,{unit:"hour"}):D(n,r.length)},k:function(t,r,e){let n=t.getHours();return n===0&&(n=24),r==="ko"?e.ordinalNumber(n,{unit:"hour"}):D(n,r.length)},m:function(t,r,e){return r==="mo"?e.ordinalNumber(t.getMinutes(),{unit:"minute"}):ue.m(t,r)},s:function(t,r,e){return r==="so"?e.ordinalNumber(t.getSeconds(),{unit:"second"}):ue.s(t,r)},S:function(t,r){return ue.S(t,r)},X:function(t,r,e){const n=t.getTimezoneOffset();if(n===0)return"Z";switch(r){case"X":return Rt(n);case"XXXX":case"XX":return we(n);case"XXXXX":case"XXX":default:return we(n,":")}},x:function(t,r,e){const n=t.getTimezoneOffset();switch(r){case"x":return Rt(n);case"xxxx":case"xx":return we(n);case"xxxxx":case"xxx":default:return we(n,":")}},O:function(t,r,e){const n=t.getTimezoneOffset();switch(r){case"O":case"OO":case"OOO":return"GMT"+Ht(n,":");case"OOOO":default:return"GMT"+we(n,":")}},z:function(t,r,e){const n=t.getTimezoneOffset();switch(r){case"z":case"zz":case"zzz":return"GMT"+Ht(n,":");case"zzzz":default:return"GMT"+we(n,":")}},t:function(t,r,e){const n=Math.trunc(t.getTime()/1e3);return D(n,r.length)},T:function(t,r,e){const n=t.getTime();return D(n,r.length)}};function Ht(t,r=""){const e=t>0?"-":"+",n=Math.abs(t),a=Math.trunc(n/60),i=n%60;return i===0?e+String(a):e+String(a)+r+D(i,2)}function Rt(t,r){return t%60===0?(t>0?"-":"+")+D(Math.abs(t)/60,2):we(t,r)}function we(t,r=""){const e=t>0?"-":"+",n=Math.abs(t),a=D(Math.trunc(n/60),2),i=D(n%60,2);return e+a+r+i}const Et=(t,r)=>{switch(t){case"P":return r.date({width:"short"});case"PP":return r.date({width:"medium"});case"PPP":return r.date({width:"long"});case"PPPP":default:return r.date({width:"full"})}},rn=(t,r)=>{switch(t){case"p":return r.time({width:"short"});case"pp":return r.time({width:"medium"});case"ppp":return r.time({width:"long"});case"pppp":default:return r.time({width:"full"})}},Qr=(t,r)=>{const e=t.match(/(P+)(p+)?/)||[],n=e[1],a=e[2];if(!a)return Et(t,r);let i;switch(n){case"P":i=r.dateTime({width:"short"});break;case"PP":i=r.dateTime({width:"medium"});break;case"PPP":i=r.dateTime({width:"long"});break;case"PPPP":default:i=r.dateTime({width:"full"});break}return i.replace("{{date}}",Et(n,r)).replace("{{time}}",rn(a,r))},ut={p:rn,P:Qr},Xr=/^D+$/,Zr=/^Y+$/,Gr=["D","DD","YY","YYYY"];function an(t){return Xr.test(t)}function sn(t){return Zr.test(t)}function ct(t,r,e){const n=jr(t,r,e);if(console.warn(n),Gr.includes(t))throw new RangeError(n)}function jr(t,r,e){const n=t[0]==="Y"?"years":"days of the month";return`Use \`${t.toLowerCase()}\` instead of \`${t}\` (in \`${r}\`) for formatting ${n} to the input \`${e}\`; see: https://github.com/date-fns/date-fns/blob/master/docs/unicodeTokens.md`}const Kr=/[yYQqMLwIdDecihHKkms]o|(\w)\1*|''|'(''|[^'])+('|$)|./g,Jr=/P+p+|P+|p+|''|'(''|[^'])+('|$)|./g,ea=/^'([^]*?)'?$/,ta=/''/g,na=/[a-zA-Z]/;function ke(t,r,e){var g,p,_,v,O,H,$,B,U,K,T,L,Q,ie,le,S,J,ee;const n=Ye(),a=(p=(g=e==null?void 0:e.locale)!=null?g:n.locale)!=null?p:jt,i=(K=(U=(H=(O=e==null?void 0:e.firstWeekContainsDate)!=null?O:(v=(_=e==null?void 0:e.locale)==null?void 0:_.options)==null?void 0:v.firstWeekContainsDate)!=null?H:n.firstWeekContainsDate)!=null?U:(B=($=n.locale)==null?void 0:$.options)==null?void 0:B.firstWeekContainsDate)!=null?K:1,o=(ee=(J=(ie=(Q=e==null?void 0:e.weekStartsOn)!=null?Q:(L=(T=e==null?void 0:e.locale)==null?void 0:T.options)==null?void 0:L.weekStartsOn)!=null?ie:n.weekStartsOn)!=null?J:(S=(le=n.locale)==null?void 0:le.options)==null?void 0:S.weekStartsOn)!=null?ee:0,u=h(t);if(!mt(u))throw new RangeError("Invalid time value");let d=r.match(Jr).map(V=>{const R=V[0];if(R==="p"||R==="P"){const se=ut[R];return se(V,a.formatLong)}return V}).join("").match(Kr).map(V=>{if(V==="''")return{isToken:!1,value:"'"};const R=V[0];if(R==="'")return{isToken:!1,value:ra(V)};if(_t[R])return{isToken:!0,value:V};if(R.match(na))throw new RangeError("Format string contains an unescaped latin alphabet character `"+R+"`");return{isToken:!1,value:V}});a.localize.preprocessor&&(d=a.localize.preprocessor(u,d));const m={firstWeekContainsDate:i,weekStartsOn:o,locale:a};return d.map(V=>{if(!V.isToken)return V.value;const R=V.value;(!(e!=null&&e.useAdditionalWeekYearTokens)&&sn(R)||!(e!=null&&e.useAdditionalDayOfYearTokens)&&an(R))&&ct(R,r,String(t));const se=_t[R[0]];return se(u,R,a.localize,m)}).join("")}function ra(t){const r=t.match(ea);return r?r[1].replace(ta,"'"):t}function on(t){return h(t).getDate()}function aa(t){return h(t).getDay()}function ia(t){const r=h(t),e=r.getFullYear(),n=r.getMonth(),a=M(t,0);return a.setFullYear(e,n+1,0),a.setHours(0,0,0,0),a.getDate()}function un(){return Object.assign({},Ye())}function ve(t){return h(t).getHours()}function sa(t){let e=h(t).getDay();return e===0&&(e=7),e}function oa(t){return h(t).getMilliseconds()}function Vt(t){return h(t).getMinutes()}function Ce(t){return h(t).getMonth()}function At(t){return h(t).getSeconds()}function k(t){return h(t).getTime()}function Fe(t){return h(t).getFullYear()}function ua(t,r){const e=r instanceof Date?M(r,0):new r(0);return e.setFullYear(t.getFullYear(),t.getMonth(),t.getDate()),e.setHours(t.getHours(),t.getMinutes(),t.getSeconds(),t.getMilliseconds()),e}const ca=10;class cn{constructor(){l(this,"subPriority",0)}validate(r,e){return!0}}class la extends cn{constructor(r,e,n,a,i){super(),this.value=r,this.validateValue=e,this.setValue=n,this.priority=a,i&&(this.subPriority=i)}validate(r,e){return this.validateValue(r,this.value,e)}set(r,e,n){return this.setValue(r,e,this.value,n)}}class da extends cn{constructor(){super(...arguments);l(this,"priority",ca);l(this,"subPriority",-1)}set(e,n){return n.timestampIsSet?e:M(e,ua(e,Date))}}class y{run(r,e,n,a){const i=this.parse(r,e,n,a);return i?{setter:new la(i.value,this.validate,this.set,this.priority,this.subPriority),rest:i.rest}:null}validate(r,e,n){return!0}}class fa extends y{constructor(){super(...arguments);l(this,"priority",140);l(this,"incompatibleTokens",["R","u","t","T"])}parse(e,n,a){switch(n){case"G":case"GG":case"GGG":return a.era(e,{width:"abbreviated"})||a.era(e,{width:"narrow"});case"GGGGG":return a.era(e,{width:"narrow"});case"GGGG":default:return a.era(e,{width:"wide"})||a.era(e,{width:"abbreviated"})||a.era(e,{width:"narrow"})}}set(e,n,a){return n.era=a,e.setFullYear(a,0,1),e.setHours(0,0,0,0),e}}const Y={month:/^(1[0-2]|0?\d)/,date:/^(3[0-1]|[0-2]?\d)/,dayOfYear:/^(36[0-6]|3[0-5]\d|[0-2]?\d?\d)/,week:/^(5[0-3]|[0-4]?\d)/,hour23h:/^(2[0-3]|[0-1]?\d)/,hour24h:/^(2[0-4]|[0-1]?\d)/,hour11h:/^(1[0-1]|0?\d)/,hour12h:/^(1[0-2]|0?\d)/,minute:/^[0-5]?\d/,second:/^[0-5]?\d/,singleDigit:/^\d/,twoDigits:/^\d{1,2}/,threeDigits:/^\d{1,3}/,fourDigits:/^\d{1,4}/,anyDigitsSigned:/^-?\d+/,singleDigitSigned:/^-?\d/,twoDigitsSigned:/^-?\d{1,2}/,threeDigitsSigned:/^-?\d{1,3}/,fourDigitsSigned:/^-?\d{1,4}/},G={basicOptionalMinutes:/^([+-])(\d{2})(\d{2})?|Z/,basic:/^([+-])(\d{2})(\d{2})|Z/,basicOptionalSeconds:/^([+-])(\d{2})(\d{2})((\d{2}))?|Z/,extended:/^([+-])(\d{2}):(\d{2})|Z/,extendedOptionalSeconds:/^([+-])(\d{2}):(\d{2})(:(\d{2}))?|Z/};function F(t,r){return t&&{value:r(t.value),rest:t.rest}}function P(t,r){const e=r.match(t);return e?{value:parseInt(e[0],10),rest:r.slice(e[0].length)}:null}function j(t,r){const e=r.match(t);if(!e)return null;if(e[0]==="Z")return{value:0,rest:r.slice(1)};const n=e[1]==="+"?1:-1,a=e[2]?parseInt(e[2],10):0,i=e[3]?parseInt(e[3],10):0,o=e[5]?parseInt(e[5],10):0;return{value:n*(a*Hr+i*_r+o*Rr),rest:r.slice(e[0].length)}}function ln(t){return P(Y.anyDigitsSigned,t)}function C(t,r){switch(t){case 1:return P(Y.singleDigit,r);case 2:return P(Y.twoDigits,r);case 3:return P(Y.threeDigits,r);case 4:return P(Y.fourDigits,r);default:return P(new RegExp("^\\d{1,"+t+"}"),r)}}function ze(t,r){switch(t){case 1:return P(Y.singleDigitSigned,r);case 2:return P(Y.twoDigitsSigned,r);case 3:return P(Y.threeDigitsSigned,r);case 4:return P(Y.fourDigitsSigned,r);default:return P(new RegExp("^-?\\d{1,"+t+"}"),r)}}function wt(t){switch(t){case"morning":return 4;case"evening":return 17;case"pm":case"noon":case"afternoon":return 12;case"am":case"midnight":case"night":default:return 0}}function dn(t,r){const e=r>0,n=e?r:1-r;let a;if(n<=50)a=t||100;else{const i=n+50,o=Math.trunc(i/100)*100,u=t>=i%100;a=t+o-(u?100:0)}return e?a:1-a}function fn(t){return t%400===0||t%4===0&&t%100!==0}class ma extends y{constructor(){super(...arguments);l(this,"priority",130);l(this,"incompatibleTokens",["Y","R","u","w","I","i","e","c","t","T"])}parse(e,n,a){const i=o=>({year:o,isTwoDigitYear:n==="yy"});switch(n){case"y":return F(C(4,e),i);case"yo":return F(a.ordinalNumber(e,{unit:"year"}),i);default:return F(C(n.length,e),i)}}validate(e,n){return n.isTwoDigitYear||n.year>0}set(e,n,a){const i=e.getFullYear();if(a.isTwoDigitYear){const u=dn(a.year,i);return e.setFullYear(u,0,1),e.setHours(0,0,0,0),e}const o=!("era"in n)||n.era===1?a.year:1-a.year;return e.setFullYear(o,0,1),e.setHours(0,0,0,0),e}}class ha extends y{constructor(){super(...arguments);l(this,"priority",130);l(this,"incompatibleTokens",["y","R","u","Q","q","M","L","I","d","D","i","t","T"])}parse(e,n,a){const i=o=>({year:o,isTwoDigitYear:n==="YY"});switch(n){case"Y":return F(C(4,e),i);case"Yo":return F(a.ordinalNumber(e,{unit:"year"}),i);default:return F(C(n.length,e),i)}}validate(e,n){return n.isTwoDigitYear||n.year>0}set(e,n,a,i){const o=ht(e,i);if(a.isTwoDigitYear){const d=dn(a.year,o);return e.setFullYear(d,0,i.firstWeekContainsDate),e.setHours(0,0,0,0),ce(e,i)}const u=!("era"in n)||n.era===1?a.year:1-a.year;return e.setFullYear(u,0,i.firstWeekContainsDate),e.setHours(0,0,0,0),ce(e,i)}}class wa extends y{constructor(){super(...arguments);l(this,"priority",130);l(this,"incompatibleTokens",["G","y","Y","u","Q","q","M","L","w","d","D","e","c","t","T"])}parse(e,n){return ze(n==="R"?4:n.length,e)}set(e,n,a){const i=M(e,0);return i.setFullYear(a,0,4),i.setHours(0,0,0,0),Te(i)}}class ga extends y{constructor(){super(...arguments);l(this,"priority",130);l(this,"incompatibleTokens",["G","y","Y","R","w","I","i","e","c","t","T"])}parse(e,n){return ze(n==="u"?4:n.length,e)}set(e,n,a){return e.setFullYear(a,0,1),e.setHours(0,0,0,0),e}}class ba extends y{constructor(){super(...arguments);l(this,"priority",120);l(this,"incompatibleTokens",["Y","R","q","M","L","w","I","d","D","i","e","c","t","T"])}parse(e,n,a){switch(n){case"Q":case"QQ":return C(n.length,e);case"Qo":return a.ordinalNumber(e,{unit:"quarter"});case"QQQ":return a.quarter(e,{width:"abbreviated",context:"formatting"})||a.quarter(e,{width:"narrow",context:"formatting"});case"QQQQQ":return a.quarter(e,{width:"narrow",context:"formatting"});case"QQQQ":default:return a.quarter(e,{width:"wide",context:"formatting"})||a.quarter(e,{width:"abbreviated",context:"formatting"})||a.quarter(e,{width:"narrow",context:"formatting"})}}validate(e,n){return n>=1&&n<=4}set(e,n,a){return e.setMonth((a-1)*3,1),e.setHours(0,0,0,0),e}}class ya extends y{constructor(){super(...arguments);l(this,"priority",120);l(this,"incompatibleTokens",["Y","R","Q","M","L","w","I","d","D","i","e","c","t","T"])}parse(e,n,a){switch(n){case"q":case"qq":return C(n.length,e);case"qo":return a.ordinalNumber(e,{unit:"quarter"});case"qqq":return a.quarter(e,{width:"abbreviated",context:"standalone"})||a.quarter(e,{width:"narrow",context:"standalone"});case"qqqqq":return a.quarter(e,{width:"narrow",context:"standalone"});case"qqqq":default:return a.quarter(e,{width:"wide",context:"standalone"})||a.quarter(e,{width:"abbreviated",context:"standalone"})||a.quarter(e,{width:"narrow",context:"standalone"})}}validate(e,n){return n>=1&&n<=4}set(e,n,a){return e.setMonth((a-1)*3,1),e.setHours(0,0,0,0),e}}class pa extends y{constructor(){super(...arguments);l(this,"incompatibleTokens",["Y","R","q","Q","L","w","I","D","i","e","c","t","T"]);l(this,"priority",110)}parse(e,n,a){const i=o=>o-1;switch(n){case"M":return F(P(Y.month,e),i);case"MM":return F(C(2,e),i);case"Mo":return F(a.ordinalNumber(e,{unit:"month"}),i);case"MMM":return a.month(e,{width:"abbreviated",context:"formatting"})||a.month(e,{width:"narrow",context:"formatting"});case"MMMMM":return a.month(e,{width:"narrow",context:"formatting"});case"MMMM":default:return a.month(e,{width:"wide",context:"formatting"})||a.month(e,{width:"abbreviated",context:"formatting"})||a.month(e,{width:"narrow",context:"formatting"})}}validate(e,n){return n>=0&&n<=11}set(e,n,a){return e.setMonth(a,1),e.setHours(0,0,0,0),e}}class va extends y{constructor(){super(...arguments);l(this,"priority",110);l(this,"incompatibleTokens",["Y","R","q","Q","M","w","I","D","i","e","c","t","T"])}parse(e,n,a){const i=o=>o-1;switch(n){case"L":return F(P(Y.month,e),i);case"LL":return F(C(2,e),i);case"Lo":return F(a.ordinalNumber(e,{unit:"month"}),i);case"LLL":return a.month(e,{width:"abbreviated",context:"standalone"})||a.month(e,{width:"narrow",context:"standalone"});case"LLLLL":return a.month(e,{width:"narrow",context:"standalone"});case"LLLL":default:return a.month(e,{width:"wide",context:"standalone"})||a.month(e,{width:"abbreviated",context:"standalone"})||a.month(e,{width:"narrow",context:"standalone"})}}validate(e,n){return n>=0&&n<=11}set(e,n,a){return e.setMonth(a,1),e.setHours(0,0,0,0),e}}function xa(t,r,e){const n=h(t),a=nn(n,e)-r;return n.setDate(n.getDate()-a*7),n}class Da extends y{constructor(){super(...arguments);l(this,"priority",100);l(this,"incompatibleTokens",["y","R","u","q","Q","M","L","I","d","D","i","t","T"])}parse(e,n,a){switch(n){case"w":return P(Y.week,e);case"wo":return a.ordinalNumber(e,{unit:"week"});default:return C(n.length,e)}}validate(e,n){return n>=1&&n<=53}set(e,n,a,i){return ce(xa(e,a,i),i)}}function Ta(t,r){const e=h(t),n=tn(e)-r;return e.setDate(e.getDate()-n*7),e}class ka extends y{constructor(){super(...arguments);l(this,"priority",100);l(this,"incompatibleTokens",["y","Y","u","q","Q","M","L","w","d","D","e","c","t","T"])}parse(e,n,a){switch(n){case"I":return P(Y.week,e);case"Io":return a.ordinalNumber(e,{unit:"week"});default:return C(n.length,e)}}validate(e,n){return n>=1&&n<=53}set(e,n,a){return Te(Ta(e,a))}}const Ma=[31,28,31,30,31,30,31,31,30,31,30,31],Na=[31,29,31,30,31,30,31,31,30,31,30,31];class Oa extends y{constructor(){super(...arguments);l(this,"priority",90);l(this,"subPriority",1);l(this,"incompatibleTokens",["Y","R","q","Q","w","I","D","i","e","c","t","T"])}parse(e,n,a){switch(n){case"d":return P(Y.date,e);case"do":return a.ordinalNumber(e,{unit:"date"});default:return C(n.length,e)}}validate(e,n){const a=e.getFullYear(),i=fn(a),o=e.getMonth();return i?n>=1&&n<=Na[o]:n>=1&&n<=Ma[o]}set(e,n,a){return e.setDate(a),e.setHours(0,0,0,0),e}}class Ia extends y{constructor(){super(...arguments);l(this,"priority",90);l(this,"subpriority",1);l(this,"incompatibleTokens",["Y","R","q","Q","M","L","w","I","d","E","i","e","c","t","T"])}parse(e,n,a){switch(n){case"D":case"DD":return P(Y.dayOfYear,e);case"Do":return a.ordinalNumber(e,{unit:"date"});default:return C(n.length,e)}}validate(e,n){const a=e.getFullYear();return fn(a)?n>=1&&n<=366:n>=1&&n<=365}set(e,n,a){return e.setMonth(0,a),e.setHours(0,0,0,0),e}}function gt(t,r,e){var p,_,v,O,H,$,B,U;const n=Ye(),a=(U=(B=(O=(v=e==null?void 0:e.weekStartsOn)!=null?v:(_=(p=e==null?void 0:e.locale)==null?void 0:p.options)==null?void 0:_.weekStartsOn)!=null?O:n.weekStartsOn)!=null?B:($=(H=n.locale)==null?void 0:H.options)==null?void 0:$.weekStartsOn)!=null?U:0,i=h(t),o=i.getDay(),d=(r%7+7)%7,m=7-a,g=r<0||r>6?r-(o+m)%7:(d+m)%7-(o+m)%7;return De(i,g)}class Pa extends y{constructor(){super(...arguments);l(this,"priority",90);l(this,"incompatibleTokens",["D","i","e","c","t","T"])}parse(e,n,a){switch(n){case"E":case"EE":case"EEE":return a.day(e,{width:"abbreviated",context:"formatting"})||a.day(e,{width:"short",context:"formatting"})||a.day(e,{width:"narrow",context:"formatting"});case"EEEEE":return a.day(e,{width:"narrow",context:"formatting"});case"EEEEEE":return a.day(e,{width:"short",context:"formatting"})||a.day(e,{width:"narrow",context:"formatting"});case"EEEE":default:return a.day(e,{width:"wide",context:"formatting"})||a.day(e,{width:"abbreviated",context:"formatting"})||a.day(e,{width:"short",context:"formatting"})||a.day(e,{width:"narrow",context:"formatting"})}}validate(e,n){return n>=0&&n<=6}set(e,n,a,i){return e=gt(e,a,i),e.setHours(0,0,0,0),e}}class Ca extends y{constructor(){super(...arguments);l(this,"priority",90);l(this,"incompatibleTokens",["y","R","u","q","Q","M","L","I","d","D","E","i","c","t","T"])}parse(e,n,a,i){const o=u=>{const d=Math.floor((u-1)/7)*7;return(u+i.weekStartsOn+6)%7+d};switch(n){case"e":case"ee":return F(C(n.length,e),o);case"eo":return F(a.ordinalNumber(e,{unit:"day"}),o);case"eee":return a.day(e,{width:"abbreviated",context:"formatting"})||a.day(e,{width:"short",context:"formatting"})||a.day(e,{width:"narrow",context:"formatting"});case"eeeee":return a.day(e,{width:"narrow",context:"formatting"});case"eeeeee":return a.day(e,{width:"short",context:"formatting"})||a.day(e,{width:"narrow",context:"formatting"});case"eeee":default:return a.day(e,{width:"wide",context:"formatting"})||a.day(e,{width:"abbreviated",context:"formatting"})||a.day(e,{width:"short",context:"formatting"})||a.day(e,{width:"narrow",context:"formatting"})}}validate(e,n){return n>=0&&n<=6}set(e,n,a,i){return e=gt(e,a,i),e.setHours(0,0,0,0),e}}class Ya extends y{constructor(){super(...arguments);l(this,"priority",90);l(this,"incompatibleTokens",["y","R","u","q","Q","M","L","I","d","D","E","i","e","t","T"])}parse(e,n,a,i){const o=u=>{const d=Math.floor((u-1)/7)*7;return(u+i.weekStartsOn+6)%7+d};switch(n){case"c":case"cc":return F(C(n.length,e),o);case"co":return F(a.ordinalNumber(e,{unit:"day"}),o);case"ccc":return a.day(e,{width:"abbreviated",context:"standalone"})||a.day(e,{width:"short",context:"standalone"})||a.day(e,{width:"narrow",context:"standalone"});case"ccccc":return a.day(e,{width:"narrow",context:"standalone"});case"cccccc":return a.day(e,{width:"short",context:"standalone"})||a.day(e,{width:"narrow",context:"standalone"});case"cccc":default:return a.day(e,{width:"wide",context:"standalone"})||a.day(e,{width:"abbreviated",context:"standalone"})||a.day(e,{width:"short",context:"standalone"})||a.day(e,{width:"narrow",context:"standalone"})}}validate(e,n){return n>=0&&n<=6}set(e,n,a,i){return e=gt(e,a,i),e.setHours(0,0,0,0),e}}function Fa(t,r){const e=h(t),n=sa(e),a=r-n;return De(e,a)}class _a extends y{constructor(){super(...arguments);l(this,"priority",90);l(this,"incompatibleTokens",["y","Y","u","q","Q","M","L","w","d","D","E","e","c","t","T"])}parse(e,n,a){const i=o=>o===0?7:o;switch(n){case"i":case"ii":return C(n.length,e);case"io":return a.ordinalNumber(e,{unit:"day"});case"iii":return F(a.day(e,{width:"abbreviated",context:"formatting"})||a.day(e,{width:"short",context:"formatting"})||a.day(e,{width:"narrow",context:"formatting"}),i);case"iiiii":return F(a.day(e,{width:"narrow",context:"formatting"}),i);case"iiiiii":return F(a.day(e,{width:"short",context:"formatting"})||a.day(e,{width:"narrow",context:"formatting"}),i);case"iiii":default:return F(a.day(e,{width:"wide",context:"formatting"})||a.day(e,{width:"abbreviated",context:"formatting"})||a.day(e,{width:"short",context:"formatting"})||a.day(e,{width:"narrow",context:"formatting"}),i)}}validate(e,n){return n>=1&&n<=7}set(e,n,a){return e=Fa(e,a),e.setHours(0,0,0,0),e}}class Ha extends y{constructor(){super(...arguments);l(this,"priority",80);l(this,"incompatibleTokens",["b","B","H","k","t","T"])}parse(e,n,a){switch(n){case"a":case"aa":case"aaa":return a.dayPeriod(e,{width:"abbreviated",context:"formatting"})||a.dayPeriod(e,{width:"narrow",context:"formatting"});case"aaaaa":return a.dayPeriod(e,{width:"narrow",context:"formatting"});case"aaaa":default:return a.dayPeriod(e,{width:"wide",context:"formatting"})||a.dayPeriod(e,{width:"abbreviated",context:"formatting"})||a.dayPeriod(e,{width:"narrow",context:"formatting"})}}set(e,n,a){return e.setHours(wt(a),0,0,0),e}}class Ra extends y{constructor(){super(...arguments);l(this,"priority",80);l(this,"incompatibleTokens",["a","B","H","k","t","T"])}parse(e,n,a){switch(n){case"b":case"bb":case"bbb":return a.dayPeriod(e,{width:"abbreviated",context:"formatting"})||a.dayPeriod(e,{width:"narrow",context:"formatting"});case"bbbbb":return a.dayPeriod(e,{width:"narrow",context:"formatting"});case"bbbb":default:return a.dayPeriod(e,{width:"wide",context:"formatting"})||a.dayPeriod(e,{width:"abbreviated",context:"formatting"})||a.dayPeriod(e,{width:"narrow",context:"formatting"})}}set(e,n,a){return e.setHours(wt(a),0,0,0),e}}class Ea extends y{constructor(){super(...arguments);l(this,"priority",80);l(this,"incompatibleTokens",["a","b","t","T"])}parse(e,n,a){switch(n){case"B":case"BB":case"BBB":return a.dayPeriod(e,{width:"abbreviated",context:"formatting"})||a.dayPeriod(e,{width:"narrow",context:"formatting"});case"BBBBB":return a.dayPeriod(e,{width:"narrow",context:"formatting"});case"BBBB":default:return a.dayPeriod(e,{width:"wide",context:"formatting"})||a.dayPeriod(e,{width:"abbreviated",context:"formatting"})||a.dayPeriod(e,{width:"narrow",context:"formatting"})}}set(e,n,a){return e.setHours(wt(a),0,0,0),e}}class Va extends y{constructor(){super(...arguments);l(this,"priority",70);l(this,"incompatibleTokens",["H","K","k","t","T"])}parse(e,n,a){switch(n){case"h":return P(Y.hour12h,e);case"ho":return a.ordinalNumber(e,{unit:"hour"});default:return C(n.length,e)}}validate(e,n){return n>=1&&n<=12}set(e,n,a){const i=e.getHours()>=12;return i&&a<12?e.setHours(a+12,0,0,0):!i&&a===12?e.setHours(0,0,0,0):e.setHours(a,0,0,0),e}}class Aa extends y{constructor(){super(...arguments);l(this,"priority",70);l(this,"incompatibleTokens",["a","b","h","K","k","t","T"])}parse(e,n,a){switch(n){case"H":return P(Y.hour23h,e);case"Ho":return a.ordinalNumber(e,{unit:"hour"});default:return C(n.length,e)}}validate(e,n){return n>=0&&n<=23}set(e,n,a){return e.setHours(a,0,0,0),e}}class Sa extends y{constructor(){super(...arguments);l(this,"priority",70);l(this,"incompatibleTokens",["h","H","k","t","T"])}parse(e,n,a){switch(n){case"K":return P(Y.hour11h,e);case"Ko":return a.ordinalNumber(e,{unit:"hour"});default:return C(n.length,e)}}validate(e,n){return n>=0&&n<=11}set(e,n,a){return e.getHours()>=12&&a<12?e.setHours(a+12,0,0,0):e.setHours(a,0,0,0),e}}class za extends y{constructor(){super(...arguments);l(this,"priority",70);l(this,"incompatibleTokens",["a","b","h","H","K","t","T"])}parse(e,n,a){switch(n){case"k":return P(Y.hour24h,e);case"ko":return a.ordinalNumber(e,{unit:"hour"});default:return C(n.length,e)}}validate(e,n){return n>=1&&n<=24}set(e,n,a){const i=a<=24?a%24:a;return e.setHours(i,0,0,0),e}}class qa extends y{constructor(){super(...arguments);l(this,"priority",60);l(this,"incompatibleTokens",["t","T"])}parse(e,n,a){switch(n){case"m":return P(Y.minute,e);case"mo":return a.ordinalNumber(e,{unit:"minute"});default:return C(n.length,e)}}validate(e,n){return n>=0&&n<=59}set(e,n,a){return e.setMinutes(a,0,0),e}}class $a extends y{constructor(){super(...arguments);l(this,"priority",50);l(this,"incompatibleTokens",["t","T"])}parse(e,n,a){switch(n){case"s":return P(Y.second,e);case"so":return a.ordinalNumber(e,{unit:"second"});default:return C(n.length,e)}}validate(e,n){return n>=0&&n<=59}set(e,n,a){return e.setSeconds(a,0),e}}class Ba extends y{constructor(){super(...arguments);l(this,"priority",30);l(this,"incompatibleTokens",["t","T"])}parse(e,n){const a=i=>Math.trunc(i*Math.pow(10,-n.length+3));return F(C(n.length,e),a)}set(e,n,a){return e.setMilliseconds(a),e}}class La extends y{constructor(){super(...arguments);l(this,"priority",10);l(this,"incompatibleTokens",["t","T","x"])}parse(e,n){switch(n){case"X":return j(G.basicOptionalMinutes,e);case"XX":return j(G.basic,e);case"XXXX":return j(G.basicOptionalSeconds,e);case"XXXXX":return j(G.extendedOptionalSeconds,e);case"XXX":default:return j(G.extended,e)}}set(e,n,a){return n.timestampIsSet?e:M(e,e.getTime()-Se(e)-a)}}class Wa extends y{constructor(){super(...arguments);l(this,"priority",10);l(this,"incompatibleTokens",["t","T","X"])}parse(e,n){switch(n){case"x":return j(G.basicOptionalMinutes,e);case"xx":return j(G.basic,e);case"xxxx":return j(G.basicOptionalSeconds,e);case"xxxxx":return j(G.extendedOptionalSeconds,e);case"xxx":default:return j(G.extended,e)}}set(e,n,a){return n.timestampIsSet?e:M(e,e.getTime()-Se(e)-a)}}class Ua extends y{constructor(){super(...arguments);l(this,"priority",40);l(this,"incompatibleTokens","*")}parse(e){return ln(e)}set(e,n,a){return[M(e,a*1e3),{timestampIsSet:!0}]}}class Qa extends y{constructor(){super(...arguments);l(this,"priority",20);l(this,"incompatibleTokens","*")}parse(e){return ln(e)}set(e,n,a){return[M(e,a),{timestampIsSet:!0}]}}const Xa={G:new fa,y:new ma,Y:new ha,R:new wa,u:new ga,Q:new ba,q:new ya,M:new pa,L:new va,w:new Da,I:new ka,d:new Oa,D:new Ia,E:new Pa,e:new Ca,c:new Ya,i:new _a,a:new Ha,b:new Ra,B:new Ea,h:new Va,H:new Aa,K:new Sa,k:new za,m:new qa,s:new $a,S:new Ba,X:new La,x:new Wa,t:new Ua,T:new Qa},Za=/[yYQqMLwIdDecihHKkms]o|(\w)\1*|''|'(''|[^'])+('|$)|./g,Ga=/P+p+|P+|p+|''|'(''|[^'])+('|$)|./g,ja=/^'([^]*?)'?$/,Ka=/''/g,Ja=/\S/,ei=/[a-zA-Z]/;function ti(t,r,e,n){var H,$,B,U,K,T,L,Q,ie,le,S,J,ee,V,R,se,_e,He;const a=un(),i=($=(H=n==null?void 0:n.locale)!=null?H:a.locale)!=null?$:jt,o=(le=(ie=(T=(K=n==null?void 0:n.firstWeekContainsDate)!=null?K:(U=(B=n==null?void 0:n.locale)==null?void 0:B.options)==null?void 0:U.firstWeekContainsDate)!=null?T:a.firstWeekContainsDate)!=null?ie:(Q=(L=a.locale)==null?void 0:L.options)==null?void 0:Q.firstWeekContainsDate)!=null?le:1,u=(He=(_e=(V=(ee=n==null?void 0:n.weekStartsOn)!=null?ee:(J=(S=n==null?void 0:n.locale)==null?void 0:S.options)==null?void 0:J.weekStartsOn)!=null?V:a.weekStartsOn)!=null?_e:(se=(R=a.locale)==null?void 0:R.options)==null?void 0:se.weekStartsOn)!=null?He:0;if(r==="")return t===""?h(e):M(e,NaN);const d={firstWeekContainsDate:o,weekStartsOn:u,locale:i},m=[new da],g=r.match(Ga).map(b=>{const I=b[0];if(I in ut){const W=ut[I];return W(b,i.formatLong)}return b}).join("").match(Za),p=[];for(let b of g){!(n!=null&&n.useAdditionalWeekYearTokens)&&sn(b)&&ct(b,r,t),!(n!=null&&n.useAdditionalDayOfYearTokens)&&an(b)&&ct(b,r,t);const I=b[0],W=Xa[I];if(W){const{incompatibleTokens:te}=W;if(Array.isArray(te)){const Me=p.find(Ne=>te.includes(Ne.token)||Ne.token===I);if(Me)throw new RangeError(`The format string mustn't contain \`${Me.fullToken}\` and \`${b}\` at the same time`)}else if(W.incompatibleTokens==="*"&&p.length>0)throw new RangeError(`The format string mustn't contain \`${b}\` and any other token at the same time`);p.push({token:I,fullToken:b});const de=W.run(t,b,i.match,d);if(!de)return M(e,NaN);m.push(de.setter),t=de.rest}else{if(I.match(ei))throw new RangeError("Format string contains an unescaped latin alphabet character `"+I+"`");if(b==="''"?b="'":I==="'"&&(b=ni(b)),t.indexOf(b)===0)t=t.slice(b.length);else return M(e,NaN)}}if(t.length>0&&Ja.test(t))return M(e,NaN);const _=m.map(b=>b.priority).sort((b,I)=>I-b).filter((b,I,W)=>W.indexOf(b)===I).map(b=>m.filter(I=>I.priority===b).sort((I,W)=>W.subPriority-I.subPriority)).map(b=>b[0]);let v=h(e);if(isNaN(v.getTime()))return M(e,NaN);const O={};for(const b of _){if(!b.validate(v,d))return M(e,NaN);const I=b.set(v,O,d);Array.isArray(I)?(v=I[0],Object.assign(O,I[1])):v=I}return M(e,v)}function ni(t){return t.match(ja)[1].replace(Ka,"'")}function ri(t){const r=h(t);return r.setMinutes(0,0,0),r}function Be(t,r){const e=h(t),n=h(r);return e.getFullYear()===n.getFullYear()&&e.getMonth()===n.getMonth()}function mn(t,r){const e=Ft(t),n=Ft(r);return+e==+n}function ai(t){const r=h(t);return r.setMilliseconds(0),r}function hn(t,r){const e=h(t),n=h(r);return e.getFullYear()===n.getFullYear()}function ii(t,r){const e=h(t),n=e.getFullYear(),a=e.getDate(),i=M(t,0);i.setFullYear(n,r,15),i.setHours(0,0,0,0);const o=ia(i);return e.setMonth(r,Math.min(a,o)),e}function si(t,r){let e=h(t);return isNaN(+e)?M(t,NaN):(r.year!=null&&e.setFullYear(r.year),r.month!=null&&(e=ii(e,r.month)),r.date!=null&&e.setDate(r.date),r.hours!=null&&e.setHours(r.hours),r.minutes!=null&&e.setMinutes(r.minutes),r.seconds!=null&&e.setSeconds(r.seconds),r.milliseconds!=null&&e.setMilliseconds(r.milliseconds),e)}function he(t,r){const e=h(t);return e.setHours(r),e}function je(t,r){const e=h(t);return e.setMinutes(r),e}function Ke(t,r){const e=h(t);return e.setSeconds(r),e}function oi(t,r){const e=h(t);return isNaN(+e)?M(t,NaN):(e.setFullYear(r),e)}const ui={date:zr,month:Be,year:hn,quarter:mn};function ci(t){return(r,e)=>{const n=(t+1)%7;return ir(r,e,{weekStartsOn:n})}}function z(t,r,e,n=0){return(e==="week"?ci(n):ui[e])(t,r)}function Je(t,r,e,n,a,i){return a==="date"?li(t,r,e,n):di(t,r,e,n,i)}function li(t,r,e,n){let a=!1,i=!1,o=!1;Array.isArray(e)&&(e[0]<t&&t<e[1]&&(a=!0),z(e[0],t,"date")&&(i=!0),z(e[1],t,"date")&&(o=!0));const u=e!==null&&(Array.isArray(e)?z(e[0],t,"date")||z(e[1],t,"date"):z(e,t,"date"));return{type:"date",dateObject:{date:on(t),month:Ce(t),year:Fe(t)},inCurrentMonth:Be(t,r),isCurrentDate:z(n,t,"date"),inSpan:a,inSelectedWeek:!1,startOfSpan:i,endOfSpan:o,selected:u,ts:k(t)}}function is(t,r,e){const n=new Date(2e3,t,1).getTime();return ke(n,r,{locale:e})}function ss(t,r,e){const n=new Date(t,1,1).getTime();return ke(n,r,{locale:e})}function os(t,r,e){const n=new Date(2e3,t*3-2,1).getTime();return ke(n,r,{locale:e})}function di(t,r,e,n,a){let i=!1,o=!1,u=!1;Array.isArray(e)&&(e[0]<t&&t<e[1]&&(i=!0),z(e[0],t,"week",a)&&(o=!0),z(e[1],t,"week",a)&&(u=!0));const d=e!==null&&(Array.isArray(e)?z(e[0],t,"week",a)||z(e[1],t,"week",a):z(e,t,"week",a));return{type:"date",dateObject:{date:on(t),month:Ce(t),year:Fe(t)},inCurrentMonth:Be(t,r),isCurrentDate:z(n,t,"date"),inSpan:i,startOfSpan:o,endOfSpan:u,selected:!1,inSelectedWeek:d,ts:k(t)}}function fi(t,r,e,{monthFormat:n}){return{type:"month",monthFormat:n,dateObject:{month:Ce(t),year:Fe(t)},isCurrent:Be(e,t),selected:r!==null&&z(r,t,"month"),ts:k(t)}}function mi(t,r,e,{yearFormat:n}){return{type:"year",yearFormat:n,dateObject:{year:Fe(t)},isCurrent:hn(e,t),selected:r!==null&&z(r,t,"year"),ts:k(t)}}function hi(t,r,e,{quarterFormat:n}){return{type:"quarter",quarterFormat:n,dateObject:{quarter:$r(t),year:Fe(t)},isCurrent:mn(e,t),selected:r!==null&&z(r,t,"quarter"),ts:k(t)}}function us(t,r,e,n,a=!1,i=!1){const o=i?"week":"date",u=Ce(t);let d=k(Lr(t)),m=k(De(d,-1));const g=[];let p=!a;for(;aa(m)!==n||p;)g.unshift(Je(m,t,r,e,o,n)),m=k(De(m,-1)),p=!1;for(;Ce(d)===u;)g.push(Je(d,t,r,e,o,n)),d=k(De(d,1));const _=a?g.length<=28?28:g.length<=35?35:42:42;for(;g.length<_;)g.push(Je(d,t,r,e,o,n)),d=k(De(d,1));return g}function cs(t,r,e,n){const a=[],i=$e(t);for(let o=0;o<12;o++)a.push(fi(k(ft(i,o)),r,e,n));return a}function ls(t,r,e,n){const a=[],i=$e(t);for(let o=0;o<4;o++)a.push(hi(k(Ar(i,o)),r,e,n));return a}function ds(t,r,e,n){const a=n.value,i=[],o=$e(oi(new Date,a[0]));for(let u=0;u<a[1]-a[0];u++)i.push(mi(k(Sr(o,u)),t,r,e));return i}function St(t,r,e,n){const a=ti(t,r,e,n);return mt(a)?ke(a,r,n)===t?a:new Date(Number.NaN):a}function fs(t){if(t===void 0)return;if(typeof t=="number")return t;const[r,e,n]=t.split(":");return{hours:Number(r),minutes:Number(e),seconds:Number(n)}}function ms(t,r){return Array.isArray(t)?t[r==="start"?0:1]:null}function zt(t,r,e){var i;const n=un(),a=bi(t,e.timeZone,(i=e.locale)!=null?i:n.locale);return"formatToParts"in a?wi(a,r):gi(a,r)}function wi(t,r){const e=t.formatToParts(r);for(let n=e.length-1;n>=0;--n)if(e[n].type==="timeZoneName")return e[n].value}function gi(t,r){const e=t.format(r).replace(/\u200E/g,""),n=/ [\w-+ ]+$/.exec(e);return n?n[0].substr(1):""}function bi(t,r,e){return new Intl.DateTimeFormat(e?[e.code,"en-US"]:void 0,{timeZone:r,timeZoneName:t})}function yi(t,r){const e=Ti(r);return"formatToParts"in e?vi(e,t):xi(e,t)}const pi={year:0,month:1,day:2,hour:3,minute:4,second:5};function vi(t,r){try{const e=t.formatToParts(r),n=[];for(let a=0;a<e.length;a++){const i=pi[e[a].type];i!==void 0&&(n[i]=parseInt(e[a].value,10))}return n}catch(e){if(e instanceof RangeError)return[NaN];throw e}}function xi(t,r){const e=t.format(r),n=/(\d+)\/(\d+)\/(\d+),? (\d+):(\d+):(\d+)/.exec(e);return[parseInt(n[3],10),parseInt(n[1],10),parseInt(n[2],10),parseInt(n[4],10),parseInt(n[5],10),parseInt(n[6],10)]}const et={},qt=new Intl.DateTimeFormat("en-US",{hourCycle:"h23",timeZone:"America/New_York",year:"numeric",month:"2-digit",day:"2-digit",hour:"2-digit",minute:"2-digit",second:"2-digit"}).format(new Date("2014-06-25T04:00:00.123Z")),Di=qt==="06/25/2014, 00:00:00"||qt==="‎06‎/‎25‎/‎2014‎ ‎00‎:‎00‎:‎00";function Ti(t){return et[t]||(et[t]=Di?new Intl.DateTimeFormat("en-US",{hourCycle:"h23",timeZone:t,year:"numeric",month:"numeric",day:"2-digit",hour:"2-digit",minute:"2-digit",second:"2-digit"}):new Intl.DateTimeFormat("en-US",{hour12:!1,timeZone:t,year:"numeric",month:"numeric",day:"2-digit",hour:"2-digit",minute:"2-digit",second:"2-digit"})),et[t]}function wn(t,r,e,n,a,i,o){const u=new Date(0);return u.setUTCFullYear(t,r,e),u.setUTCHours(n,a,i,o),u}const $t=36e5,ki=6e4,tt={timezoneZ:/^(Z)$/,timezoneHH:/^([+-]\d{2})$/,timezoneHHMM:/^([+-])(\d{2}):?(\d{2})$/};function bt(t,r,e){if(!t)return 0;let n=tt.timezoneZ.exec(t);if(n)return 0;let a,i;if(n=tt.timezoneHH.exec(t),n)return a=parseInt(n[1],10),Bt(a)?-(a*$t):NaN;if(n=tt.timezoneHHMM.exec(t),n){a=parseInt(n[2],10);const o=parseInt(n[3],10);return Bt(a,o)?(i=Math.abs(a)*$t+o*ki,n[1]==="+"?-i:i):NaN}if(Oi(t)){r=new Date(r||Date.now());const o=e?r:Mi(r),u=lt(o,t);return-(e?u:Ni(r,u,t))}return NaN}function Mi(t){return wn(t.getFullYear(),t.getMonth(),t.getDate(),t.getHours(),t.getMinutes(),t.getSeconds(),t.getMilliseconds())}function lt(t,r){const e=yi(t,r),n=wn(e[0],e[1]-1,e[2],e[3]%24,e[4],e[5],0).getTime();let a=t.getTime();const i=a%1e3;return a-=i>=0?i:1e3+i,n-a}function Ni(t,r,e){let a=t.getTime()-r;const i=lt(new Date(a),e);if(r===i)return r;a-=i-r;const o=lt(new Date(a),e);return i===o?i:Math.max(i,o)}function Bt(t,r){return-23<=t&&t<=23&&(r==null||0<=r&&r<=59)}const Lt={};function Oi(t){if(Lt[t])return!0;try{return new Intl.DateTimeFormat(void 0,{timeZone:t}),Lt[t]=!0,!0}catch(r){return!1}}const Ii=60*1e3,Pi={X:function(t,r,e){const n=nt(e.timeZone,t);if(n===0)return"Z";switch(r){case"X":return Wt(n);case"XXXX":case"XX":return xe(n);case"XXXXX":case"XXX":default:return xe(n,":")}},x:function(t,r,e){const n=nt(e.timeZone,t);switch(r){case"x":return Wt(n);case"xxxx":case"xx":return xe(n);case"xxxxx":case"xxx":default:return xe(n,":")}},O:function(t,r,e){const n=nt(e.timeZone,t);switch(r){case"O":case"OO":case"OOO":return"GMT"+Ci(n,":");case"OOOO":default:return"GMT"+xe(n,":")}},z:function(t,r,e){switch(r){case"z":case"zz":case"zzz":return zt("short",t,e);case"zzzz":default:return zt("long",t,e)}}};function nt(t,r){var n;const e=t?bt(t,r,!0)/Ii:(n=r==null?void 0:r.getTimezoneOffset())!=null?n:0;if(Number.isNaN(e))throw new RangeError("Invalid time zone specified: "+t);return e}function qe(t,r){const e=t<0?"-":"";let n=Math.abs(t).toString();for(;n.length<r;)n="0"+n;return e+n}function xe(t,r=""){const e=t>0?"-":"+",n=Math.abs(t),a=qe(Math.floor(n/60),2),i=qe(Math.floor(n%60),2);return e+a+r+i}function Wt(t,r){return t%60===0?(t>0?"-":"+")+qe(Math.abs(t)/60,2):xe(t,r)}function Ci(t,r=""){const e=t>0?"-":"+",n=Math.abs(t),a=Math.floor(n/60),i=n%60;return i===0?e+String(a):e+String(a)+r+qe(i,2)}function Ut(t){const r=new Date(Date.UTC(t.getFullYear(),t.getMonth(),t.getDate(),t.getHours(),t.getMinutes(),t.getSeconds(),t.getMilliseconds()));return r.setUTCFullYear(t.getFullYear()),+t-+r}const Yi=/(Z|[+-]\d{2}(?::?\d{2})?| UTC| [a-zA-Z]+\/[a-zA-Z_]+(?:\/[a-zA-Z_]+)?)$/,rt=36e5,Qt=6e4,Fi=2,q={dateTimePattern:/^([0-9W+-]+)(T| )(.*)/,datePattern:/^([0-9W+-]+)(.*)/,YY:/^(\d{2})$/,YYY:[/^([+-]\d{2})$/,/^([+-]\d{3})$/,/^([+-]\d{4})$/],YYYY:/^(\d{4})/,YYYYY:[/^([+-]\d{4})/,/^([+-]\d{5})/,/^([+-]\d{6})/],MM:/^-(\d{2})$/,DDD:/^-?(\d{3})$/,MMDD:/^-?(\d{2})-?(\d{2})$/,Www:/^-?W(\d{2})$/,WwwD:/^-?W(\d{2})-?(\d{1})$/,HH:/^(\d{2}([.,]\d*)?)$/,HHMM:/^(\d{2}):?(\d{2}([.,]\d*)?)$/,HHMMSS:/^(\d{2}):?(\d{2}):?(\d{2}([.,]\d*)?)$/,timeZone:Yi};function gn(t,r={}){if(arguments.length<1)throw new TypeError("1 argument required, but only "+arguments.length+" present");if(t===null)return new Date(NaN);const e=r.additionalDigits==null?Fi:Number(r.additionalDigits);if(e!==2&&e!==1&&e!==0)throw new RangeError("additionalDigits must be 0, 1 or 2");if(t instanceof Date||typeof t=="object"&&Object.prototype.toString.call(t)==="[object Date]")return new Date(t.getTime());if(typeof t=="number"||Object.prototype.toString.call(t)==="[object Number]")return new Date(t);if(Object.prototype.toString.call(t)!=="[object String]")return new Date(NaN);const n=_i(t),{year:a,restDateString:i}=Hi(n.date,e),o=Ri(i,a);if(o===null||isNaN(o.getTime()))return new Date(NaN);if(o){const u=o.getTime();let d=0,m;if(n.time&&(d=Ei(n.time),d===null||isNaN(d)))return new Date(NaN);if(n.timeZone||r.timeZone){if(m=bt(n.timeZone||r.timeZone,new Date(u+d)),isNaN(m))return new Date(NaN)}else m=Ut(new Date(u+d)),m=Ut(new Date(u+d+m));return new Date(u+d+m)}else return new Date(NaN)}function _i(t){const r={};let e=q.dateTimePattern.exec(t),n;if(e?(r.date=e[1],n=e[3]):(e=q.datePattern.exec(t),e?(r.date=e[1],n=e[2]):(r.date=null,n=t)),n){const a=q.timeZone.exec(n);a?(r.time=n.replace(a[1],""),r.timeZone=a[1].trim()):r.time=n}return r}function Hi(t,r){if(t){const e=q.YYY[r],n=q.YYYYY[r];let a=q.YYYY.exec(t)||n.exec(t);if(a){const i=a[1];return{year:parseInt(i,10),restDateString:t.slice(i.length)}}if(a=q.YY.exec(t)||e.exec(t),a){const i=a[1];return{year:parseInt(i,10)*100,restDateString:t.slice(i.length)}}}return{year:null}}function Ri(t,r){if(r===null)return null;let e,n,a;if(!t||!t.length)return e=new Date(0),e.setUTCFullYear(r),e;let i=q.MM.exec(t);if(i)return e=new Date(0),n=parseInt(i[1],10)-1,Zt(r,n)?(e.setUTCFullYear(r,n),e):new Date(NaN);if(i=q.DDD.exec(t),i){e=new Date(0);const o=parseInt(i[1],10);return Si(r,o)?(e.setUTCFullYear(r,0,o),e):new Date(NaN)}if(i=q.MMDD.exec(t),i){e=new Date(0),n=parseInt(i[1],10)-1;const o=parseInt(i[2],10);return Zt(r,n,o)?(e.setUTCFullYear(r,n,o),e):new Date(NaN)}if(i=q.Www.exec(t),i)return a=parseInt(i[1],10)-1,Gt(a)?Xt(r,a):new Date(NaN);if(i=q.WwwD.exec(t),i){a=parseInt(i[1],10)-1;const o=parseInt(i[2],10)-1;return Gt(a,o)?Xt(r,a,o):new Date(NaN)}return null}function Ei(t){let r,e,n=q.HH.exec(t);if(n)return r=parseFloat(n[1].replace(",",".")),at(r)?r%24*rt:NaN;if(n=q.HHMM.exec(t),n)return r=parseInt(n[1],10),e=parseFloat(n[2].replace(",",".")),at(r,e)?r%24*rt+e*Qt:NaN;if(n=q.HHMMSS.exec(t),n){r=parseInt(n[1],10),e=parseInt(n[2],10);const a=parseFloat(n[3].replace(",","."));return at(r,e,a)?r%24*rt+e*Qt+a*1e3:NaN}return null}function Xt(t,r,e){r=r||0,e=e||0;const n=new Date(0);n.setUTCFullYear(t,0,4);const a=n.getUTCDay()||7,i=r*7+e+1-a;return n.setUTCDate(n.getUTCDate()+i),n}const Vi=[31,28,31,30,31,30,31,31,30,31,30,31],Ai=[31,29,31,30,31,30,31,31,30,31,30,31];function bn(t){return t%400===0||t%4===0&&t%100!==0}function Zt(t,r,e){if(r<0||r>11)return!1;if(e!=null){if(e<1)return!1;const n=bn(t);if(n&&e>Ai[r]||!n&&e>Vi[r])return!1}return!0}function Si(t,r){if(r<1)return!1;const e=bn(t);return!(e&&r>366||!e&&r>365)}function Gt(t,r){return!(t<0||t>52||r!=null&&(r<0||r>6))}function at(t,r,e){return!(t<0||t>=25||r!=null&&(r<0||r>=60)||e!=null&&(e<0||e>=60))}const zi=/([xXOz]+)|''|'(''|[^'])+('|$)/g;function qi(t,r,e={}){r=String(r);const n=r.match(zi);if(n){const a=gn(e.originalDate||t,e);r=n.reduce(function(i,o){if(o[0]==="'")return i;const u=i.indexOf(o),d=i[u-1]==="'",m=i.replace(o,"'"+Pi[o[0]](a,o,e)+"'");return d?m.substring(0,u-1)+m.substring(u+1):m},r)}return ke(t,r,e)}function $i(t,r,e){t=gn(t,e);const n=bt(r,t,!0),a=new Date(t.getTime()-n),i=new Date(0);return i.setFullYear(a.getUTCFullYear(),a.getUTCMonth(),a.getUTCDate()),i.setHours(a.getUTCHours(),a.getUTCMinutes(),a.getUTCSeconds(),a.getUTCMilliseconds()),i}function Bi(t,r,e,n){return n=Pt(It({},n),{timeZone:r,originalDate:t}),qi($i(t,r,{timeZone:n.timeZone}),e,n)}const yn=sr("n-time-picker"),Ee=dt({name:"TimePickerPanelCol",props:{clsPrefix:{type:String,required:!0},data:{type:Array,required:!0},activeValue:{type:[Number,String],default:null},onItemClick:Function},render(){const{activeValue:t,onItemClick:r,clsPrefix:e}=this;return this.data.map(n=>{const{label:a,disabled:i,value:o}=n,u=t===o;return w("div",{key:a,"data-active":u?"":null,class:[`${e}-time-picker-col__item`,u&&`${e}-time-picker-col__item--active`,i&&`${e}-time-picker-col__item--disabled`],onClick:r&&!i?()=>{r(o)}:void 0},a)})}}),Ie={amHours:["00","01","02","03","04","05","06","07","08","09","10","11"],pmHours:["12","01","02","03","04","05","06","07","08","09","10","11"],hours:["00","01","02","03","04","05","06","07","08","09","10","11","12","13","14","15","16","17","18","19","20","21","22","23"],minutes:["00","01","02","03","04","05","06","07","08","09","10","11","12","13","14","15","16","17","18","19","20","21","22","23","24","25","26","27","28","29","30","31","32","33","34","35","36","37","38","39","40","41","42","43","44","45","46","47","48","49","50","51","52","53","54","55","56","57","58","59"],seconds:["00","01","02","03","04","05","06","07","08","09","10","11","12","13","14","15","16","17","18","19","20","21","22","23","24","25","26","27","28","29","30","31","32","33","34","35","36","37","38","39","40","41","42","43","44","45","46","47","48","49","50","51","52","53","54","55","56","57","58","59"],period:["AM","PM"]};function it(t){return`00${t}`.slice(-2)}function Pe(t,r,e){return Array.isArray(r)?(e==="am"?r.filter(n=>n<12):e==="pm"?r.filter(n=>n>=12).map(n=>n===12?12:n-12):r).map(n=>it(n)):typeof r=="number"?e==="am"?t.filter(n=>{const a=Number(n);return a<12&&a%r===0}):e==="pm"?t.filter(n=>{const a=Number(n);return a>=12&&a%r===0}).map(n=>{const a=Number(n);return it(a===12?12:a-12)}):t.filter(n=>Number(n)%r===0):e==="am"?t.filter(n=>Number(n)<12):e==="pm"?t.map(n=>Number(n)).filter(n=>Number(n)>=12).map(n=>it(n===12?12:n-12)):t}function Ve(t,r,e){return e?typeof e=="number"?t%e===0:e.includes(t):!0}function Li(t,r,e){const n=Pe(Ie[r],e).map(Number);let a,i;for(let o=0;o<n.length;++o){const u=n[o];if(u===t)return u;if(u>t){i=u;break}a=u}return a===void 0?(i||or("time-picker","Please set 'hours' or 'minutes' or 'seconds' props"),i):i===void 0||i-t>t-a?a:i}function Wi(t){return ve(t)<12?"am":"pm"}const Ui={actions:{type:Array,default:()=>["now","confirm"]},showHour:{type:Boolean,default:!0},showMinute:{type:Boolean,default:!0},showSecond:{type:Boolean,default:!0},showPeriod:{type:Boolean,default:!0},isHourInvalid:Boolean,isMinuteInvalid:Boolean,isSecondInvalid:Boolean,isAmPmInvalid:Boolean,isValueInvalid:Boolean,hourValue:{type:Number,default:null},minuteValue:{type:Number,default:null},secondValue:{type:Number,default:null},amPmValue:{type:String,default:null},isHourDisabled:Function,isMinuteDisabled:Function,isSecondDisabled:Function,onHourClick:{type:Function,required:!0},onMinuteClick:{type:Function,required:!0},onSecondClick:{type:Function,required:!0},onAmPmClick:{type:Function,required:!0},onNowClick:Function,clearText:String,nowText:String,confirmText:String,transitionDisabled:Boolean,onClearClick:Function,onConfirmClick:Function,onFocusin:Function,onFocusout:Function,onFocusDetectorFocus:Function,onKeydown:Function,hours:[Number,Array],minutes:[Number,Array],seconds:[Number,Array],use12Hours:Boolean},Qi=dt({name:"TimePickerPanel",props:Ui,setup(t){const{mergedThemeRef:r,mergedClsPrefixRef:e}=yr(yn),n=N(()=>{const{isHourDisabled:u,hours:d,use12Hours:m,amPmValue:g}=t;if(m){const p=g!=null?g:Wi(Date.now());return Pe(Ie.hours,d,p).map(_=>{const v=Number(_),O=p==="pm"&&v!==12?v+12:v;return{label:_,value:O,disabled:u?u(O):!1}})}else return Pe(Ie.hours,d).map(p=>({label:p,value:Number(p),disabled:u?u(Number(p)):!1}))}),a=N(()=>{const{isMinuteDisabled:u,minutes:d}=t;return Pe(Ie.minutes,d).map(m=>({label:m,value:Number(m),disabled:u?u(Number(m),t.hourValue):!1}))}),i=N(()=>{const{isSecondDisabled:u,seconds:d}=t;return Pe(Ie.seconds,d).map(m=>({label:m,value:Number(m),disabled:u?u(Number(m),t.minuteValue,t.hourValue):!1}))}),o=N(()=>{const{isHourDisabled:u}=t;let d=!0,m=!0;for(let g=0;g<12;++g)if(!(u!=null&&u(g))){d=!1;break}for(let g=12;g<24;++g)if(!(u!=null&&u(g))){m=!1;break}return[{label:"AM",value:"am",disabled:d},{label:"PM",value:"pm",disabled:m}]});return{mergedTheme:r,mergedClsPrefix:e,hours:n,minutes:a,seconds:i,amPm:o,hourScrollRef:X(null),minuteScrollRef:X(null),secondScrollRef:X(null),amPmScrollRef:X(null)}},render(){var t,r,e,n;const{mergedClsPrefix:a,mergedTheme:i}=this;return w("div",{tabindex:0,class:`${a}-time-picker-panel`,onFocusin:this.onFocusin,onFocusout:this.onFocusout,onKeydown:this.onKeydown},w("div",{class:`${a}-time-picker-cols`},this.showHour?w("div",{class:[`${a}-time-picker-col`,this.isHourInvalid&&`${a}-time-picker-col--invalid`,this.transitionDisabled&&`${a}-time-picker-col--transition-disabled`]},w(Re,{ref:"hourScrollRef",theme:i.peers.Scrollbar,themeOverrides:i.peerOverrides.Scrollbar},{default:()=>[w(Ee,{clsPrefix:a,data:this.hours,activeValue:this.hourValue,onItemClick:this.onHourClick}),w("div",{class:`${a}-time-picker-col__padding`})]})):null,this.showMinute?w("div",{class:[`${a}-time-picker-col`,this.transitionDisabled&&`${a}-time-picker-col--transition-disabled`,this.isMinuteInvalid&&`${a}-time-picker-col--invalid`]},w(Re,{ref:"minuteScrollRef",theme:i.peers.Scrollbar,themeOverrides:i.peerOverrides.Scrollbar},{default:()=>[w(Ee,{clsPrefix:a,data:this.minutes,activeValue:this.minuteValue,onItemClick:this.onMinuteClick}),w("div",{class:`${a}-time-picker-col__padding`})]})):null,this.showSecond?w("div",{class:[`${a}-time-picker-col`,this.isSecondInvalid&&`${a}-time-picker-col--invalid`,this.transitionDisabled&&`${a}-time-picker-col--transition-disabled`]},w(Re,{ref:"secondScrollRef",theme:i.peers.Scrollbar,themeOverrides:i.peerOverrides.Scrollbar},{default:()=>[w(Ee,{clsPrefix:a,data:this.seconds,activeValue:this.secondValue,onItemClick:this.onSecondClick}),w("div",{class:`${a}-time-picker-col__padding`})]})):null,this.use12Hours?w("div",{class:[`${a}-time-picker-col`,this.isAmPmInvalid&&`${a}-time-picker-col--invalid`,this.transitionDisabled&&`${a}-time-picker-col--transition-disabled`]},w(Re,{ref:"amPmScrollRef",theme:i.peers.Scrollbar,themeOverrides:i.peerOverrides.Scrollbar},{default:()=>[w(Ee,{clsPrefix:a,data:this.amPm,activeValue:this.amPmValue,onItemClick:this.onAmPmClick}),w("div",{class:`${a}-time-picker-col__padding`})]})):null),!((t=this.actions)===null||t===void 0)&&t.length?w("div",{class:`${a}-time-picker-actions`},!((r=this.actions)===null||r===void 0)&&r.includes("clear")?w(Xe,{theme:i.peers.Button,themeOverrides:i.peerOverrides.Button,size:"tiny",onClick:this.onClearClick},{default:()=>this.clearText}):null,!((e=this.actions)===null||e===void 0)&&e.includes("now")?w(Xe,{size:"tiny",theme:i.peers.Button,themeOverrides:i.peerOverrides.Button,onClick:this.onNowClick},{default:()=>this.nowText}):null,!((n=this.actions)===null||n===void 0)&&n.includes("confirm")?w(Xe,{size:"tiny",type:"primary",class:`${a}-time-picker-actions__confirm`,theme:i.peers.Button,themeOverrides:i.peerOverrides.Button,disabled:this.isValueInvalid,onClick:this.onConfirmClick},{default:()=>this.confirmText}):null):null,w(Dr,{onFocus:this.onFocusDetectorFocus}))}}),Xi=fe([me("time-picker",`
 z-index: auto;
 position: relative;
 `,[me("time-picker-icon",`
 color: var(--n-icon-color-override);
 transition: color .3s var(--n-bezier);
 `),ye("disabled",[me("time-picker-icon",`
 color: var(--n-icon-color-disabled-override);
 `)])]),me("time-picker-panel",`
 transition:
 box-shadow .3s var(--n-bezier),
 background-color .3s var(--n-bezier);
 outline: none;
 font-size: var(--n-item-font-size);
 border-radius: var(--n-border-radius);
 margin: 4px 0;
 min-width: 104px;
 overflow: hidden;
 background-color: var(--n-panel-color);
 box-shadow: var(--n-panel-box-shadow);
 `,[ur(),me("time-picker-actions",`
 padding: var(--n-panel-action-padding);
 align-items: center;
 display: flex;
 justify-content: space-evenly;
 `),me("time-picker-cols",`
 height: calc(var(--n-item-height) * 6);
 display: flex;
 position: relative;
 transition: border-color .3s var(--n-bezier);
 border-bottom: 1px solid var(--n-panel-divider-color);
 `),me("time-picker-col",`
 flex-grow: 1;
 min-width: var(--n-item-width);
 height: calc(var(--n-item-height) * 6);
 flex-direction: column;
 transition: box-shadow .3s var(--n-bezier);
 `,[ye("transition-disabled",[Oe("item","transition: none;",[fe("&::before","transition: none;")])]),Oe("padding",`
 height: calc(var(--n-item-height) * 5);
 `),fe("&:first-child","min-width: calc(var(--n-item-width) + 4px);",[Oe("item",[fe("&::before","left: 4px;")])]),Oe("item",`
 cursor: pointer;
 height: var(--n-item-height);
 display: flex;
 align-items: center;
 justify-content: center;
 transition: 
 color .3s var(--n-bezier),
 background-color .3s var(--n-bezier),
 opacity .3s var(--n-bezier),
 text-decoration-color .3s var(--n-bezier);
 background: #0000;
 text-decoration-color: #0000;
 color: var(--n-item-text-color);
 z-index: 0;
 box-sizing: border-box;
 padding-top: 4px;
 position: relative;
 `,[fe("&::before",`
 content: "";
 transition: background-color .3s var(--n-bezier);
 z-index: -1;
 position: absolute;
 left: 0;
 right: 4px;
 top: 4px;
 bottom: 0;
 border-radius: var(--n-item-border-radius);
 `),cr("disabled",[fe("&:hover::before",`
 background-color: var(--n-item-color-hover);
 `)]),ye("active",`
 color: var(--n-item-text-color-active);
 `,[fe("&::before",`
 background-color: var(--n-item-color-hover);
 `)]),ye("disabled",`
 opacity: var(--n-item-opacity-disabled);
 cursor: not-allowed;
 `)]),ye("invalid",[Oe("item",[ye("active",`
 text-decoration: line-through;
 text-decoration-color: var(--n-item-text-color-active);
 `)])])])])]);function st(t,r){return t===void 0?!0:Array.isArray(t)?t.every(e=>e>=0&&e<=r):t>=0&&t<=r}const pn=Object.assign(Object.assign({},Kt.props),{to:ot.propTo,bordered:{type:Boolean,default:void 0},actions:Array,defaultValue:{type:Number,default:null},defaultFormattedValue:String,placeholder:String,placement:{type:String,default:"bottom-start"},value:Number,format:{type:String,default:"HH:mm:ss"},valueFormat:String,formattedValue:String,isHourDisabled:Function,size:String,isMinuteDisabled:Function,isSecondDisabled:Function,inputReadonly:Boolean,clearable:Boolean,status:String,"onUpdate:value":[Function,Array],onUpdateValue:[Function,Array],"onUpdate:show":[Function,Array],onUpdateShow:[Function,Array],onUpdateFormattedValue:[Function,Array],"onUpdate:formattedValue":[Function,Array],onBlur:[Function,Array],onConfirm:[Function,Array],onClear:Function,onFocus:[Function,Array],timeZone:String,showIcon:{type:Boolean,default:!0},disabled:{type:Boolean,default:void 0},show:{type:Boolean,default:void 0},hours:{type:[Number,Array],validator:t=>st(t,23)},minutes:{type:[Number,Array],validator:t=>st(t,59)},seconds:{type:[Number,Array],validator:t=>st(t,59)},use12Hours:Boolean,stateful:{type:Boolean,default:!0},onChange:[Function,Array]}),Zi=dt({name:"TimePicker",props:pn,setup(t){const{mergedBorderedRef:r,mergedClsPrefixRef:e,namespaceRef:n,inlineThemeDisabled:a}=mr(t),{localeRef:i,dateLocaleRef:o}=Ir("TimePicker"),u=hr(t),{mergedSizeRef:d,mergedDisabledRef:m,mergedStatusRef:g}=u,p=Kt("TimePicker","-time-picker",Xi,br,t,e),_=Pr(),v=X(null),O=X(null),H=N(()=>({locale:o.value.locale}));function $(s){return s===null?null:St(s,t.valueFormat||t.format,new Date,H.value).getTime()}const{defaultValue:B,defaultFormattedValue:U}=t,K=X(U!==void 0?$(U):B),T=N(()=>{const{formattedValue:s}=t;if(s!==void 0)return $(s);const{value:c}=t;return c!==void 0?c:K.value}),L=N(()=>{const{timeZone:s}=t;return s?(c,f,x)=>Bi(c,s,f,x):(c,f,x)=>ke(c,f,x)}),Q=X("");Ze(()=>t.timeZone,()=>{const s=T.value;Q.value=s===null?"":L.value(s,t.format,H.value)},{immediate:!0});const ie=X(!1),le=vr(t,"show"),S=Cr(le,ie),J=X(T.value),ee=X(!1),V=N(()=>i.value.clear),R=N(()=>i.value.now),se=N(()=>t.placeholder!==void 0?t.placeholder:i.value.placeholder),_e=N(()=>i.value.negativeText),He=N(()=>i.value.positiveText),b=N(()=>/H|h|K|k/.test(t.format)),I=N(()=>t.format.includes("m")),W=N(()=>t.format.includes("s")),te=N(()=>{const{value:s}=T;return s===null?null:Number(L.value(s,"HH",H.value))}),de=N(()=>{const{value:s}=T;return s===null?null:Number(L.value(s,"mm",H.value))}),Me=N(()=>{const{value:s}=T;return s===null?null:Number(L.value(s,"ss",H.value))}),Ne=N(()=>{const{isHourDisabled:s}=t;return te.value===null?!1:Ve(te.value,"hours",t.hours)?s?s(te.value):!1:!0}),yt=N(()=>{const{value:s}=de,{value:c}=te;if(s===null||c===null)return!1;if(!Ve(s,"minutes",t.minutes))return!0;const{isMinuteDisabled:f}=t;return f?f(s,c):!1}),pt=N(()=>{const{value:s}=de,{value:c}=te,{value:f}=Me;if(f===null||s===null||c===null)return!1;if(!Ve(f,"seconds",t.seconds))return!0;const{isSecondDisabled:x}=t;return x?x(f,s,c):!1}),vt=N(()=>Ne.value||yt.value||pt.value),vn=N(()=>t.format.length+4),xn=N(()=>{const{value:s}=T;return s===null?null:ve(s)<12?"am":"pm"});function Dn(s,c){const{onUpdateFormattedValue:f,"onUpdate:formattedValue":x}=t;f&&Z(f,s,c),x&&Z(x,s,c)}function xt(s){return s===null?null:L.value(s,t.valueFormat||t.format)}function E(s){const{onUpdateValue:c,"onUpdate:value":f,onChange:x}=t,{nTriggerFormChange:re,nTriggerFormInput:ae}=u,A=xt(s);c&&Z(c,s,A),f&&Z(f,s,A),x&&Z(x,s,A),Dn(A,s),K.value=s,re(),ae()}function Tn(s){const{onFocus:c}=t,{nTriggerFormFocus:f}=u;c&&Z(c,s),f()}function Le(s){const{onBlur:c}=t,{nTriggerFormBlur:f}=u;c&&Z(c,s),f()}function kn(){const{onConfirm:s}=t;s&&Z(s,T.value,xt(T.value))}function Mn(s){var c;s.stopPropagation(),E(null),oe(null),(c=t.onClear)===null||c===void 0||c.call(t)}function Nn(){ne({returnFocus:!0})}function On(){E(null),oe(null),ne({returnFocus:!0})}function In(s){s.key==="Escape"&&S.value&&Yt(s)}function Pn(s){var c;switch(s.key){case"Escape":S.value&&(Yt(s),ne({returnFocus:!0}));break;case"Tab":_.shift&&s.target===((c=O.value)===null||c===void 0?void 0:c.$el)&&(s.preventDefault(),ne({returnFocus:!0}));break}}function Cn(){ee.value=!0,Ge(()=>{ee.value=!1})}function Yn(s){m.value||Tr(s,"clear")||S.value||Tt()}function Fn(s){typeof s!="string"&&(T.value===null?E(k(he(ri(new Date),s))):E(k(he(T.value,s))))}function _n(s){typeof s!="string"&&(T.value===null?E(k(je(Br(new Date),s))):E(k(je(T.value,s))))}function Hn(s){typeof s!="string"&&(T.value===null?E(k(Ke(ai(new Date),s))):E(k(Ke(T.value,s))))}function Rn(s){const{value:c}=T;if(c===null){const f=new Date,x=ve(f);s==="pm"&&x<12?E(k(he(f,x+12))):s==="am"&&x>=12&&E(k(he(f,x-12))),E(k(f))}else{const f=ve(c);s==="pm"&&f<12?E(k(he(c,f+12))):s==="am"&&f>=12&&E(k(he(c,f-12)))}}function oe(s){s===void 0&&(s=T.value),s===null?Q.value="":Q.value=L.value(s,t.format,H.value)}function En(s){Ue(s)||Tn(s)}function Vn(s){var c;if(!Ue(s))if(S.value){const f=(c=O.value)===null||c===void 0?void 0:c.$el;f!=null&&f.contains(s.relatedTarget)||(oe(),Le(s),ne({returnFocus:!1}))}else oe(),Le(s)}function An(){m.value||S.value||Tt()}function Sn(){m.value||(oe(),ne({returnFocus:!1}))}function Dt(){if(!O.value)return;const{hourScrollRef:s,minuteScrollRef:c,secondScrollRef:f,amPmScrollRef:x}=O.value;[s,c,f,x].forEach(re=>{var ae;if(!re)return;const A=(ae=re.contentRef)===null||ae===void 0?void 0:ae.querySelector("[data-active]");A&&re.scrollTo({top:A.offsetTop})})}function We(s){ie.value=s;const{onUpdateShow:c,"onUpdate:show":f}=t;c&&Z(c,s),f&&Z(f,s)}function Ue(s){var c,f,x;return!!(!((f=(c=v.value)===null||c===void 0?void 0:c.wrapperElRef)===null||f===void 0)&&f.contains(s.relatedTarget)||!((x=O.value)===null||x===void 0)&&x.$el.contains(s.relatedTarget))}function Tt(){J.value=T.value,We(!0),Ge(Dt)}function zn(s){var c,f;S.value&&!(!((f=(c=v.value)===null||c===void 0?void 0:c.wrapperElRef)===null||f===void 0)&&f.contains(gr(s)))&&ne({returnFocus:!1})}function ne({returnFocus:s}){var c;S.value&&(We(!1),s&&((c=v.value)===null||c===void 0||c.focus()))}function qn(s){if(s===""){E(null);return}const c=St(s,t.format,new Date,H.value);if(Q.value=s,mt(c)){const{value:f}=T;if(f!==null){const x=si(f,{hours:ve(c),minutes:Vt(c),seconds:At(c),milliseconds:oa(c)});E(k(x))}else E(k(c))}}function $n(){E(J.value),We(!1)}function Bn(){const s=new Date,c={hours:ve,minutes:Vt,seconds:At},[f,x,re]=["hours","minutes","seconds"].map(A=>!t[A]||Ve(c[A](s),A,t[A])?c[A](s):Li(c[A](s),A,t[A])),ae=Ke(je(he(T.value?T.value:k(s),f),x),re);E(k(ae))}function Ln(){oe(),kn(),ne({returnFocus:!0})}function Wn(s){Ue(s)||(oe(),Le(s),ne({returnFocus:!1}))}Ze(T,s=>{oe(s),Cn(),Ge(Dt)}),Ze(S,()=>{vt.value&&E(J.value)}),xr(yn,{mergedThemeRef:p,mergedClsPrefixRef:e});const kt={focus:()=>{var s;(s=v.value)===null||s===void 0||s.focus()},blur:()=>{var s;(s=v.value)===null||s===void 0||s.blur()}},Mt=N(()=>{const{common:{cubicBezierEaseInOut:s},self:{iconColor:c,iconColorDisabled:f}}=p.value;return{"--n-icon-color-override":c,"--n-icon-color-disabled-override":f,"--n-bezier":s}}),ge=a?Ct("time-picker-trigger",void 0,Mt,t):void 0,Nt=N(()=>{const{self:{panelColor:s,itemTextColor:c,itemTextColorActive:f,itemColorHover:x,panelDividerColor:re,panelBoxShadow:ae,itemOpacityDisabled:A,borderRadius:Un,itemFontSize:Qn,itemWidth:Xn,itemHeight:Zn,panelActionPadding:Gn,itemBorderRadius:jn},common:{cubicBezierEaseInOut:Kn}}=p.value;return{"--n-bezier":Kn,"--n-border-radius":Un,"--n-item-color-hover":x,"--n-item-font-size":Qn,"--n-item-height":Zn,"--n-item-opacity-disabled":A,"--n-item-text-color":c,"--n-item-text-color-active":f,"--n-item-width":Xn,"--n-panel-action-padding":Gn,"--n-panel-box-shadow":ae,"--n-panel-color":s,"--n-panel-divider-color":re,"--n-item-border-radius":jn}}),be=a?Ct("time-picker",void 0,Nt,t):void 0;return{focus:kt.focus,blur:kt.blur,mergedStatus:g,mergedBordered:r,mergedClsPrefix:e,namespace:n,uncontrolledValue:K,mergedValue:T,isMounted:wr(),inputInstRef:v,panelInstRef:O,adjustedTo:ot(t),mergedShow:S,localizedClear:V,localizedNow:R,localizedPlaceholder:se,localizedNegativeText:_e,localizedPositiveText:He,hourInFormat:b,minuteInFormat:I,secondInFormat:W,mergedAttrSize:vn,displayTimeString:Q,mergedSize:d,mergedDisabled:m,isValueInvalid:vt,isHourInvalid:Ne,isMinuteInvalid:yt,isSecondInvalid:pt,transitionDisabled:ee,hourValue:te,minuteValue:de,secondValue:Me,amPmValue:xn,handleInputKeydown:In,handleTimeInputFocus:En,handleTimeInputBlur:Vn,handleNowClick:Bn,handleConfirmClick:Ln,handleTimeInputUpdateValue:qn,handleMenuFocusOut:Wn,handleCancelClick:$n,handleClickOutside:zn,handleTimeInputActivate:An,handleTimeInputDeactivate:Sn,handleHourClick:Fn,handleMinuteClick:_n,handleSecondClick:Hn,handleAmPmClick:Rn,handleTimeInputClear:Mn,handleFocusDetectorFocus:Nn,handleMenuKeydown:Pn,handleTriggerClick:Yn,mergedTheme:p,triggerCssVars:a?void 0:Mt,triggerThemeClass:ge==null?void 0:ge.themeClass,triggerOnRender:ge==null?void 0:ge.onRender,cssVars:a?void 0:Nt,themeClass:be==null?void 0:be.themeClass,onRender:be==null?void 0:be.onRender,clearSelectedValue:On}},render(){const{mergedClsPrefix:t,$slots:r,triggerOnRender:e}=this;return e==null||e(),w("div",{class:[`${t}-time-picker`,this.triggerThemeClass],style:this.triggerCssVars},w(kr,null,{default:()=>[w(Mr,null,{default:()=>w(Or,{ref:"inputInstRef",status:this.mergedStatus,value:this.displayTimeString,bordered:this.mergedBordered,passivelyActivated:!0,attrSize:this.mergedAttrSize,theme:this.mergedTheme.peers.Input,themeOverrides:this.mergedTheme.peerOverrides.Input,stateful:this.stateful,size:this.mergedSize,placeholder:this.localizedPlaceholder,clearable:this.clearable,disabled:this.mergedDisabled,textDecoration:this.isValueInvalid?"line-through":void 0,onFocus:this.handleTimeInputFocus,onBlur:this.handleTimeInputBlur,onActivate:this.handleTimeInputActivate,onDeactivate:this.handleTimeInputDeactivate,onUpdateValue:this.handleTimeInputUpdateValue,onClear:this.handleTimeInputClear,internalDeactivateOnEnter:!0,internalForceFocus:this.mergedShow,readonly:this.inputReadonly||this.mergedDisabled,onClick:this.handleTriggerClick,onKeydown:this.handleInputKeydown},this.showIcon?{[this.clearable?"clear-icon-placeholder":"suffix"]:()=>w(lr,{clsPrefix:t,class:`${t}-time-picker-icon`},{default:()=>r.icon?r.icon():w(Yr,null)})}:null)}),w(Nr,{teleportDisabled:this.adjustedTo===ot.tdkey,show:this.mergedShow,to:this.adjustedTo,containerClass:this.namespace,placement:this.placement},{default:()=>w(dr,{name:"fade-in-scale-up-transition",appear:this.isMounted},{default:()=>{var n;return this.mergedShow?((n=this.onRender)===null||n===void 0||n.call(this),pr(w(Qi,{ref:"panelInstRef",actions:this.actions,class:this.themeClass,style:this.cssVars,seconds:this.seconds,minutes:this.minutes,hours:this.hours,transitionDisabled:this.transitionDisabled,hourValue:this.hourValue,showHour:this.hourInFormat,isHourInvalid:this.isHourInvalid,isHourDisabled:this.isHourDisabled,minuteValue:this.minuteValue,showMinute:this.minuteInFormat,isMinuteInvalid:this.isMinuteInvalid,isMinuteDisabled:this.isMinuteDisabled,secondValue:this.secondValue,amPmValue:this.amPmValue,showSecond:this.secondInFormat,isSecondInvalid:this.isSecondInvalid,isSecondDisabled:this.isSecondDisabled,isValueInvalid:this.isValueInvalid,clearText:this.localizedClear,nowText:this.localizedNow,confirmText:this.localizedPositiveText,use12Hours:this.use12Hours,onFocusout:this.handleMenuFocusOut,onKeydown:this.handleMenuKeydown,onHourClick:this.handleHourClick,onMinuteClick:this.handleMinuteClick,onSecondClick:this.handleSecondClick,onAmPmClick:this.handleAmPmClick,onNowClick:this.handleNowClick,onConfirmClick:this.handleConfirmClick,onClearClick:this.clearSelectedValue,onFocusDetectorFocus:this.handleFocusDetectorFocus}),[[fr,this.handleClickOutside,void 0,{capture:!0}]])):null}})})]}))}}),hs=Object.freeze(Object.defineProperty({__proto__:null,NTimePicker:Zi,timePickerProps:pn},Symbol.toStringTag,{value:"Module"}));export{ms as A,ve as B,Vt as C,At as D,hs as E,Zi as N,ft as a,Sr as b,Ce as c,us as d,Fe as e,ke as f,k as g,oi as h,St as i,mt as j,si as k,on as l,cs as m,fs as n,ai as o,Lr as p,ls as q,$e as r,ii as s,Ft as t,Ae as u,Be as v,os as w,is as x,ds as y,ss as z};
