import{d as r,k as o,l,q as d,s as t,t as p,v as n,A as i,B as c,C as u,I as f,x as m}from"../jse/index-index-UaL0SrHU.js";const C=r({__name:"Card",props:{class:{}},setup(a){const s=a;return(e,_)=>(l(),o("div",{class:d(t(p)("bg-card text-card-foreground border-border rounded-xl border",s.class))},[n(e.$slots,"default")],2))}}),h=r({__name:"CardContent",props:{class:{}},setup(a){const s=a;return(e,_)=>(l(),o("div",{class:d(t(p)("p-6 pt-0",s.class))},[n(e.$slots,"default")],2))}}),x=r({__name:"CardHeader",props:{class:{}},setup(a){const s=a;return(e,_)=>(l(),o("div",{class:d(t(p)("flex flex-col gap-y-1.5 p-5",s.class))},[n(e.$slots,"default")],2))}}),$=r({__name:"CardTitle",props:{class:{}},setup(a){const s=a;return(e,_)=>(l(),o("h3",{class:d(t(p)("font-semibold leading-none tracking-tight",s.class))},[n(e.$slots,"default")],2))}}),b=r({name:"AnalysisChartCard",__name:"analysis-chart-card",props:{title:{}},setup(a){return(s,e)=>(l(),i(t(C),null,{default:c(()=>[u(t(x),null,{default:c(()=>[u(t($),{class:"text-xl"},{default:c(()=>[f(m(s.title),1)]),_:1})]),_:1}),u(t(h),null,{default:c(()=>[n(s.$slots,"default")]),_:3})]),_:3}))}});export{x as _,$ as a,h as b,C as c,b as d};
