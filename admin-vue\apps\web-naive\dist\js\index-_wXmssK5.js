import{b3 as Ge,Z as Pe,aN as Ze,a4 as dn,g as V,j as Y,k as G,q as ee,i as un,ba as Je,r as Ee,w as cn,ak as fn,a3 as vn,l as Qe,a0 as hn,m as ge,dn as gn,B as pn,z as ie,an as ze,n as Xe,bD as mn,bb as Ke,v as bn,aH as yn,bc as Sn,bd as On,dp as wn,y as I}from"./bootstrap-B_sue86n.js";import{i as Ie,e as Fn,B as Mn,V as Rn,d as xn,u as Ne}from"./Follower-C2co6Kvh.js";import{a as Cn,u as Tn,N as kn,b as Pn,c as zn}from"./Selection-B1GrIgK6.js";import{F as Bn,h as le}from"./FocusDetector-DeVNIRXA.js";import{d as ae,h,b as Ye,E as B,g as T,c as M,w as Ae,f as _n,P as Ue,j as Nn,z as An,a4 as In}from"../jse/index-index-UaL0SrHU.js";import{V as Vn}from"./VirtualList-B6BLUGgC.js";import{u as He}from"./use-merged-state-lZNesr9e.js";import{u as Dn}from"./Popover-ulf1mwTf.js";import{u as Ln}from"./use-locale-zaiRAV2Y.js";function Be(e){const i=e.filter(a=>a!==void 0);if(i.length!==0)return i.length===1?i[0]:a=>{e.forEach(r=>{r&&r(a)})}}const jn=ae({name:"Checkmark",render(){return h("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 16 16"},h("g",{fill:"none"},h("path",{d:"M14.046 3.486a.75.75 0 0 1-.032 1.06l-7.93 7.474a.85.85 0 0 1-1.188-.022l-2.68-2.72a.75.75 0 1 1 1.068-1.053l2.234 2.267l7.468-7.038a.75.75 0 0 1 1.06.032z",fill:"currentColor"})))}}),We=ae({name:"NBaseSelectGroupHeader",props:{clsPrefix:{type:String,required:!0},tmNode:{type:Object,required:!0}},setup(){const{renderLabelRef:e,renderOptionRef:i,labelFieldRef:a,nodePropsRef:r}=Ye(Ie);return{labelField:a,nodeProps:r,renderLabel:e,renderOption:i}},render(){const{clsPrefix:e,renderLabel:i,renderOption:a,nodeProps:r,tmNode:{rawNode:s}}=this,p=r==null?void 0:r(s),u=i?i(s,!1):Ge(s[this.labelField],s,!1),b=h("div",Object.assign({},p,{class:[`${e}-base-select-group-header`,p==null?void 0:p.class]}),u);return s.render?s.render({node:b,option:s}):a?a({node:b,option:s,selected:!1}):b}});function $n(e,i){return h(Ze,{name:"fade-in-scale-up-transition"},{default:()=>e?h(dn,{clsPrefix:i,class:`${i}-base-select-option__check`},{default:()=>h(jn)}):null})}const qe=ae({name:"NBaseSelectOption",props:{clsPrefix:{type:String,required:!0},tmNode:{type:Object,required:!0}},setup(e){const{valueRef:i,pendingTmNodeRef:a,multipleRef:r,valueSetRef:s,renderLabelRef:p,renderOptionRef:u,labelFieldRef:b,valueFieldRef:R,showCheckmarkRef:_,nodePropsRef:S,handleOptionClick:D,handleOptionMouseEnter:w}=Ye(Ie),y=Pe(()=>{const{value:x}=a;return x?e.tmNode.key===x.key:!1});function m(x){const{tmNode:F}=e;F.disabled||D(x,F)}function L(x){const{tmNode:F}=e;F.disabled||w(x,F)}function ne(x){const{tmNode:F}=e,{value:z}=y;F.disabled||z||w(x,F)}return{multiple:r,isGrouped:Pe(()=>{const{tmNode:x}=e,{parent:F}=x;return F&&F.rawNode.type==="group"}),showCheckmark:_,nodeProps:S,isPending:y,isSelected:Pe(()=>{const{value:x}=i,{value:F}=r;if(x===null)return!1;const z=e.tmNode.rawNode[R.value];if(F){const{value:k}=s;return k.has(z)}else return x===z}),labelField:b,renderLabel:p,renderOption:u,handleMouseMove:ne,handleMouseEnter:L,handleClick:m}},render(){const{clsPrefix:e,tmNode:{rawNode:i},isSelected:a,isPending:r,isGrouped:s,showCheckmark:p,nodeProps:u,renderOption:b,renderLabel:R,handleClick:_,handleMouseEnter:S,handleMouseMove:D}=this,w=$n(a,e),y=R?[R(i,a),p&&w]:[Ge(i[this.labelField],i,a),p&&w],m=u==null?void 0:u(i),L=h("div",Object.assign({},m,{class:[`${e}-base-select-option`,i.class,m==null?void 0:m.class,{[`${e}-base-select-option--disabled`]:i.disabled,[`${e}-base-select-option--selected`]:a,[`${e}-base-select-option--grouped`]:s,[`${e}-base-select-option--pending`]:r,[`${e}-base-select-option--show-checkmark`]:p}],style:[(m==null?void 0:m.style)||"",i.style||""],onClick:Be([_,m==null?void 0:m.onClick]),onMouseenter:Be([S,m==null?void 0:m.onMouseenter]),onMousemove:Be([D,m==null?void 0:m.onMousemove])}),h("div",{class:`${e}-base-select-option__content`},y));return i.render?i.render({node:L,option:i,selected:a}):b?b({node:L,option:i,selected:a}):L}}),En=V("base-select-menu",`
 line-height: 1.5;
 outline: none;
 z-index: 0;
 position: relative;
 border-radius: var(--n-border-radius);
 transition:
 background-color .3s var(--n-bezier),
 box-shadow .3s var(--n-bezier);
 background-color: var(--n-color);
`,[V("scrollbar",`
 max-height: var(--n-height);
 `),V("virtual-list",`
 max-height: var(--n-height);
 `),V("base-select-option",`
 min-height: var(--n-option-height);
 font-size: var(--n-option-font-size);
 display: flex;
 align-items: center;
 `,[Y("content",`
 z-index: 1;
 white-space: nowrap;
 text-overflow: ellipsis;
 overflow: hidden;
 `)]),V("base-select-group-header",`
 min-height: var(--n-option-height);
 font-size: .93em;
 display: flex;
 align-items: center;
 `),V("base-select-menu-option-wrapper",`
 position: relative;
 width: 100%;
 `),Y("loading, empty",`
 display: flex;
 padding: 12px 32px;
 flex: 1;
 justify-content: center;
 `),Y("loading",`
 color: var(--n-loading-color);
 font-size: var(--n-loading-size);
 `),Y("header",`
 padding: 8px var(--n-option-padding-left);
 font-size: var(--n-option-font-size);
 transition: 
 color .3s var(--n-bezier),
 border-color .3s var(--n-bezier);
 border-bottom: 1px solid var(--n-action-divider-color);
 color: var(--n-action-text-color);
 `),Y("action",`
 padding: 8px var(--n-option-padding-left);
 font-size: var(--n-option-font-size);
 transition: 
 color .3s var(--n-bezier),
 border-color .3s var(--n-bezier);
 border-top: 1px solid var(--n-action-divider-color);
 color: var(--n-action-text-color);
 `),V("base-select-group-header",`
 position: relative;
 cursor: default;
 padding: var(--n-option-padding);
 color: var(--n-group-header-text-color);
 `),V("base-select-option",`
 cursor: pointer;
 position: relative;
 padding: var(--n-option-padding);
 transition:
 color .3s var(--n-bezier),
 opacity .3s var(--n-bezier);
 box-sizing: border-box;
 color: var(--n-option-text-color);
 opacity: 1;
 `,[G("show-checkmark",`
 padding-right: calc(var(--n-option-padding-right) + 20px);
 `),ee("&::before",`
 content: "";
 position: absolute;
 left: 4px;
 right: 4px;
 top: 0;
 bottom: 0;
 border-radius: var(--n-border-radius);
 transition: background-color .3s var(--n-bezier);
 `),ee("&:active",`
 color: var(--n-option-text-color-pressed);
 `),G("grouped",`
 padding-left: calc(var(--n-option-padding-left) * 1.5);
 `),G("pending",[ee("&::before",`
 background-color: var(--n-option-color-pending);
 `)]),G("selected",`
 color: var(--n-option-text-color-active);
 `,[ee("&::before",`
 background-color: var(--n-option-color-active);
 `),G("pending",[ee("&::before",`
 background-color: var(--n-option-color-active-pending);
 `)])]),G("disabled",`
 cursor: not-allowed;
 `,[un("selected",`
 color: var(--n-option-text-color-disabled);
 `),G("selected",`
 opacity: var(--n-option-opacity-disabled);
 `)]),Y("check",`
 font-size: 16px;
 position: absolute;
 right: calc(var(--n-option-padding-right) - 4px);
 top: calc(50% - 7px);
 color: var(--n-option-check-color);
 transition: color .3s var(--n-bezier);
 `,[Je({enterScale:"0.5"})])])]),Kn=ae({name:"InternalSelectMenu",props:Object.assign(Object.assign({},ge.props),{clsPrefix:{type:String,required:!0},scrollable:{type:Boolean,default:!0},treeMate:{type:Object,required:!0},multiple:Boolean,size:{type:String,default:"medium"},value:{type:[String,Number,Array],default:null},autoPending:Boolean,virtualScroll:{type:Boolean,default:!0},show:{type:Boolean,default:!0},labelField:{type:String,default:"label"},valueField:{type:String,default:"value"},loading:Boolean,focusable:Boolean,renderLabel:Function,renderOption:Function,nodeProps:Function,showCheckmark:{type:Boolean,default:!0},onMousedown:Function,onScroll:Function,onFocus:Function,onBlur:Function,onKeyup:Function,onKeydown:Function,onTabOut:Function,onMouseenter:Function,onMouseleave:Function,onResize:Function,resetMenuOnOptionsChange:{type:Boolean,default:!0},inlineThemeDisabled:Boolean,onToggle:Function}),setup(e){const{mergedClsPrefixRef:i,mergedRtlRef:a}=Qe(e),r=hn("InternalSelectMenu",a,i),s=ge("InternalSelectMenu","-internal-select-menu",En,gn,e,B(e,"clsPrefix")),p=T(null),u=T(null),b=T(null),R=M(()=>e.treeMate.getFlattenedNodes()),_=M(()=>Cn(R.value)),S=T(null);function D(){const{treeMate:t}=e;let l=null;const{value:O}=e;O===null?l=t.getFirstAvailableNode():(e.multiple?l=t.getNode((O||[])[(O||[]).length-1]):l=t.getNode(O),(!l||l.disabled)&&(l=t.getFirstAvailableNode())),j(l||null)}function w(){const{value:t}=S;t&&!e.treeMate.getNode(t.key)&&(S.value=null)}let y;Ae(()=>e.show,t=>{t?y=Ae(()=>e.treeMate,()=>{e.resetMenuOnOptionsChange?(e.autoPending?D():w(),An(oe)):w()},{immediate:!0}):y==null||y()},{immediate:!0}),_n(()=>{y==null||y()});const m=M(()=>pn(s.value.self[ie("optionHeight",e.size)])),L=M(()=>ze(s.value.self[ie("padding",e.size)])),ne=M(()=>e.multiple&&Array.isArray(e.value)?new Set(e.value):new Set),x=M(()=>{const t=R.value;return t&&t.length===0});function F(t){const{onToggle:l}=e;l&&l(t)}function z(t){const{onScroll:l}=e;l&&l(t)}function k(t){var l;(l=b.value)===null||l===void 0||l.sync(),z(t)}function P(){var t;(t=b.value)===null||t===void 0||t.sync()}function re(){const{value:t}=S;return t||null}function K(t,l){l.disabled||j(l,!1)}function me(t,l){l.disabled||F(l)}function be(t){var l;le(t,"action")||(l=e.onKeyup)===null||l===void 0||l.call(e,t)}function U(t){var l;le(t,"action")||(l=e.onKeydown)===null||l===void 0||l.call(e,t)}function Z(t){var l;(l=e.onMousedown)===null||l===void 0||l.call(e,t),!e.focusable&&t.preventDefault()}function se(){const{value:t}=S;t&&j(t.getNext({loop:!0}),!0)}function te(){const{value:t}=S;t&&j(t.getPrev({loop:!0}),!0)}function j(t,l=!1){S.value=t,l&&oe()}function oe(){var t,l;const O=S.value;if(!O)return;const W=_.value(O.key);W!==null&&(e.virtualScroll?(t=u.value)===null||t===void 0||t.scrollTo({index:W}):(l=b.value)===null||l===void 0||l.scrollTo({index:W,elSize:m.value}))}function J(t){var l,O;!((l=p.value)===null||l===void 0)&&l.contains(t.target)&&((O=e.onFocus)===null||O===void 0||O.call(e,t))}function ye(t){var l,O;!((l=p.value)===null||l===void 0)&&l.contains(t.relatedTarget)||(O=e.onBlur)===null||O===void 0||O.call(e,t)}Ue(Ie,{handleOptionMouseEnter:K,handleOptionClick:me,valueSetRef:ne,pendingTmNodeRef:S,nodePropsRef:B(e,"nodeProps"),showCheckmarkRef:B(e,"showCheckmark"),multipleRef:B(e,"multiple"),valueRef:B(e,"value"),renderLabelRef:B(e,"renderLabel"),renderOptionRef:B(e,"renderOption"),labelFieldRef:B(e,"labelField"),valueFieldRef:B(e,"valueField")}),Ue(Fn,p),Nn(()=>{const{value:t}=b;t&&t.sync()});const H=M(()=>{const{size:t}=e,{common:{cubicBezierEaseInOut:l},self:{height:O,borderRadius:W,color:ce,groupHeaderTextColor:fe,actionDividerColor:q,optionTextColorPressed:A,optionTextColor:ve,optionTextColorDisabled:Q,optionTextColorActive:Se,optionOpacityDisabled:Oe,optionCheckColor:we,actionTextColor:Fe,optionColorPending:Me,optionColorActive:Re,loadingColor:xe,loadingSize:Ce,optionColorActivePending:Te,[ie("optionFontSize",t)]:ke,[ie("optionHeight",t)]:he,[ie("optionPadding",t)]:$}}=s.value;return{"--n-height":O,"--n-action-divider-color":q,"--n-action-text-color":Fe,"--n-bezier":l,"--n-border-radius":W,"--n-color":ce,"--n-option-font-size":ke,"--n-group-header-text-color":fe,"--n-option-check-color":we,"--n-option-color-pending":Me,"--n-option-color-active":Re,"--n-option-color-active-pending":Te,"--n-option-height":he,"--n-option-opacity-disabled":Oe,"--n-option-text-color":ve,"--n-option-text-color-active":Se,"--n-option-text-color-disabled":Q,"--n-option-text-color-pressed":A,"--n-option-padding":$,"--n-option-padding-left":ze($,"left"),"--n-option-padding-right":ze($,"right"),"--n-loading-color":xe,"--n-loading-size":Ce}}),{inlineThemeDisabled:de}=e,N=de?Xe("internal-select-menu",M(()=>e.size[0]),H,e):void 0,ue={selfRef:p,next:se,prev:te,getPendingTmNode:re};return Tn(p,e.onResize),Object.assign({mergedTheme:s,mergedClsPrefix:i,rtlEnabled:r,virtualListRef:u,scrollbarRef:b,itemSize:m,padding:L,flattenedNodes:R,empty:x,virtualListContainer(){const{value:t}=u;return t==null?void 0:t.listElRef},virtualListContent(){const{value:t}=u;return t==null?void 0:t.itemsElRef},doScroll:z,handleFocusin:J,handleFocusout:ye,handleKeyUp:be,handleKeyDown:U,handleMouseDown:Z,handleVirtualListResize:P,handleVirtualListScroll:k,cssVars:de?void 0:H,themeClass:N==null?void 0:N.themeClass,onRender:N==null?void 0:N.onRender},ue)},render(){const{$slots:e,virtualScroll:i,clsPrefix:a,mergedTheme:r,themeClass:s,onRender:p}=this;return p==null||p(),h("div",{ref:"selfRef",tabindex:this.focusable?0:-1,class:[`${a}-base-select-menu`,this.rtlEnabled&&`${a}-base-select-menu--rtl`,s,this.multiple&&`${a}-base-select-menu--multiple`],style:this.cssVars,onFocusin:this.handleFocusin,onFocusout:this.handleFocusout,onKeyup:this.handleKeyUp,onKeydown:this.handleKeyDown,onMousedown:this.handleMouseDown,onMouseenter:this.onMouseenter,onMouseleave:this.onMouseleave},Ee(e.header,u=>u&&h("div",{class:`${a}-base-select-menu__header`,"data-header":!0,key:"header"},u)),this.loading?h("div",{class:`${a}-base-select-menu__loading`},h(cn,{clsPrefix:a,strokeWidth:20})):this.empty?h("div",{class:`${a}-base-select-menu__empty`,"data-empty":!0},vn(e.empty,()=>[h(kn,{theme:r.peers.Empty,themeOverrides:r.peerOverrides.Empty,size:this.size})])):h(fn,{ref:"scrollbarRef",theme:r.peers.Scrollbar,themeOverrides:r.peerOverrides.Scrollbar,scrollable:this.scrollable,container:i?this.virtualListContainer:void 0,content:i?this.virtualListContent:void 0,onScroll:i?void 0:this.doScroll},{default:()=>i?h(Vn,{ref:"virtualListRef",class:`${a}-virtual-list`,items:this.flattenedNodes,itemSize:this.itemSize,showScrollbar:!1,paddingTop:this.padding.top,paddingBottom:this.padding.bottom,onResize:this.handleVirtualListResize,onScroll:this.handleVirtualListScroll,itemResizable:!0},{default:({item:u})=>u.isGroup?h(We,{key:u.key,clsPrefix:a,tmNode:u}):u.ignored?null:h(qe,{clsPrefix:a,key:u.key,tmNode:u})}):h("div",{class:`${a}-base-select-menu-option-wrapper`,style:{paddingTop:this.padding.top,paddingBottom:this.padding.bottom}},this.flattenedNodes.map(u=>u.isGroup?h(We,{key:u.key,clsPrefix:a,tmNode:u}):h(qe,{clsPrefix:a,key:u.key,tmNode:u})))}),Ee(e.action,u=>u&&[h("div",{class:`${a}-base-select-menu__action`,"data-action":!0,key:"action"},u),h(Bn,{onFocus:this.onTabOut,key:"focus-detector"})]))}});function pe(e){return e.type==="group"}function en(e){return e.type==="ignored"}function _e(e,i){try{return!!(1+i.toString().toLowerCase().indexOf(e.trim().toLowerCase()))}catch(a){return!1}}function Un(e,i){return{getIsGroup:pe,getIgnored:en,getKey(r){return pe(r)?r.name||r.key||"key-required":r[e]},getChildren(r){return r[i]}}}function Hn(e,i,a,r){if(!i)return e;function s(p){if(!Array.isArray(p))return[];const u=[];for(const b of p)if(pe(b)){const R=s(b[r]);R.length&&u.push(Object.assign({},b,{[r]:R}))}else{if(en(b))continue;i(a,b)&&u.push(b)}return u}return s(e)}function Wn(e,i,a){const r=new Map;return e.forEach(s=>{pe(s)?s[a].forEach(p=>{r.set(p[i],p)}):r.set(s[i],s)}),r}const qn=ee([V("select",`
 z-index: auto;
 outline: none;
 width: 100%;
 position: relative;
 font-weight: var(--n-font-weight);
 `),V("select-menu",`
 margin: 4px 0;
 box-shadow: var(--n-menu-box-shadow);
 `,[Je({originalTransition:"background-color .3s var(--n-bezier), box-shadow .3s var(--n-bezier)"})])]),nn=Object.assign(Object.assign({},ge.props),{to:Ne.propTo,bordered:{type:Boolean,default:void 0},clearable:Boolean,clearFilterAfterSelect:{type:Boolean,default:!0},options:{type:Array,default:()=>[]},defaultValue:{type:[String,Number,Array],default:null},keyboard:{type:Boolean,default:!0},value:[String,Number,Array],placeholder:String,menuProps:Object,multiple:Boolean,size:String,menuSize:{type:String},filterable:Boolean,disabled:{type:Boolean,default:void 0},remote:Boolean,loading:Boolean,filter:Function,placement:{type:String,default:"bottom-start"},widthMode:{type:String,default:"trigger"},tag:Boolean,onCreate:Function,fallbackOption:{type:[Function,Boolean],default:void 0},show:{type:Boolean,default:void 0},showArrow:{type:Boolean,default:!0},maxTagCount:[Number,String],ellipsisTagPopoverProps:Object,consistentMenuWidth:{type:Boolean,default:!0},virtualScroll:{type:Boolean,default:!0},labelField:{type:String,default:"label"},valueField:{type:String,default:"value"},childrenField:{type:String,default:"children"},renderLabel:Function,renderOption:Function,renderTag:Function,"onUpdate:value":[Function,Array],inputProps:Object,nodeProps:Function,ignoreComposition:{type:Boolean,default:!0},showOnFocus:Boolean,onUpdateValue:[Function,Array],onBlur:[Function,Array],onClear:[Function,Array],onFocus:[Function,Array],onScroll:[Function,Array],onSearch:[Function,Array],onUpdateShow:[Function,Array],"onUpdate:show":[Function,Array],displayDirective:{type:String,default:"show"},resetMenuOnOptionsChange:{type:Boolean,default:!0},status:String,showCheckmark:{type:Boolean,default:!0},onChange:[Function,Array],items:Array}),Gn=ae({name:"Select",props:nn,slots:Object,setup(e){const{mergedClsPrefixRef:i,mergedBorderedRef:a,namespaceRef:r,inlineThemeDisabled:s}=Qe(e),p=ge("Select","-select",qn,wn,e,i),u=T(e.defaultValue),b=B(e,"value"),R=He(b,u),_=T(!1),S=T(""),D=Dn(e,["items","options"]),w=T([]),y=T([]),m=M(()=>y.value.concat(w.value).concat(D.value)),L=M(()=>{const{filter:n}=e;if(n)return n;const{labelField:o,valueField:d}=e;return(g,f)=>{if(!f)return!1;const c=f[o];if(typeof c=="string")return _e(g,c);const v=f[d];return typeof v=="string"?_e(g,v):typeof v=="number"?_e(g,String(v)):!1}}),ne=M(()=>{if(e.remote)return D.value;{const{value:n}=m,{value:o}=S;return!o.length||!e.filterable?n:Hn(n,L.value,o,e.childrenField)}}),x=M(()=>{const{valueField:n,childrenField:o}=e,d=Un(n,o);return zn(ne.value,d)}),F=M(()=>Wn(m.value,e.valueField,e.childrenField)),z=T(!1),k=He(B(e,"show"),z),P=T(null),re=T(null),K=T(null),{localeRef:me}=Ln("Select"),be=M(()=>{var n;return(n=e.placeholder)!==null&&n!==void 0?n:me.value.placeholder}),U=[],Z=T(new Map),se=M(()=>{const{fallbackOption:n}=e;if(n===void 0){const{labelField:o,valueField:d}=e;return g=>({[o]:String(g),[d]:g})}return n===!1?!1:o=>Object.assign(n(o),{value:o})});function te(n){const o=e.remote,{value:d}=Z,{value:g}=F,{value:f}=se,c=[];return n.forEach(v=>{if(g.has(v))c.push(g.get(v));else if(o&&d.has(v))c.push(d.get(v));else if(f){const C=f(v);C&&c.push(C)}}),c}const j=M(()=>{if(e.multiple){const{value:n}=R;return Array.isArray(n)?te(n):[]}return null}),oe=M(()=>{const{value:n}=R;return!e.multiple&&!Array.isArray(n)?n===null?null:te([n])[0]||null:null}),J=bn(e),{mergedSizeRef:ye,mergedDisabledRef:H,mergedStatusRef:de}=J;function N(n,o){const{onChange:d,"onUpdate:value":g,onUpdateValue:f}=e,{nTriggerFormChange:c,nTriggerFormInput:v}=J;d&&I(d,n,o),f&&I(f,n,o),g&&I(g,n,o),u.value=n,c(),v()}function ue(n){const{onBlur:o}=e,{nTriggerFormBlur:d}=J;o&&I(o,n),d()}function t(){const{onClear:n}=e;n&&I(n)}function l(n){const{onFocus:o,showOnFocus:d}=e,{nTriggerFormFocus:g}=J;o&&I(o,n),g(),d&&q()}function O(n){const{onSearch:o}=e;o&&I(o,n)}function W(n){const{onScroll:o}=e;o&&I(o,n)}function ce(){var n;const{remote:o,multiple:d}=e;if(o){const{value:g}=Z;if(d){const{valueField:f}=e;(n=j.value)===null||n===void 0||n.forEach(c=>{g.set(c[f],c)})}else{const f=oe.value;f&&g.set(f[e.valueField],f)}}}function fe(n){const{onUpdateShow:o,"onUpdate:show":d}=e;o&&I(o,n),d&&I(d,n),z.value=n}function q(){H.value||(fe(!0),z.value=!0,e.filterable&&je())}function A(){fe(!1)}function ve(){S.value="",y.value=U}const Q=T(!1);function Se(){e.filterable&&(Q.value=!0)}function Oe(){e.filterable&&(Q.value=!1,k.value||ve())}function we(){H.value||(k.value?e.filterable?je():A():q())}function Fe(n){var o,d;!((d=(o=K.value)===null||o===void 0?void 0:o.selfRef)===null||d===void 0)&&d.contains(n.relatedTarget)||(_.value=!1,ue(n),A())}function Me(n){l(n),_.value=!0}function Re(){_.value=!0}function xe(n){var o;!((o=P.value)===null||o===void 0)&&o.$el.contains(n.relatedTarget)||(_.value=!1,ue(n),A())}function Ce(){var n;(n=P.value)===null||n===void 0||n.focus(),A()}function Te(n){var o;k.value&&(!((o=P.value)===null||o===void 0)&&o.$el.contains(Sn(n))||A())}function ke(n){if(!Array.isArray(n))return[];if(se.value)return Array.from(n);{const{remote:o}=e,{value:d}=F;if(o){const{value:g}=Z;return n.filter(f=>d.has(f)||g.has(f))}else return n.filter(g=>d.has(g))}}function he(n){$(n.rawNode)}function $(n){if(H.value)return;const{tag:o,remote:d,clearFilterAfterSelect:g,valueField:f}=e;if(o&&!d){const{value:c}=y,v=c[0]||null;if(v){const C=w.value;C.length?C.push(v):w.value=[v],y.value=U}}if(d&&Z.value.set(n[f],n),e.multiple){const c=ke(R.value),v=c.findIndex(C=>C===n[f]);if(~v){if(c.splice(v,1),o&&!d){const C=Ve(n[f]);~C&&(w.value.splice(C,1),g&&(S.value=""))}}else c.push(n[f]),g&&(S.value="");N(c,te(c))}else{if(o&&!d){const c=Ve(n[f]);~c?w.value=[w.value[c]]:w.value=U}Le(),A(),N(n[f],n)}}function Ve(n){return w.value.findIndex(d=>d[e.valueField]===n)}function tn(n){k.value||q();const{value:o}=n.target;S.value=o;const{tag:d,remote:g}=e;if(O(o),d&&!g){if(!o){y.value=U;return}const{onCreate:f}=e,c=f?f(o):{[e.labelField]:o,[e.valueField]:o},{valueField:v,labelField:C}=e;D.value.some(E=>E[v]===c[v]||E[C]===c[C])||w.value.some(E=>E[v]===c[v]||E[C]===c[C])?y.value=U:y.value=[c]}}function on(n){n.stopPropagation();const{multiple:o}=e;!o&&e.filterable&&A(),t(),o?N([],[]):N(null,null)}function ln(n){!le(n,"action")&&!le(n,"empty")&&!le(n,"header")&&n.preventDefault()}function an(n){W(n)}function De(n){var o,d,g,f,c;if(!e.keyboard){n.preventDefault();return}switch(n.key){case" ":if(e.filterable)break;n.preventDefault();case"Enter":if(!(!((o=P.value)===null||o===void 0)&&o.isComposing)){if(k.value){const v=(d=K.value)===null||d===void 0?void 0:d.getPendingTmNode();v?he(v):e.filterable||(A(),Le())}else if(q(),e.tag&&Q.value){const v=y.value[0];if(v){const C=v[e.valueField],{value:E}=R;e.multiple&&Array.isArray(E)&&E.includes(C)||$(v)}}}n.preventDefault();break;case"ArrowUp":if(n.preventDefault(),e.loading)return;k.value&&((g=K.value)===null||g===void 0||g.prev());break;case"ArrowDown":if(n.preventDefault(),e.loading)return;k.value?(f=K.value)===null||f===void 0||f.next():q();break;case"Escape":k.value&&(On(n),A()),(c=P.value)===null||c===void 0||c.focus();break}}function Le(){var n;(n=P.value)===null||n===void 0||n.focus()}function je(){var n;(n=P.value)===null||n===void 0||n.focusInput()}function rn(){var n;k.value&&((n=re.value)===null||n===void 0||n.syncPosition())}ce(),Ae(B(e,"options"),ce);const sn={focus:()=>{var n;(n=P.value)===null||n===void 0||n.focus()},focusInput:()=>{var n;(n=P.value)===null||n===void 0||n.focusInput()},blur:()=>{var n;(n=P.value)===null||n===void 0||n.blur()},blurInput:()=>{var n;(n=P.value)===null||n===void 0||n.blurInput()}},$e=M(()=>{const{self:{menuBoxShadow:n}}=p.value;return{"--n-menu-box-shadow":n}}),X=s?Xe("select",void 0,$e,e):void 0;return Object.assign(Object.assign({},sn),{mergedStatus:de,mergedClsPrefix:i,mergedBordered:a,namespace:r,treeMate:x,isMounted:yn(),triggerRef:P,menuRef:K,pattern:S,uncontrolledShow:z,mergedShow:k,adjustedTo:Ne(e),uncontrolledValue:u,mergedValue:R,followerRef:re,localizedPlaceholder:be,selectedOption:oe,selectedOptions:j,mergedSize:ye,mergedDisabled:H,focused:_,activeWithoutMenuOpen:Q,inlineThemeDisabled:s,onTriggerInputFocus:Se,onTriggerInputBlur:Oe,handleTriggerOrMenuResize:rn,handleMenuFocus:Re,handleMenuBlur:xe,handleMenuTabOut:Ce,handleTriggerClick:we,handleToggle:he,handleDeleteOption:$,handlePatternInput:tn,handleClear:on,handleTriggerBlur:Fe,handleTriggerFocus:Me,handleKeydown:De,handleMenuAfterLeave:ve,handleMenuClickOutside:Te,handleMenuScroll:an,handleMenuKeydown:De,handleMenuMousedown:ln,mergedTheme:p,cssVars:s?void 0:$e,themeClass:X==null?void 0:X.themeClass,onRender:X==null?void 0:X.onRender})},render(){return h("div",{class:`${this.mergedClsPrefix}-select`},h(Mn,null,{default:()=>[h(Rn,null,{default:()=>h(Pn,{ref:"triggerRef",inlineThemeDisabled:this.inlineThemeDisabled,status:this.mergedStatus,inputProps:this.inputProps,clsPrefix:this.mergedClsPrefix,showArrow:this.showArrow,maxTagCount:this.maxTagCount,ellipsisTagPopoverProps:this.ellipsisTagPopoverProps,bordered:this.mergedBordered,active:this.activeWithoutMenuOpen||this.mergedShow,pattern:this.pattern,placeholder:this.localizedPlaceholder,selectedOption:this.selectedOption,selectedOptions:this.selectedOptions,multiple:this.multiple,renderTag:this.renderTag,renderLabel:this.renderLabel,filterable:this.filterable,clearable:this.clearable,disabled:this.mergedDisabled,size:this.mergedSize,theme:this.mergedTheme.peers.InternalSelection,labelField:this.labelField,valueField:this.valueField,themeOverrides:this.mergedTheme.peerOverrides.InternalSelection,loading:this.loading,focused:this.focused,onClick:this.handleTriggerClick,onDeleteOption:this.handleDeleteOption,onPatternInput:this.handlePatternInput,onClear:this.handleClear,onBlur:this.handleTriggerBlur,onFocus:this.handleTriggerFocus,onKeydown:this.handleKeydown,onPatternBlur:this.onTriggerInputBlur,onPatternFocus:this.onTriggerInputFocus,onResize:this.handleTriggerOrMenuResize,ignoreComposition:this.ignoreComposition},{arrow:()=>{var e,i;return[(i=(e=this.$slots).arrow)===null||i===void 0?void 0:i.call(e)]}})}),h(xn,{ref:"followerRef",show:this.mergedShow,to:this.adjustedTo,teleportDisabled:this.adjustedTo===Ne.tdkey,containerClass:this.namespace,width:this.consistentMenuWidth?"target":void 0,minWidth:"target",placement:this.placement},{default:()=>h(Ze,{name:"fade-in-scale-up-transition",appear:this.isMounted,onAfterLeave:this.handleMenuAfterLeave},{default:()=>{var e,i,a;return this.mergedShow||this.displayDirective==="show"?((e=this.onRender)===null||e===void 0||e.call(this),In(h(Kn,Object.assign({},this.menuProps,{ref:"menuRef",onResize:this.handleTriggerOrMenuResize,inlineThemeDisabled:this.inlineThemeDisabled,virtualScroll:this.consistentMenuWidth&&this.virtualScroll,class:[`${this.mergedClsPrefix}-select-menu`,this.themeClass,(i=this.menuProps)===null||i===void 0?void 0:i.class],clsPrefix:this.mergedClsPrefix,focusable:!0,labelField:this.labelField,valueField:this.valueField,autoPending:!0,nodeProps:this.nodeProps,theme:this.mergedTheme.peers.InternalSelectMenu,themeOverrides:this.mergedTheme.peerOverrides.InternalSelectMenu,treeMate:this.treeMate,multiple:this.multiple,size:this.menuSize,renderOption:this.renderOption,renderLabel:this.renderLabel,value:this.mergedValue,style:[(a=this.menuProps)===null||a===void 0?void 0:a.style,this.cssVars],onToggle:this.handleToggle,onScroll:this.handleMenuScroll,onFocus:this.handleMenuFocus,onBlur:this.handleMenuBlur,onKeydown:this.handleMenuKeydown,onTabOut:this.handleMenuTabOut,onMousedown:this.handleMenuMousedown,show:this.mergedShow,showCheckmark:this.showCheckmark,resetMenuOnOptionsChange:this.resetMenuOnOptionsChange}),{empty:()=>{var r,s;return[(s=(r=this.$slots).empty)===null||s===void 0?void 0:s.call(r)]},header:()=>{var r,s;return[(s=(r=this.$slots).header)===null||s===void 0?void 0:s.call(r)]},action:()=>{var r,s;return[(s=(r=this.$slots).action)===null||s===void 0?void 0:s.call(r)]}}),this.displayDirective==="show"?[[mn,this.mergedShow],[Ke,this.handleMenuClickOutside,void 0,{capture:!0}]]:[[Ke,this.handleMenuClickOutside,void 0,{capture:!0}]])):null}})})]}))}}),it=Object.freeze(Object.defineProperty({__proto__:null,NSelect:Gn,selectProps:nn},Symbol.toStringTag,{value:"Module"}));export{Kn as N,Gn as a,Un as c,it as i,Be as m};
