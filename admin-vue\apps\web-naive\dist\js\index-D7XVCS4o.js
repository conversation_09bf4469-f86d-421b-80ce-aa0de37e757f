import{s as r,r as t}from"./RadioGroup-CWOP-sdM.js";import{N as f,b as R,c as $,a as B}from"./RadioGroup-CWOP-sdM.js";import{r as s}from"./bootstrap-B_sue86n.js";import{d as i,h as o}from"../jse/index-index-UaL0SrHU.js";import"./use-merged-state-lZNesr9e.js";import"./get-slot-Bk_rJcZu.js";const h=t,c=i({name:"RadioButton",props:t,setup:r,render(){const{mergedClsPrefix:e}=this;return o("label",{class:[`${e}-radio-button`,this.mergedDisabled&&`${e}-radio-button--disabled`,this.renderSafeChecked&&`${e}-radio-button--checked`,this.focus&&[`${e}-radio-button--focus`]]},o("input",{ref:"inputRef",type:"radio",class:`${e}-radio-input`,value:this.value,name:this.mergedName,checked:this.renderSafeChecked,disabled:this.mergedDisabled,onChange:this.handleRadioInputChange,onFocus:this.handleRadioInputFocus,onBlur:this.handleRadioInputBlur}),o("div",{class:`${e}-radio-button__state-border`}),s(this.$slots.default,a=>!a&&!this.label?null:o("div",{ref:"labelRef",class:`${e}-radio__label`},a||this.label)))}});export{f as NRadio,c as NRadioButton,R as NRadioGroup,h as radioButtonProps,$ as radioGroupProps,B as radioProps};
