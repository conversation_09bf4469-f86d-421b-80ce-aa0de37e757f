import{p as b,c as e,i as q,a as E}from"../jse/index-index-UaL0SrHU.js";function J(){const a=b.getPreferences(),p=b.getInitialPreferences(),P=e(()=>E(p,a)),t=e(()=>a.app),o=e(()=>a.shortcutKeys),u=e(()=>q(a.theme.mode)),g=e(()=>a.app.locale),s=e(()=>t.value.isMobile),y=e(()=>u.value?"dark":"light"),x=e(()=>s.value?"sidebar-nav":t.value.layout),m=e(()=>a.header.enable),l=e(()=>t.value.layout==="full-content"),i=e(()=>t.value.layout==="sidebar-nav"),c=e(()=>t.value.layout==="sidebar-mixed-nav"),S=e(()=>t.value.layout==="header-nav"),d=e(()=>t.value.layout==="header-mixed-nav"),v=e(()=>t.value.layout==="header-sidebar-nav"),h=e(()=>t.value.layout==="mixed-nav"),H=e(()=>h.value||c.value||i.value||d.value||v.value),M=e(()=>a.sidebar.collapsed),L=e(()=>a.tabbar.enable&&a.tabbar.keepAlive),k=e(()=>t.value.authPageLayout==="panel-left"),N=e(()=>t.value.authPageLayout==="panel-right"),I=e(()=>t.value.authPageLayout==="panel-center"),K=e(()=>{const r=a.header.hidden,n=a.sidebar.hidden;return r&&n&&!l.value}),B=e(()=>{const{enable:r,globalSearch:n}=o.value;return r&&n}),C=e(()=>{const{enable:r,globalLogout:n}=o.value;return r&&n}),z=e(()=>{const{enable:r,globalLockScreen:n}=o.value;return r&&n}),A=e(()=>{const{enablePreferences:r,preferencesButtonPosition:n}=a.app;if(!r)return{fixed:!1,header:!1};const{header:D,sidebar:w}=a,F=D.hidden,R=w.hidden,T=F&&R,j=n==="header";if(n!=="auto")return{fixed:n==="fixed",header:j};const f=T||l.value||s.value||!m.value;return{fixed:f,header:!f}});return{authPanelCenter:I,authPanelLeft:k,authPanelRight:N,contentIsMaximize:K,diffPreference:P,globalLockScreenShortcutKey:z,globalLogoutShortcutKey:C,globalSearchShortcutKey:B,isDark:u,isFullContent:l,isHeaderMixedNav:d,isHeaderNav:S,isHeaderSidebarNav:v,isMixedNav:h,isMobile:s,isSideMixedNav:c,isSideMode:H,isSideNav:i,keepAlive:L,layout:x,locale:g,preferencesButtonPosition:A,sidebarCollapsed:M,theme:y}}export{J as u};
