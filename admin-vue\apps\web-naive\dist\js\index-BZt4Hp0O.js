var K=Object.defineProperty;var p=Object.getOwnPropertySymbols;var I=Object.prototype.hasOwnProperty,N=Object.prototype.propertyIsEnumerable;var F=(t,a,s)=>a in t?K(t,a,{enumerable:!0,configurable:!0,writable:!0,value:s}):t[a]=s,j=(t,a)=>{for(var s in a||(a={}))I.call(a,s)&&F(t,s,a[s]);if(p)for(var s of p(a))N.call(a,s)&&F(t,s,a[s]);return t};var A=(t,a)=>{var s={};for(var e in t)I.call(t,e)&&a.indexOf(e)<0&&(s[e]=t[e]);if(t!=null&&p)for(var e of p(t))a.indexOf(e)<0&&N.call(t,e)&&(s[e]=t[e]);return s};import{ap as Q,aq as R,ar as U,as as W,at as X,au as ee,av as ae}from"./bootstrap-B_sue86n.js";import{_ as te}from"./analytics-trends.vue_vue_type_script_setup_true_lang-D4UfNiqS.js";import{_ as se}from"./analytics-visits-data.vue_vue_type_script_setup_true_lang-C75c7PB_.js";import{_ as le}from"./analytics-visits-sales.vue_vue_type_script_setup_true_lang-lRGscdvD.js";import{_ as ne}from"./analytics-visits-source.vue_vue_type_script_setup_true_lang-C-oROGhc.js";import{_ as oe}from"./analytics-visits.vue_vue_type_script_setup_true_lang-973y5RIe.js";import{_ as re,a as ie,b as ce,c as ue,d as h}from"./analysis-chart-card.vue_vue_type_script_setup_true_lang-DkuR_rK2.js";import{d as f,k as d,l as c,q as de,s as l,t as O,v as w,c as V,A as v,B as r,O as fe,g as E,Y as P,V as _e,w as me,j as pe,Z as ve,$ as ge,y as be,x as g,C as n,F as $,L as y,I as q,n as D}from"../jse/index-index-UaL0SrHU.js";import{_ as xe,a as he,b as $e}from"./TabsList.vue_vue_type_script_setup_true_lang-DMrMu-Xs.js";import"./use-echarts-e0qZouDC.js";import"./use-preferences-D9nCK1i-.js";const ye=f({__name:"CardFooter",props:{class:{}},setup(t){const a=t;return(s,e)=>(c(),d("div",{class:de(l(O)("flex items-center p-6 pt-0",a.class))},[w(s.$slots,"default")],2))}}),we=f({__name:"TabsTrigger",props:{value:{},disabled:{type:Boolean},asChild:{type:Boolean},as:{},class:{}},setup(t){const a=t,s=V(()=>{const m=a,{class:i}=m;return A(m,["class"])}),e=Q(s);return(i,o)=>(c(),v(l(R),fe(l(e),{class:l(O)("ring-offset-background focus-visible:ring-ring data-[state=active]:bg-background data-[state=active]:text-foreground inline-flex items-center justify-center whitespace-nowrap rounded-md px-3 py-1 text-sm font-medium transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:shadow",a.class)}),{default:r(()=>[w(i.$slots,"default")]),_:3},16,["class"]))}}),z=f({name:"CountToAnimator",__name:"count-to-animator",props:{autoplay:{type:Boolean,default:!0},color:{default:""},decimal:{default:"."},decimals:{default:0},duration:{default:1500},endVal:{default:2021},prefix:{default:""},separator:{default:","},startVal:{default:0},suffix:{default:""},transition:{default:"linear"},useEasing:{type:Boolean,default:!0}},emits:["finished","onFinished","onStarted","started"],setup(t,{expose:a,emit:s}){const e=t,i=s,o=E(e.startVal),m=E(!1);let T=P(o);const L=V(()=>Y(l(T)));_e(()=>{o.value=e.startVal}),me([()=>e.startVal,()=>e.endVal],()=>{e.autoplay&&k()}),pe(()=>{e.autoplay&&k()});function k(){B(),o.value=e.endVal}function M(){o.value=e.startVal,B()}function B(){T=P(o,j({disabled:m,duration:e.duration,onFinished:()=>{i("finished"),i("onFinished")},onStarted:()=>{i("started"),i("onStarted")}},e.useEasing?{transition:ve[e.transition]}:{}))}function Y(u){if(!u&&u!==0)return"";const{decimal:C,decimals:Z,prefix:G,separator:b,suffix:H}=e;u=Number(u).toFixed(Z),u+="";const x=u.split(".");let _=x[0];const J=x.length>1?C+x[1]:"",S=/(\d+)(\d{3})/;if(b&&!ge(b)&&_)for(;S.test(_);)_=_.replace(S,`$1${b}$2`);return G+_+J+H}return a({reset:M}),(u,C)=>(c(),d("span",{style:be({color:u.color})},g(L.value),5))}}),Ve={class:"card-box w-full px-4 pb-5 pt-3"},Te=f({name:"AnalysisChartsTabs",__name:"analysis-charts-tabs",props:{tabs:{default:()=>[]}},setup(t){const a=t,s=V(()=>{var e,i;return(i=(e=a.tabs)==null?void 0:e[0])==null?void 0:i.value});return(e,i)=>(c(),d("div",Ve,[n(l(xe),{"default-value":s.value},{default:r(()=>[n(l(he),null,{default:r(()=>[(c(!0),d($,null,y(e.tabs,o=>(c(),v(l(we),{key:o.label,value:o.value},{default:r(()=>[q(g(o.label),1)]),_:2},1032,["value"]))),128))]),_:1}),(c(!0),d($,null,y(e.tabs,o=>(c(),v(l($e),{key:o.label,value:o.value,class:"pt-4"},{default:r(()=>[w(e.$slots,o.value)]),_:2},1032,["value"]))),128))]),_:3},8,["default-value"])]))}}),ke={class:"grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-4"},Be=f({name:"AnalysisOverview",__name:"analysis-overview",props:{items:{default:()=>[]}},setup(t){return(a,s)=>(c(),d("div",ke,[(c(!0),d($,null,y(a.items,e=>(c(),v(l(ue),{key:e.title,title:e.title,class:"w-full"},{default:r(()=>[n(l(re),null,{default:r(()=>[n(l(ie),{class:"text-xl"},{default:r(()=>[q(g(e.title),1)]),_:2},1024)]),_:2},1024),n(l(ce),{class:"flex items-center justify-between"},{default:r(()=>[n(l(z),{"end-val":e.value,"start-val":1,class:"text-xl",prefix:""},null,8,["end-val"]),n(l(U),{icon:e.icon,class:"size-8 flex-shrink-0"},null,8,["icon"])]),_:2},1024),n(l(ye),{class:"justify-between"},{default:r(()=>[D("span",null,g(e.totalTitle),1),n(l(z),{"end-val":e.totalValue,"start-val":1,prefix:""},null,8,["end-val"])]),_:2},1024)]),_:2},1032,["title"]))),128))]))}}),Ce={class:"p-5"},Se={class:"mt-5 w-full md:flex"},Me=f({__name:"index",setup(t){const a=[{icon:W,title:"用户量",totalTitle:"总用户量",totalValue:12e4,value:2e3},{icon:X,title:"访问量",totalTitle:"总访问量",totalValue:5e5,value:2e4},{icon:ee,title:"下载量",totalTitle:"总下载量",totalValue:12e4,value:8e3},{icon:ae,title:"使用量",totalTitle:"总使用量",totalValue:5e4,value:5e3}],s=[{label:"流量趋势",value:"trends"},{label:"月访问量",value:"visits"}];return(e,i)=>(c(),d("div",Ce,[n(l(Be),{items:a}),n(l(Te),{tabs:s,class:"mt-5"},{trends:r(()=>[n(te)]),visits:r(()=>[n(oe)]),_:1}),D("div",Se,[n(l(h),{class:"mt-5 md:mr-4 md:mt-0 md:w-1/3",title:"访问数量"},{default:r(()=>[n(se)]),_:1}),n(l(h),{class:"mt-5 md:mr-4 md:mt-0 md:w-1/3",title:"访问来源"},{default:r(()=>[n(ne)]),_:1}),n(l(h),{class:"mt-5 md:mt-0 md:w-1/3",title:"访问来源"},{default:r(()=>[n(le)]),_:1})])]))}});export{Me as default};
