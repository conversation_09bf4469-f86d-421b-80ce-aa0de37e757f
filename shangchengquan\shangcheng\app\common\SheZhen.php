<?php
/**
 * 舌诊公共类
 * 2025-01-03 22:55:53,565-INFO-[<PERSON><PERSON><PERSON>][shezhen_api_001] 舌诊API调用封装
 * 2025-01-13 优化：支持检测和报告两个接口，修复401错误
 */

namespace app\common;

use think\facade\Db;
use think\facade\Log;

class SheZhen
{
    // API端点常量
    const API_DETECT_PATH = '/diagnose/face-tongue/result/';  // 舌面检测接口
    const API_REPORT_PATH = '/diagnose/face-tongue/report/';  // 报告生成接口
    
    /**
     * 调用舌面检测API（第一步：图片检测）
     * 2025-01-13 新增方法 - 专门用于图片检测
     * 2025-01-13 优化：增加session数据存储功能
     * 2025-01-17 扩展：支持面诊和综合诊疗
     * @param array $imageData 图片数据
     * @param int $aid 应用ID
     * @param int $diagnosisType 诊疗类型：1=舌诊，2=面诊，3=综合诊疗
     * @return array 返回结果
     */
    public static function callDetectApi($imageData, $aid = 0, $diagnosisType = 1)
    {
        // 2025-01-17 根据诊疗类型验证图片参数
        $validationResult = self::validateImagesByDiagnosisType($imageData, $diagnosisType);
        if ($validationResult['status'] == 0) {
            Log::error('2025-01-17 ERROR-[SheZhen][call_detect_api_001] ' . $validationResult['msg']);
            return $validationResult;
        }
        
        // 2025-01-13 构建检测请求数据
        $requestData = [
            'scene' => 1,
            'gender' => isset($imageData['gender']) ? $imageData['gender'] : '男'
        ];
        
        // 2025-01-13 添加图片URL
        if (!empty($imageData['ff_image'])) $requestData['ff_image'] = $imageData['ff_image'];
        if (!empty($imageData['tf_image'])) $requestData['tf_image'] = $imageData['tf_image'];
        if (!empty($imageData['tb_image'])) $requestData['tb_image'] = $imageData['tb_image'];
        
        // 2025-01-17 先创建session记录，包含诊疗类型
        $sessionRecord = self::createSessionRecord($aid, $requestData, $diagnosisType);
        if (!$sessionRecord) {
            Log::error('2025-01-17 ERROR-[SheZhen][call_detect_api_002] 创建session记录失败');
            return ['status' => 0, 'msg' => '创建session记录失败'];
        }
        
        // 2025-01-13 调用检测接口
        $result = self::callAliyunApi($requestData, $aid, self::API_DETECT_PATH, 'detect');
        
        // 2025-01-13 更新session记录状态和结果
        if ($result['status'] == 1) {
            // 检测成功，提取session_id并更新记录
            $sessionId = '';
            
            // 2025-01-13 优化session_id提取逻辑，支持多种响应格式
            Log::info('2025-01-13 INFO-[SheZhen][call_detect_api_debug] 检测API响应结构分析', [
                'response_structure' => [
                    'has_data' => isset($result['data']),
                    'data_keys' => isset($result['data']) ? array_keys($result['data']) : [],
                    'response_keys' => array_keys($result)
                ],
                'full_response' => $result['data'] ?? null
            ]);
            
            // 尝试多种路径提取session_id
            $possiblePaths = [
                ['data', 'data', 'session_id'],
                ['data', 'session_id'],
                ['session_id'],
                ['data', 'data', 'sessionId'],
                ['data', 'sessionId'],
                ['sessionId']
            ];
            
            foreach ($possiblePaths as $path) {
                $current = $result;
                foreach ($path as $key) {
                    if (isset($current[$key])) {
                        $current = $current[$key];
                    } else {
                        $current = null;
                        break;
                    }
                }
                if ($current && is_string($current)) {
                    $sessionId = $current;
                    Log::info('2025-01-13 INFO-[SheZhen][call_detect_api_session_found] session_id提取成功', [
                        'session_id' => $sessionId,
                        'extraction_path' => implode('.', $path)
                    ]);
                    break;
                }
            }
            
            if (!empty($sessionId)) {
                // 更新session记录
                $updateResult = self::updateSessionDetectResult($sessionRecord['id'], $sessionId, $result['data'], $result['duration']);
                if ($updateResult) {
                    // 保存图片信息
                    self::saveSessionImages($sessionId, $imageData);
                    
                    Log::info('2025-01-13 INFO-[SheZhen][call_detect_api_003] 检测成功，session_id: ' . $sessionId);
                    $result['session_record_id'] = $sessionRecord['id'];
                    $result['session_id'] = $sessionId;
                } else {
                    Log::error('2025-01-13 ERROR-[SheZhen][call_detect_api_004] 更新session记录失败');
                }
            } else {
                // 检测接口调用成功但未返回session_id
                self::updateSessionDetectError($sessionRecord['id'], '检测接口未返回session_id', $result['duration']);
                Log::error('2025-01-13 ERROR-[SheZhen][call_detect_api_005] 检测接口未返回session_id');
            }
        } else {
            // 检测失败，更新错误信息
            self::updateSessionDetectError($sessionRecord['id'], $result['msg'], isset($result['duration']) ? $result['duration'] : 0);
            Log::error('2025-01-13 ERROR-[SheZhen][call_detect_api_006] 检测接口调用失败: ' . $result['msg']);
        }
        
        return $result;
    }
    
    /**
     * 调用报告生成API（第二步：生成报告）
     * 2025-01-13 新增方法 - 专门用于生成报告  
     * 2025-01-13 优化：从数据库获取session数据，存储报告结果
     * @param string $sessionId 检测接口返回的session_id
     * @param array $answers 问题答案数组
     * @param int $aid 应用ID
     * @return array 返回结果
     */
    public static function callReportApi($sessionId, $answers = [], $aid = 0)
    {
        // 2025-01-13 验证session_id参数
        if (empty($sessionId)) {
            Log::error('2025-01-13 ERROR-[SheZhen][call_report_api_001] session_id不能为空');
            return ['status' => 0, 'msg' => 'session_id不能为空'];
        }
        
        // 2025-01-13 从数据库获取session记录
        $sessionRecord = self::getSessionRecord($sessionId);
        if (!$sessionRecord) {
            Log::error('2025-01-13 ERROR-[SheZhen][call_report_api_002] 未找到session记录: ' . $sessionId);
            return ['status' => 0, 'msg' => '未找到对应的检测记录'];
        }
        
        // 2025-01-13 检查检测状态
        if ($sessionRecord['detect_status'] != 1) {
            Log::error('2025-01-13 ERROR-[SheZhen][call_report_api_003] 检测未成功，无法生成报告');
            return ['status' => 0, 'msg' => '检测未成功，无法生成报告'];
        }
        
        // 2025-01-13 构建报告请求数据
        $requestData = [
            'session_id' => $sessionId,
            'answers' => $answers ?: []
        ];
        
        // 2025-01-13 调用报告接口
        $result = self::callAliyunApi($requestData, $aid, self::API_REPORT_PATH, 'report', $sessionId);
        
        // 2025-01-13 更新session记录的报告状态和结果
        if ($result['status'] == 1) {
            // 报告生成成功
            $reportUrl = '';
            if (isset($result['data']['data']['report_url'])) {
                $reportUrl = $result['data']['data']['report_url'];
            }
            
            // 更新session记录
            $updateResult = self::updateSessionReportResult($sessionRecord['id'], $answers, $result['data'], $reportUrl, $result['duration']);
            if ($updateResult) {
                Log::info('2025-01-13 INFO-[SheZhen][call_report_api_004] 报告生成成功，URL: ' . $reportUrl);
                $result['session_record_id'] = $sessionRecord['id'];
                $result['report_url'] = $reportUrl;
            } else {
                Log::error('2025-01-13 ERROR-[SheZhen][call_report_api_005] 更新session报告记录失败');
            }
        } else {
            // 报告生成失败，更新错误信息
            self::updateSessionReportError($sessionRecord['id'], $answers, $result['msg'], isset($result['duration']) ? $result['duration'] : 0);
            Log::error('2025-01-13 ERROR-[SheZhen][call_report_api_006] 报告生成失败: ' . $result['msg']);
        }
        
        return $result;
    }

    /**
     * 调用阿里云舌诊接口（通用方法）
     * 2025-01-13 优化：支持不同的API端点，修复401错误
     * 2025-01-13 优化：增加session_id参数支持
     * @param array $data 请求数据
     * @param int $aid 应用ID
     * @param string $apiPath API路径
     * @param string $apiType API类型（detect/report）
     * @param string $sessionId 会话ID（可选）
     * @return array 返回结果
     */
    public static function callAliyunApi($data, $aid = 0, $apiPath = self::API_DETECT_PATH, $apiType = 'detect', $sessionId = '')
    {
        // 2025-01-13 获取舌诊配置
        $config = self::getConfig($aid);
        if (!$config) {
            Log::error('2025-01-13 ERROR-[SheZhen][call_aliyun_api_001] 舌诊配置未找到');
            return ['status' => 0, 'msg' => '舌诊配置未找到'];
        }

        // 2025-01-13 检查配置完整性
        if (empty($config['aliyun_app_code']) || empty($config['aliyun_endpoint'])) {
            Log::error('2025-01-13 ERROR-[SheZhen][call_aliyun_api_002] 阿里云配置不完整');
            return ['status' => 0, 'msg' => '阿里云配置不完整，请检查AppCode和Endpoint配置'];
        }

        // 2025-01-13 检查是否启用
        if (isset($config['is_enable']) && $config['is_enable'] == 0) {
            Log::error('2025-01-13 ERROR-[SheZhen][call_aliyun_api_003] 面诊接口已禁用');
            return ['status' => 0, 'msg' => '面诊接口已禁用'];
        }

        // 2025-01-13 检查调用次数限制（仅检测接口需要扣费）
        if ($apiType === 'detect') {
        $limitCheck = self::checkCallLimit($aid);
        if ($limitCheck['status'] == 0) {
                Log::error('2025-01-13 ERROR-[SheZhen][call_aliyun_api_004] ' . $limitCheck['msg']);
            return $limitCheck;
        }
        }

        // 2025-01-13 构建请求参数
        $host = rtrim($config['aliyun_endpoint'], '/');  // 去除末尾斜杠
        $path = $apiPath;
        $method = "POST";
        $appcode = trim($config['aliyun_app_code']);  // 去除空格
        // 2025-01-13 修复请求头格式，添加空格
        $headers = array();
        array_push($headers, "Authorization: APPCODE " . $appcode);  // 修复：添加空格
        array_push($headers, "Content-Type: application/json; charset=UTF-8");  // 修复：添加空格
        
        $url = $host . $path;
        $bodys = json_encode($data, JSON_UNESCAPED_UNICODE);

        // 2025-01-13 记录请求详细信息
        Log::info('2025-01-13 INFO-[SheZhen][call_aliyun_api_005] API请求开始', [
            'api_type' => $apiType,
            'url' => $url,
            'request_data' => $data,
            'aid' => $aid
        ]);

        // 2025-01-13 记录开始时间
        $startTime = microtime(true);

        // 2025-01-13 发起curl请求
        $curl = curl_init();
        curl_setopt($curl, CURLOPT_CUSTOMREQUEST, $method);
        curl_setopt($curl, CURLOPT_URL, $url);
        curl_setopt($curl, CURLOPT_HTTPHEADER, $headers);
        curl_setopt($curl, CURLOPT_FAILONERROR, false);
        curl_setopt($curl, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($curl, CURLOPT_HEADER, false);
        curl_setopt($curl, CURLOPT_TIMEOUT, isset($config['timeout']) ? $config['timeout'] : 30);
        curl_setopt($curl, CURLOPT_CONNECTTIMEOUT, 10);  // 2025-01-13 新增连接超时
        
        // 2025-01-13 HTTPS SSL设置
        if (strpos($host, "https://") === 0) {
            curl_setopt($curl, CURLOPT_SSL_VERIFYPEER, false);
            curl_setopt($curl, CURLOPT_SSL_VERIFYHOST, false);
        }
        
        curl_setopt($curl, CURLOPT_POSTFIELDS, $bodys);
        
        // 2025-01-13 执行请求
        $response = curl_exec($curl);
        $httpCode = curl_getinfo($curl, CURLINFO_HTTP_CODE);
        $error = curl_error($curl);
        $curlInfo = curl_getinfo($curl);  // 2025-01-13 获取详细信息
        curl_close($curl);

        // 2025-01-13 计算调用耗时
        $endTime = microtime(true);
        $duration = round(($endTime - $startTime) * 1000); // 转换为毫秒

        // 2025-01-13 详细错误日志
        Log::info('2025-01-13 INFO-[SheZhen][call_aliyun_api_006] API响应信息', [
            'http_code' => $httpCode,
            'duration' => $duration . 'ms',
            'curl_error' => $error,
            'response_length' => strlen($response),
            'curl_info' => $curlInfo
        ]);

        // 2025-01-13 处理响应结果
        if ($error) {
            $errorMsg = 'cURL错误: ' . $error;
            Log::error('2025-01-13 ERROR-[SheZhen][call_aliyun_api_007] ' . $errorMsg);
            self::logApiCall($aid, $data, null, 0, $errorMsg, $duration, $apiType, $sessionId);
            return ['status' => 0, 'msg' => $errorMsg];
        }

        if ($httpCode !== 200) {
            $errorMsg = 'HTTP错误: ' . $httpCode;
            
            // 2025-01-13 针对401错误的特殊处理
            if ($httpCode === 401) {
                $errorMsg .= ' - 授权失败，请检查AppCode是否正确或是否已过期';
                Log::error('2025-01-13 ERROR-[SheZhen][call_aliyun_api_008] AppCode授权失败', [
                    'appcode' => substr($appcode, 0, 8) . '***',  // 只显示前8位
                    'url' => $url,
                    'response' => $response
                ]);
            }
            
            Log::error('2025-01-13 ERROR-[SheZhen][call_aliyun_api_009] ' . $errorMsg);
            self::logApiCall($aid, $data, $response, 0, $errorMsg, $duration, $apiType, $sessionId);
            return ['status' => 0, 'msg' => $errorMsg, 'response' => $response, 'http_code' => $httpCode];
        }

        // 2025-01-13 解析JSON响应
        $result = json_decode($response, true);
        if (json_last_error() !== JSON_ERROR_NONE) {
            $errorMsg = 'JSON解析错误: ' . json_last_error_msg();
            Log::error('2025-01-13 ERROR-[SheZhen][call_aliyun_api_010] ' . $errorMsg, [
                'response' => $response
            ]);
            self::logApiCall($aid, $data, $response, 0, $errorMsg, $duration, $apiType, $sessionId);
            return ['status' => 0, 'msg' => $errorMsg, 'response' => $response];
        }

        // 2025-01-13 增强错误处理：记录完整的API响应用于问题诊断
        Log::info('2025-01-13 INFO-[SheZhen][call_aliyun_api_010_enhanced] API响应分析', [
            'api_type' => $apiType,
            'response_structure' => [
                'has_code' => isset($result['code']),
                'code_value' => isset($result['code']) ? $result['code'] : 'not_set',
                'code_type' => isset($result['code']) ? gettype($result['code']) : 'not_set',
                'has_message' => isset($result['message']),
                'message_value' => isset($result['message']) ? $result['message'] : 'not_set',
                'has_data' => isset($result['data']),
                'response_keys' => array_keys($result),
                'response_size' => strlen($response)
            ],
            'full_response' => $result
        ]);

        // 2025-01-13 检查API返回的业务状态 - 修复成功状态码判断
        // 阿里云API成功状态码：200, 0, 20000
        $successCodes = [200, 0, 20000, '200', '0', '20000'];
        if (isset($result['code']) && !in_array($result['code'], $successCodes)) {
            // 2025-01-13 增强错误信息提取逻辑
            $errorDetails = [];
            $errorDetails['api_code'] = $result['code'];
            
            // 尝试多种字段获取错误信息
            $possibleErrorFields = ['message', 'msg', 'error', 'error_message', 'description', 'detail'];
            $errorMsg = '未知错误';
            
            foreach ($possibleErrorFields as $field) {
                if (isset($result[$field]) && !empty($result[$field])) {
                    $errorMsg = $result[$field];
                    $errorDetails['error_field'] = $field;
                    break;
                }
            }
            
            // 如果仍然是未知错误，尝试从data字段获取
            if ($errorMsg === '未知错误' && isset($result['data'])) {
                if (is_array($result['data'])) {
                    foreach ($possibleErrorFields as $field) {
                        if (isset($result['data'][$field]) && !empty($result['data'][$field])) {
                            $errorMsg = $result['data'][$field];
                            $errorDetails['error_field'] = 'data.' . $field;
                            break;
                        }
                    }
                } elseif (is_string($result['data']) && !empty($result['data'])) {
                    $errorMsg = $result['data'];
                    $errorDetails['error_field'] = 'data';
                }
            }
            
            $finalErrorMsg = '接口返回错误[' . $result['code'] . ']: ' . $errorMsg;
            $errorDetails['final_message'] = $finalErrorMsg;
            
            Log::error('2025-01-13 ERROR-[SheZhen][call_aliyun_api_011_enhanced] ' . $finalErrorMsg, [
                'error_details' => $errorDetails,
                'full_result' => $result,
                'api_type' => $apiType,
                'session_id' => $sessionId
            ]);
            
            self::logApiCall($aid, $data, $result, 0, $finalErrorMsg, $duration, $apiType, $sessionId);
            return ['status' => 0, 'msg' => $finalErrorMsg, 'data' => $result, 'error_details' => $errorDetails];
        }

        // 2025-01-13 仅检测接口成功时扣除次数
        if ($apiType === 'detect' && $aid > 0) {
            $deductResult = self::deductMianzhenNum($aid);
            if ($deductResult['status'] == 0) {
                Log::warning('2025-01-13 WARNING-[SheZhen][call_aliyun_api_012] 扣除次数失败: ' . $deductResult['msg']);
                // 不影响接口调用结果，仅记录警告
            }
        }

        // 2025-01-13 记录成功的API调用日志
        if (!isset($config['log_enable']) || $config['log_enable'] != 0) {
            self::logApiCall($aid, $data, $result, 1, '', $duration, $apiType, $sessionId);
        }

        // 2025-01-13 记录成功日志
        Log::info('2025-01-13 INFO-[SheZhen][call_aliyun_api_013] API调用成功', [
            'api_type' => $apiType,
            'duration' => $duration . 'ms',
            'aid' => $aid
        ]);

        // 2025-01-13 返回成功结果
        return ['status' => 1, 'data' => $result, 'duration' => $duration, 'api_type' => $apiType];
    }

    /**
     * 获取舌诊配置
     * 2025-01-03 22:55:53,565-INFO-[SheZhen][get_config_001]
     * @param int $aid 应用ID
     * @return array|null 配置信息
     */
    public static function getConfig($aid = 0)
    {
        // 2025-01-03 22:55:53,565-INFO-[SheZhen][get_config_002] 从数据库获取配置
        if ($aid == 0) {
            $aid = defined('aid') ? aid : 0;
        }
        
        $config = Db::name('sysset')->where('name', 'mianzhen_set')->value('value');
        if ($config) {
            // 2025-01-03 22:55:53,565-INFO-[SheZhen][get_config_003] 解析配置JSON
            return json_decode($config, true);
        }
        
        // 2025-01-03 22:55:53,565-INFO-[SheZhen][get_config_004] 返回默认配置
        return null;
    }

    /**
     * 保存舌诊配置
     * 2025-01-03 22:55:53,565-INFO-[SheZhen][save_config_001]
     * @param array $config 配置数据
     * @return bool 保存结果
     */
    public static function saveConfig($config)
    {
        try {
            // 2025-01-03 22:55:53,565-INFO-[SheZhen][save_config_002] 检查是否存在配置
            $exists = Db::name('sysset')->where('name', 'mianzhen_set')->find();
            
            $data = [
                'name' => 'mianzhen_set',
                'value' => json_encode($config)
            ];
            
            if ($exists) {
                // 2025-01-03 22:55:53,565-INFO-[SheZhen][save_config_003] 更新配置
                $result = Db::name('sysset')->where('name', 'mianzhen_set')->update($data);
            } else {
                // 2025-01-03 22:55:53,565-INFO-[SheZhen][save_config_004] 插入新配置
                $result = Db::name('sysset')->insert($data);
            }
            
            Log::info('2025-01-03 22:55:53,565-INFO-[SheZhen][save_config_005] 配置保存成功');
            return $result !== false;
        } catch (\Exception $e) {
            Log::error('2025-01-03 22:55:53,565-ERROR-[SheZhen][save_config_006] 配置保存失败: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * 记录API调用日志
     * 2025-01-13 优化：添加API类型参数和session_id参数
     * @param int $aid 应用ID
     * @param array $request 请求数据
     * @param array $response 响应数据
     * @param int $status 调用状态 0失败 1成功
     * @param string $errorMsg 错误信息
     * @param int $duration 调用耗时(毫秒)
     * @param string $apiType API类型（detect/report）
     * @param string $sessionId 会话ID（可选）
     */
    private static function logApiCall($aid, $request, $response, $status = 1, $errorMsg = '', $duration = 0, $apiType = 'detect', $sessionId = '')
    {
        try {
            // 2025-01-13 构建日志数据
            $logData = [
                'aid' => $aid,
                'request_data' => json_encode($request),
                'response_data' => $response ? json_encode($response) : '',
                'ip' => request()->ip(),
                'user_agent' => request()->header('user-agent'),
                'status' => $status,
                'error_msg' => $errorMsg,
                'duration' => $duration,
                'api_type' => $apiType, // 2025-01-13 新增API类型字段
                'session_id' => $sessionId, // 2025-01-13 新增session_id字段
                'createtime' => time()
            ];
            
            // 2025-01-13 插入调用日志
            Db::name('mianzhen_call_log')->insert($logData);
            
            Log::info('2025-01-13 INFO-[SheZhen][log_api_call_004] API调用日志记录成功');
        } catch (\Exception $e) {
            Log::error('2025-01-13 ERROR-[SheZhen][log_api_call_005] API调用日志记录失败: ' . $e->getMessage());
        }
    }

    /**
     * 检查调用次数限制
     * 2025-01-03 22:55:53,565-INFO-[SheZhen][check_call_limit_001]
     * @param int $aid 应用ID
     * @return array 检查结果
     */
    public static function checkCallLimit($aid = 0)
    {
        // 2025-01-03 22:55:53,565-INFO-[SheZhen][check_call_limit_002] 获取配置
        $config = self::getConfig($aid);
        if (!$config) {
            return ['status' => 0, 'msg' => '配置未找到'];
        }

        // 2025-01-03 22:55:53,565-INFO-[SheZhen][check_call_limit_003] 检查账户面诊次数余额
        if ($aid > 0) {
            $admin_info = Db::name('admin')->where('id', $aid)->find();
            if (!$admin_info) {
                return ['status' => 0, 'msg' => '账户不存在'];
            }
            
            $mianzhen_num = intval($admin_info['mianzhen_num']);
            if ($mianzhen_num <= 0) {
                return ['status' => 0, 'msg' => '面诊次数不足，请联系管理员充值'];
            }
        }

        // 2025-01-03 22:55:53,565-INFO-[SheZhen][check_call_limit_004] 检查是否启用每日限制
        if (empty($config['daily_limit']) || $config['daily_limit'] <= 0) {
            return ['status' => 1, 'msg' => '无每日限制'];
        }

        // 2025-01-03 22:55:53,565-INFO-[SheZhen][check_call_limit_005] 统计今日调用次数
        $today = date('Y-m-d');
        $startTime = strtotime($today . ' 00:00:00');
        $endTime = strtotime($today . ' 23:59:59');
        
        $todayCount = Db::name('mianzhen_call_log')
            ->where('aid', $aid)
            ->where('createtime', 'between', [$startTime, $endTime])
            ->count();

        // 2025-01-03 22:55:53,565-INFO-[SheZhen][check_call_limit_006] 检查是否超出每日限制
        if ($todayCount >= $config['daily_limit']) {
            return ['status' => 0, 'msg' => '今日调用次数已达上限'];
        }

        return ['status' => 1, 'remaining' => $config['daily_limit'] - $todayCount];
    }
    
    /**
     * 扣除面诊次数
     * 2025-01-03 22:55:53,565-INFO-[SheZhen][deduct_mianzhen_num_001]
     * @param int $aid 应用ID
     * @return array 扣除结果
     */
    public static function deductMianzhenNum($aid)
    {
        try {
            if ($aid <= 0) {
                return ['status' => 0, 'msg' => '账户ID无效'];
            }
            
            // 2025-01-03 22:55:53,565-INFO-[SheZhen][deduct_mianzhen_num_002] 开始数据库事务
            Db::startTrans();
            
            // 2025-01-03 22:55:53,565-INFO-[SheZhen][deduct_mianzhen_num_003] 获取当前账户信息
            $admin_info = Db::name('admin')->where('id', $aid)->find();
            if (!$admin_info) {
                Db::rollback();
                return ['status' => 0, 'msg' => '账户不存在'];
            }
            
            $current_num = intval($admin_info['mianzhen_num']);
            if ($current_num <= 0) {
                Db::rollback();
                return ['status' => 0, 'msg' => '面诊次数不足'];
            }
            
            // 2025-01-03 22:55:53,565-INFO-[SheZhen][deduct_mianzhen_num_004] 扣除一次
            $new_num = $current_num - 1;
            $update_result = Db::name('admin')->where('id', $aid)->update([
                'mianzhen_num' => $new_num
            ]);
            
            if (!$update_result) {
                Db::rollback();
                return ['status' => 0, 'msg' => '扣除次数失败'];
            }
            
            // 2025-01-03 22:55:53,565-INFO-[SheZhen][deduct_mianzhen_num_005] 记录扣费日志
            $log_data = [
                'aid' => $aid,
                'recharge_num' => -1, // 负数表示扣除
                'before_num' => $current_num,
                'after_num' => $new_num,
                'operator_id' => 0,
                'operator_name' => 'system',
                'remark' => '系统自动扣除（API调用成功）',
                'createtime' => time(),
                'status' => 1
            ];
            
            $log_result = Db::name('mianzhen_recharge')->insert($log_data);
            if (!$log_result) {
                Db::rollback();
                return ['status' => 0, 'msg' => '记录扣费日志失败'];
            }
            
            // 2025-01-03 22:55:53,565-INFO-[SheZhen][deduct_mianzhen_num_006] 提交事务
            Db::commit();
            
            return [
                'status' => 1, 
                'msg' => '扣除成功',
                'before_num' => $current_num,
                'after_num' => $new_num
            ];
            
        } catch (\Exception $e) {
            Db::rollback();
            Log::error('2025-01-03 22:55:53,565-ERROR-[SheZhen][deduct_mianzhen_num_007] 扣除次数异常: ' . $e->getMessage());
            return ['status' => 0, 'msg' => '系统异常：' . $e->getMessage()];
        }
    }

    /**
     * 测试API连接（不消耗次数）
     * 2025-01-13 新增方法 - 用于测试API配置是否正确
     * @param int $aid 应用ID
     * @return array 测试结果
     */
    public static function testApiConnection($aid = 0)
    {
        // 2025-01-13 获取配置
        $config = self::getConfig($aid);
        if (!$config) {
            return ['status' => 0, 'msg' => '舌诊配置未找到'];
        }

        // 2025-01-13 检查配置完整性
        if (empty($config['aliyun_app_code']) || empty($config['aliyun_endpoint'])) {
            return ['status' => 0, 'msg' => '阿里云配置不完整，请检查AppCode和Endpoint配置'];
        }

        // 2025-01-13 构建测试请求（最小数据）
        $testData = [
            'scene' => 1,
            'tf_image' => 'https://example.com/test.jpg',  // 使用假的URL进行连接测试
            'gender' => '男'
        ];

        // 2025-01-13 构建请求参数
        $host = rtrim($config['aliyun_endpoint'], '/');
        $path = self::API_DETECT_PATH;
        $url = $host . $path;
        $appcode = trim($config['aliyun_app_code']);
        
        $headers = array();
        array_push($headers, "Authorization: APPCODE " . $appcode);
        array_push($headers, "Content-Type: application/json; charset=UTF-8");
        
        $bodys = json_encode($testData, JSON_UNESCAPED_UNICODE);

        // 2025-01-13 发起curl请求
        $curl = curl_init();
        curl_setopt($curl, CURLOPT_CUSTOMREQUEST, "POST");
        curl_setopt($curl, CURLOPT_URL, $url);
        curl_setopt($curl, CURLOPT_HTTPHEADER, $headers);
        curl_setopt($curl, CURLOPT_FAILONERROR, false);
        curl_setopt($curl, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($curl, CURLOPT_HEADER, false);
        curl_setopt($curl, CURLOPT_TIMEOUT, 10);
        curl_setopt($curl, CURLOPT_CONNECTTIMEOUT, 5);
        
        if (strpos($host, "https://") === 0) {
            curl_setopt($curl, CURLOPT_SSL_VERIFYPEER, false);
            curl_setopt($curl, CURLOPT_SSL_VERIFYHOST, false);
        }
        
        curl_setopt($curl, CURLOPT_POSTFIELDS, $bodys);
        
        $response = curl_exec($curl);
        $httpCode = curl_getinfo($curl, CURLINFO_HTTP_CODE);
        $error = curl_error($curl);
        curl_close($curl);

        // 2025-01-13 分析测试结果
        if ($error) {
            return ['status' => 0, 'msg' => '网络连接失败: ' . $error];
        }

        if ($httpCode === 401) {
            return ['status' => 0, 'msg' => 'AppCode授权失败，请检查AppCode是否正确'];
        }

        if ($httpCode === 404) {
            return ['status' => 0, 'msg' => 'API端点不存在，请检查endpoint配置'];
        }

        if ($httpCode === 200) {
            return ['status' => 1, 'msg' => 'API连接正常', 'http_code' => $httpCode];
        }

        return ['status' => 0, 'msg' => 'API连接异常，HTTP状态码: ' . $httpCode, 'http_code' => $httpCode];
    }

    /**
     * 获取API调用统计
     * 2025-01-13 新增方法 - 获取调用次数统计
     * @param int $aid 应用ID
     * @param string $date 日期（Y-m-d格式，默认今天）
     * @return array 统计结果
     */
    public static function getApiStats($aid = 0, $date = '')
    {
        if (empty($date)) {
            $date = date('Y-m-d');
        }
        
        $startTime = strtotime($date . ' 00:00:00');
        $endTime = strtotime($date . ' 23:59:59');
        
        try {
            // 2025-01-13 统计总调用次数
            $totalCalls = Db::name('mianzhen_call_log')
                ->where('aid', $aid)
                ->where('createtime', 'between', [$startTime, $endTime])
                ->count();
            
            // 2025-01-13 统计成功次数
            $successCalls = Db::name('mianzhen_call_log')
                ->where('aid', $aid)
                ->where('status', 1)
                ->where('createtime', 'between', [$startTime, $endTime])
                ->count();
            
            // 2025-01-13 统计失败次数
            $failedCalls = $totalCalls - $successCalls;
            
            // 2025-01-13 按API类型统计
            $detectCalls = Db::name('mianzhen_call_log')
                ->where('aid', $aid)
                ->where('api_type', 'detect')
                ->where('createtime', 'between', [$startTime, $endTime])
                ->count();
                
            $reportCalls = Db::name('mianzhen_call_log')
                ->where('aid', $aid)
                ->where('api_type', 'report')
                ->where('createtime', 'between', [$startTime, $endTime])
                ->count();
            
            // 2025-01-13 获取剩余次数
            $remainingCalls = 0;
            if ($aid > 0) {
                $admin_info = Db::name('admin')->where('id', $aid)->find();
                if ($admin_info) {
                    $remainingCalls = intval($admin_info['mianzhen_num']);
                }
            }
            
            return [
                'status' => 1,
                'data' => [
                    'date' => $date,
                    'total_calls' => $totalCalls,
                    'success_calls' => $successCalls,
                    'failed_calls' => $failedCalls,
                    'detect_calls' => $detectCalls,
                    'report_calls' => $reportCalls,
                    'remaining_calls' => $remainingCalls,
                    'success_rate' => $totalCalls > 0 ? round($successCalls / $totalCalls * 100, 2) : 0
                ]
            ];
            
        } catch (\Exception $e) {
            Log::error('2025-01-13 ERROR-[SheZhen][get_api_stats_001] 获取统计失败: ' . $e->getMessage());
            return ['status' => 0, 'msg' => '获取统计失败: ' . $e->getMessage()];
        }
    }

    /**
     * 验证图片URL是否可访问
     * 2025-01-13 新增方法 - 在调用API前验证图片
     * @param string $imageUrl 图片URL
     * @return array 验证结果
     */
    public static function validateImageUrl($imageUrl)
    {
        if (empty($imageUrl)) {
            return ['status' => 0, 'msg' => '图片URL不能为空'];
        }
        
        if (!filter_var($imageUrl, FILTER_VALIDATE_URL)) {
            return ['status' => 0, 'msg' => '图片URL格式不正确'];
        }
        
        // 2025-01-13 检查图片扩展名
        $allowedExtensions = ['jpg', 'jpeg', 'png', 'gif', 'bmp'];
        $extension = strtolower(pathinfo(parse_url($imageUrl, PHP_URL_PATH), PATHINFO_EXTENSION));
        
        if (!in_array($extension, $allowedExtensions)) {
            return ['status' => 0, 'msg' => '不支持的图片格式，仅支持: ' . implode(', ', $allowedExtensions)];
        }
        
        // 2025-01-13 测试图片是否可访问
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $imageUrl);
        curl_setopt($ch, CURLOPT_NOBODY, true);  // 只获取头部
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_TIMEOUT, 10);
        curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, 5);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
        curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);
        curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
        curl_setopt($ch, CURLOPT_MAXREDIRS, 3);
        
        curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $contentType = curl_getinfo($ch, CURLINFO_CONTENT_TYPE);
        $error = curl_error($ch);
        curl_close($ch);
        
        if ($error) {
            return ['status' => 0, 'msg' => '图片URL无法访问: ' . $error];
        }
        
        if ($httpCode !== 200) {
            return ['status' => 0, 'msg' => '图片URL返回错误状态: ' . $httpCode];
        }
        
        // 2025-01-13 检查Content-Type
        if ($contentType && !strpos($contentType, 'image/') === 0) {
            return ['status' => 0, 'msg' => 'URL指向的不是图片文件'];
        }
        
        return ['status' => 1, 'msg' => '图片URL验证通过'];
    }

    /**
     * 创建session记录
     * 2025-01-13 新增方法 - 在调用检测接口前创建session记录
     * 2025-01-13 修复：不设置session_id字段，避免唯一约束冲突
     * 2025-01-17 扩展：支持诊疗类型
     * @param int $aid 应用ID
     * @param array $requestData 请求数据
     * @param int $diagnosisType 诊疗类型
     * @return array|false session记录或失败
     */
    private static function createSessionRecord($aid, $requestData, $diagnosisType = 1)
    {
        try {
            $sessionData = [
                'aid' => $aid,
                'request_data' => json_encode($requestData, JSON_UNESCAPED_UNICODE),
                'detect_status' => 0, // 待检测
                'report_status' => 0, // 待生成
                'diagnosis_type' => $diagnosisType, // 2025-01-17 新增诊疗类型
                'ip' => request()->ip(),
                'user_agent' => request()->header('user-agent'),
                'createtime' => time(),
                'updatetime' => time()
                // 2025-01-13 修复：不设置session_id字段，等API返回后再更新
            ];
            
            $id = Db::name('mianzhen_sessions')->insertGetId($sessionData);
            if ($id) {
                $sessionData['id'] = $id;
                Log::info('2025-01-13 INFO-[SheZhen][create_session_record_001] session记录创建成功，ID: ' . $id);
                return $sessionData;
            }
            
            return false;
        } catch (\Exception $e) {
            Log::error('2025-01-13 ERROR-[SheZhen][create_session_record_002] 创建session记录失败: ' . $e->getMessage());
            return false;
        }
    }
    
    /**
     * 更新session检测结果
     * 2025-01-13 新增方法 - 检测成功后更新session记录
     * @param int $sessionRecordId session记录ID
     * @param string $sessionId 阿里云返回的session_id
     * @param array $detectResult 检测结果
     * @param int $duration 检测耗时
     * @return bool 更新结果
     */
    private static function updateSessionDetectResult($sessionRecordId, $sessionId, $detectResult, $duration)
    {
        try {
            $updateData = [
                'session_id' => $sessionId,
                'detect_result' => json_encode($detectResult, JSON_UNESCAPED_UNICODE),
                'detect_status' => 1, // 检测成功
                'detect_time' => time(),
                'detect_duration' => $duration,
                'updatetime' => time()
            ];
            
            $result = Db::name('mianzhen_sessions')->where('id', $sessionRecordId)->update($updateData);
            
            if ($result) {
                Log::info('2025-01-13 INFO-[SheZhen][update_session_detect_result_001] session检测结果更新成功');
                return true;
            }
            
            return false;
        } catch (\Exception $e) {
            Log::error('2025-01-13 ERROR-[SheZhen][update_session_detect_result_002] 更新session检测结果失败: ' . $e->getMessage());
            return false;
        }
    }
    
    /**
     * 更新session检测错误
     * 2025-01-13 新增方法 - 检测失败后更新session记录
     * @param int $sessionRecordId session记录ID
     * @param string $errorMsg 错误信息
     * @param int $duration 检测耗时
     * @return bool 更新结果
     */
    private static function updateSessionDetectError($sessionRecordId, $errorMsg, $duration)
    {
        try {
            $updateData = [
                'detect_status' => 2, // 检测失败
                'detect_error' => $errorMsg,
                'detect_time' => time(),
                'detect_duration' => $duration,
                'updatetime' => time()
            ];
            
            $result = Db::name('mianzhen_sessions')->where('id', $sessionRecordId)->update($updateData);
            
            if ($result) {
                Log::info('2025-01-13 INFO-[SheZhen][update_session_detect_error_001] session检测错误更新成功');
                return true;
            }
            
            return false;
        } catch (\Exception $e) {
            Log::error('2025-01-13 ERROR-[SheZhen][update_session_detect_error_002] 更新session检测错误失败: ' . $e->getMessage());
            return false;
        }
    }
    
    /**
     * 获取session记录
     * 2025-01-13 新增方法 - 根据session_id获取session记录
     * 2025-01-13 修复：添加通过记录ID查询的支持
     * @param string $sessionId 阿里云返回的session_id
     * @return array|null session记录
     */
    private static function getSessionRecord($sessionId)
    {
        try {
            $record = Db::name('mianzhen_sessions')->where('session_id', $sessionId)->find();
            
            if ($record) {
                Log::info('2025-01-13 INFO-[SheZhen][get_session_record_001] 获取session记录成功');
                return $record;
            }
            
            return null;
        } catch (\Exception $e) {
            Log::error('2025-01-13 ERROR-[SheZhen][get_session_record_002] 获取session记录失败: ' . $e->getMessage());
            return null;
        }
    }
    
    /**
     * 根据记录ID获取session记录
     * 2025-01-13 新增方法 - 根据数据库记录ID获取session记录
     * @param int $recordId 数据库记录ID
     * @return array|null session记录
     */
    private static function getSessionRecordById($recordId)
    {
        try {
            $record = Db::name('mianzhen_sessions')->where('id', $recordId)->find();
            
            if ($record) {
                Log::info('2025-01-13 INFO-[SheZhen][get_session_record_by_id_001] 根据ID获取session记录成功');
                return $record;
            }
            
            return null;
        } catch (\Exception $e) {
            Log::error('2025-01-13 ERROR-[SheZhen][get_session_record_by_id_002] 根据ID获取session记录失败: ' . $e->getMessage());
            return null;
        }
    }
    
    /**
     * 更新session报告结果
     * 2025-01-13 新增方法 - 报告生成成功后更新session记录
     * @param int $sessionRecordId session记录ID
     * @param array $answers 问题答案
     * @param array $reportResult 报告结果
     * @param string $reportUrl 报告URL
     * @param int $duration 报告生成耗时
     * @return bool 更新结果
     */
    private static function updateSessionReportResult($sessionRecordId, $answers, $reportResult, $reportUrl, $duration)
    {
        try {
            $updateData = [
                'report_answers' => json_encode($answers, JSON_UNESCAPED_UNICODE),
                'report_result' => json_encode($reportResult, JSON_UNESCAPED_UNICODE),
                'report_url' => $reportUrl,
                'report_status' => 1, // 报告生成成功
                'report_time' => time(),
                'report_duration' => $duration,
                'updatetime' => time()
            ];
            
            $result = Db::name('mianzhen_sessions')->where('id', $sessionRecordId)->update($updateData);
            
            if ($result) {
                Log::info('2025-01-13 INFO-[SheZhen][update_session_report_result_001] session报告结果更新成功');
                return true;
            }
            
            return false;
        } catch (\Exception $e) {
            Log::error('2025-01-13 ERROR-[SheZhen][update_session_report_result_002] 更新session报告结果失败: ' . $e->getMessage());
            return false;
        }
    }
    
    /**
     * 更新session报告错误
     * 2025-01-13 新增方法 - 报告生成失败后更新session记录
     * @param int $sessionRecordId session记录ID
     * @param array $answers 问题答案
     * @param string $errorMsg 错误信息
     * @param int $duration 报告生成耗时
     * @return bool 更新结果
     */
    private static function updateSessionReportError($sessionRecordId, $answers, $errorMsg, $duration)
    {
        try {
            $updateData = [
                'report_answers' => json_encode($answers, JSON_UNESCAPED_UNICODE),
                'report_status' => 2, // 报告生成失败
                'report_error' => $errorMsg,
                'report_time' => time(),
                'report_duration' => $duration,
                'updatetime' => time()
            ];
            
            $result = Db::name('mianzhen_sessions')->where('id', $sessionRecordId)->update($updateData);
            
            if ($result) {
                Log::info('2025-01-13 INFO-[SheZhen][update_session_report_error_001] session报告错误更新成功');
                return true;
            }
            
            return false;
        } catch (\Exception $e) {
            Log::error('2025-01-13 ERROR-[SheZhen][update_session_report_error_002] 更新session报告错误失败: ' . $e->getMessage());
            return false;
        }
    }
    
    /**
     * 保存session图片信息
     * 2025-01-13 新增方法 - 保存检测时使用的图片信息
     * @param string $sessionId 会话ID
     * @param array $imageData 图片数据
     * @return bool 保存结果
     */
    private static function saveSessionImages($sessionId, $imageData)
    {
        try {
            $imageTypes = ['ff_image' => '正面脸部', 'tf_image' => '伸舌头', 'tb_image' => '舌背'];
            $saveCount = 0;
            
            foreach ($imageTypes as $type => $description) {
                if (!empty($imageData[$type])) {
                    $imageInfo = [
                        'session_id' => $sessionId,
                        'image_type' => $type,
                        'image_url' => $imageData[$type],
                        'upload_time' => time(),
                        'status' => 1
                    ];
                    
                    $result = Db::name('mianzhen_images')->insert($imageInfo);
                    if ($result) {
                        $saveCount++;
                    }
                }
            }
            
            Log::info('2025-01-13 INFO-[SheZhen][save_session_images_001] 保存图片信息成功，数量: ' . $saveCount);
            return $saveCount > 0;
        } catch (\Exception $e) {
            Log::error('2025-01-13 ERROR-[SheZhen][save_session_images_002] 保存session图片信息失败: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * 获取session详细信息
     * 2025-01-13 新增方法 - 获取完整的session信息包括图片
     * @param string $sessionId 会话ID
     * @return array|null session详细信息
     */
    public static function getSessionDetail($sessionId)
    {
        try {
            // 获取session基本信息
            $session = self::getSessionRecord($sessionId);
            if (!$session) {
                return null;
            }
            
            // 获取关联的图片信息
            $images = Db::name('mianzhen_images')
                ->where('session_id', $sessionId)
                ->where('status', 1)
                ->select()
                ->toArray();
            
            $session['images'] = $images;
            
            // 解析JSON数据
            if ($session['request_data']) {
                $session['request_data_parsed'] = json_decode($session['request_data'], true);
            }
            if ($session['detect_result']) {
                $session['detect_result_parsed'] = json_decode($session['detect_result'], true);
            }
            if ($session['report_answers']) {
                $session['report_answers_parsed'] = json_decode($session['report_answers'], true);
            }
            if ($session['report_result']) {
                $session['report_result_parsed'] = json_decode($session['report_result'], true);
            }
            
            return $session;
        } catch (\Exception $e) {
            Log::error('2025-01-13 ERROR-[SheZhen][get_session_detail_001] 获取session详细信息失败: ' . $e->getMessage());
            return null;
        }
    }

    /**
     * 获取session列表
     * 2025-01-13 新增方法 - 获取session记录列表
     * @param int $aid 应用ID
     * @param int $page 页码
     * @param int $limit 每页数量
     * @param array $filters 筛选条件
     * @return array session列表和统计信息
     */
    public static function getSessionList($aid = 0, $page = 1, $limit = 20, $filters = [])
    {
        try {
            $where = [];
            if ($aid > 0) {
                $where[] = ['aid', '=', $aid];
            }
            
            // 添加筛选条件
            if (!empty($filters['detect_status'])) {
                $where[] = ['detect_status', '=', $filters['detect_status']];
            }
            if (!empty($filters['report_status'])) {
                $where[] = ['report_status', '=', $filters['report_status']];
            }
            if (!empty($filters['start_time'])) {
                $where[] = ['createtime', '>=', strtotime($filters['start_time'])];
            }
            if (!empty($filters['end_time'])) {
                $where[] = ['createtime', '<=', strtotime($filters['end_time'] . ' 23:59:59')];
            }
            
            // 获取总数
            $total = Db::name('mianzhen_sessions')->where($where)->count();
            
            // 获取列表数据
            $list = Db::name('mianzhen_sessions')
                ->where($where)
                ->order('createtime', 'desc')
                ->page($page, $limit)
                ->select()
                ->toArray();
            
            return [
                'status' => 1,
                'data' => [
                    'list' => $list,
                    'total' => $total,
                    'page' => $page,
                    'limit' => $limit,
                    'pages' => ceil($total / $limit)
                ]
            ];
        } catch (\Exception $e) {
            Log::error('2025-01-13 ERROR-[SheZhen][get_session_list_001] 获取session列表失败: ' . $e->getMessage());
            return ['status' => 0, 'msg' => '获取session列表失败: ' . $e->getMessage()];
        }
    }

    /**
     * 根据诊疗类型验证图片参数
     * 2025-01-17 新增方法 - 验证不同诊疗类型所需的图片
     * @param array $imageData 图片数据
     * @param int $diagnosisType 诊疗类型：1=舌诊，2=面诊，3=综合诊疗
     * @return array 验证结果
     */
    public static function validateImagesByDiagnosisType($imageData, $diagnosisType)
    {
        switch ($diagnosisType) {
            case 1: // 舌诊
                if (empty($imageData['tf_image'])) {
                    return ['status' => 0, 'msg' => '舌诊需要提供舌头图片'];
                }
                break;
            case 2: // 面诊
                if (empty($imageData['ff_image'])) {
                    return ['status' => 0, 'msg' => '面诊需要提供面部图片'];
                }
                break;
            case 3: // 综合诊疗
                if (empty($imageData['tf_image']) && empty($imageData['ff_image']) && empty($imageData['tb_image'])) {
                    return ['status' => 0, 'msg' => '综合诊疗至少需要提供一张图片（舌头、面部或舌下脉络）'];
                }
                break;
            default:
                return ['status' => 0, 'msg' => '不支持的诊疗类型'];
        }

        return ['status' => 1, 'msg' => '图片验证通过'];
    }

    /**
     * 获取诊疗类型名称
     * 2025-01-17 新增方法 - 获取诊疗类型的中文名称
     * @param int $diagnosisType 诊疗类型
     * @return string 诊疗类型名称
     */
    public static function getDiagnosisTypeName($diagnosisType)
    {
        $typeNames = [
            1 => '舌诊',
            2 => '面诊',
            3 => '综合诊疗'
        ];

        return $typeNames[$diagnosisType] ?? '未知类型';
    }

    /**
     * 获取诊疗类型配置
     * 2025-01-17 新增方法 - 根据诊疗类型获取对应配置
     * @param int $aid 应用ID
     * @param int $diagnosisType 诊疗类型
     * @return array|null 配置信息
     */
    public static function getDiagnosisConfig($aid = 0, $diagnosisType = 1)
    {
        $config = self::getConfig($aid);
        if (!$config) {
            return null;
        }

        // 根据诊疗类型返回对应配置
        switch ($diagnosisType) {
            case 1: // 舌诊
                return [
                    'is_enable' => isset($config['is_enable']) ? $config['is_enable'] : 1,
                    'price' => $config['single_price'] ?? 0,
                    'free_level' => $config['free_level'] ?? '',
                    'daily_free_count' => $config['daily_free_count'] ?? 1
                ];
            case 2: // 面诊
                return [
                    'is_enable' => isset($config['face_diagnosis_enable']) ? $config['face_diagnosis_enable'] : 1,
                    'price' => $config['face_price'] ?? 0,
                    'free_level' => $config['face_free_level'] ?? '',
                    'daily_free_count' => $config['face_daily_free_count'] ?? 1
                ];
            case 3: // 综合诊疗
                return [
                    'is_enable' => isset($config['comprehensive_diagnosis_enable']) ? $config['comprehensive_diagnosis_enable'] : 1,
                    'price' => $config['comprehensive_price'] ?? 0,
                    'free_level' => $config['comprehensive_free_level'] ?? '',
                    'daily_free_count' => $config['comprehensive_daily_free_count'] ?? 1
                ];
            default:
                return null;
        }
    }
}