var ae=Object.defineProperty,oe=Object.defineProperties;var ie=Object.getOwnPropertyDescriptors;var H=Object.getOwnPropertySymbols;var re=Object.prototype.hasOwnProperty,le=Object.prototype.propertyIsEnumerable;var Z=(i,o,s)=>o in i?ae(i,o,{enumerable:!0,configurable:!0,writable:!0,value:s}):i[o]=s,K=(i,o)=>{for(var s in o||(o={}))re.call(o,s)&&Z(i,s,o[s]);if(H)for(var s of H(o))le.call(o,s)&&Z(i,s,o[s]);return i},X=(i,o)=>oe(i,ie(o));var ee=(i,o,s)=>new Promise((c,r)=>{var u=p=>{try{l(s.next(p))}catch(v){r(v)}},e=p=>{try{l(s.throw(p))}catch(v){r(v)}},l=p=>p.done?c(p.value):Promise.resolve(p.value).then(u,e);l((s=s.apply(i,o)).next())});import{ae as ce,aI as ue,aJ as de,_ as fe,$ as d,aK as U,P as me,E as pe,aw as ge,F as O,aL as he,aM as ye,ag as be,Q,R as ve}from"./bootstrap-B_sue86n.js";import{d as B,c as I,k as _,l as m,y as F,v as M,u as D,g as z,q as j,C as g,s as t,B as h,A as V,I as x,x as w,R as te,S as we,r as ne,w as _e,V as $e,a5 as ke,a6 as se,t as Pe,n as E,j as Se,m as R,a7 as Me}from"../jse/index-index-UaL0SrHU.js";import{T as Le}from"./auth-title-Z_muH2-J.js";import{a as Te,b as Re,M as xe,c as Ee}from"./index-C-lrPiV1.js";const Be=B({__name:"spine-text",props:{animationDuration:{default:2},animationIterationCount:{default:"infinite"}},setup(i){const o=I(()=>({animation:`shine ${i.animationDuration}s linear ${i.animationIterationCount}`}));return(s,c)=>(m(),_("div",{style:F(o.value),class:"vben-spine-text !bg-clip-text text-transparent"},[M(s.$slots,"default")],4))}}),Ce=B({__name:"slider-captcha-action",props:{actionStyle:{},isPassing:{type:Boolean},toLeft:{type:Boolean}},setup(i,{expose:o}){const s=i,c=D("actionRef"),r=z("0"),u=I(()=>{const{actionStyle:l}=s;return X(K({},l),{left:r.value})}),e=I(()=>Number.parseInt(r.value)>10&&!s.isPassing);return o({getEl:()=>c.value,getStyle:()=>{var l;return(l=c==null?void 0:c.value)==null?void 0:l.style},setLeft:l=>{r.value=l}}),(l,p)=>(m(),_("div",{ref_key:"actionRef",ref:c,class:j([{"transition-width !left-0 duration-300":l.toLeft,"rounded-md":e.value},"bg-background dark:bg-accent absolute left-0 top-0 flex h-full cursor-move items-center justify-center px-3.5 shadow-md"]),style:F(u.value),name:"captcha-action"},[g(t(ce),{"is-passing":l.isPassing,class:"text-foreground/60 size-4"},{default:h(()=>[M(l.$slots,"icon",{},()=>[l.isPassing?(m(),V(t(de),{key:1})):(m(),V(t(ue),{key:0}))])]),_:3},8,["is-passing"])],6))}}),Ae=B({__name:"slider-captcha-bar",props:{barStyle:{},toLeft:{type:Boolean}},setup(i,{expose:o}){const s=i,c=D("barRef"),r=z("0"),u=I(()=>{const{barStyle:e}=s;return X(K({},e),{width:r.value})});return o({getEl:()=>c.value,setWidth:e=>{r.value=e}}),(e,l)=>(m(),_("div",{ref_key:"barRef",ref:c,class:j([e.toLeft&&"transition-width !w-0 duration-300","bg-success absolute h-full"]),style:F(u.value)},null,6))}}),De=B({__name:"slider-captcha-content",props:{contentStyle:{},isPassing:{type:Boolean},successText:{},text:{}},setup(i,{expose:o}){const s=i,c=D("contentRef"),r=I(()=>{const{contentStyle:u}=s;return K({},u)});return o({getEl:()=>c.value}),(u,e)=>(m(),_("div",{ref_key:"contentRef",ref:c,class:j([{[u.$style.success]:u.isPassing},"absolute top-0 flex size-full select-none items-center justify-center text-xs"]),style:F(r.value)},[M(u.$slots,"text",{},()=>[g(t(Be),{class:"flex h-full items-center"},{default:h(()=>[x(w(u.isPassing?u.successText:u.text),1)]),_:1})])],6))}}),Ve="_success_fwxn1_2",Ie={success:Ve},We={$style:Ie},Ne=fe(De,[["__cssModules",We]]),Fe=B({__name:"index",props:te({class:{},actionStyle:{default:()=>({})},barStyle:{default:()=>({})},contentStyle:{default:()=>({})},wrapperStyle:{default:()=>({})},isSlot:{type:Boolean,default:!1},successText:{default:""},text:{default:""}},{modelValue:{type:Boolean,default:!1},modelModifiers:{}}),emits:te(["end","move","start","success"],["update:modelValue"]),setup(i,{expose:o,emit:s}){const c=i,r=s,u=we(i,"modelValue"),e=ne({endTime:0,isMoving:!1,isPassing:!1,moveDistance:0,startTime:0,toLeft:!1});o({resume:q});const l=D("wrapperRef"),p=D("barRef"),v=D("contentRef"),$=D("actionRef");_e(()=>e.isPassing,n=>{if(n){const{endTime:y,startTime:b}=e,k=(y-b)/1e3;r("success",{isPassing:n,time:k.toFixed(1)}),u.value=n}}),$e(()=>{e.isPassing=!!u.value});function W(n){return"pageX"in n?n.pageX:"touches"in n&&n.touches[0]?n.touches[0].pageX:0}function C(n){e.isPassing||$.value&&(r("start",n),e.moveDistance=W(n)-Number.parseInt($.value.getStyle().left.replace("px","")||"0",10),e.startTime=Date.now(),e.isMoving=!0)}function a(n){var P,S,A;const y=(S=(P=l.value)==null?void 0:P.offsetWidth)!=null?S:220,b=(A=n==null?void 0:n.offsetWidth)!=null?A:40,k=y-b-6;return{actionWidth:b,offset:k,wrapperWidth:y}}function f(n){const{isMoving:y,moveDistance:b}=e;if(y){const k=t($),P=t(p);if(!k||!P)return;const{actionWidth:S,offset:A,wrapperWidth:N}=a(k.getEl()),T=W(n)-b;r("move",{event:n,moveDistance:b,moveX:T}),T>0&&T<=A?(k.setLeft(`${T}px`),P.setWidth(`${T+S/2}px`)):T>A&&(k.setLeft(`${N-S}px`),P.setWidth(`${N-S/2}px`),c.isSlot||G())}}function L(n){const{isMoving:y,isPassing:b,moveDistance:k}=e;if(y&&!b){r("end",n);const P=$.value,S=t(p);if(!P||!S)return;const A=W(n)-k,{actionWidth:N,offset:T,wrapperWidth:J}=a(P.getEl());A<T?c.isSlot?setTimeout(()=>{if(u.value){const Y=t(v);Y&&(Y.getEl().style.width=`${Number.parseInt(S.getEl().style.width)}px`)}else q()},0):q():(P.setLeft(`${J-N}px`),S.setWidth(`${J-N/2}px`),G()),e.isMoving=!1}}function G(){if(c.isSlot){q();return}e.endTime=Date.now(),e.isPassing=!0,e.isMoving=!1}function q(){e.isMoving=!1,e.isPassing=!1,e.moveDistance=0,e.toLeft=!1,e.startTime=0,e.endTime=0;const n=t($),y=t(p),b=t(v);!n||!y||!b||(b.getEl().style.width="100%",e.toLeft=!0,ke(()=>{e.toLeft=!1,n.setLeft("0"),y.setWidth("0")},300))}return(n,y)=>(m(),_("div",{ref_key:"wrapperRef",ref:l,class:j(t(Pe)("border-border bg-background-deep relative flex h-10 w-full items-center overflow-hidden rounded-md border text-center",c.class)),style:F(n.wrapperStyle),onMouseleave:L,onMousemove:f,onMouseup:L,onTouchend:L,onTouchmove:f},[g(Ae,{ref_key:"barRef",ref:p,"bar-style":n.barStyle,"to-left":e.toLeft},null,8,["bar-style","to-left"]),g(Ne,{ref_key:"contentRef",ref:v,"content-style":n.contentStyle,"is-passing":e.isPassing,"success-text":n.successText||t(d)("ui.captcha.sliderSuccessText"),text:n.text||t(d)("ui.captcha.sliderDefaultText")},se({_:2},[n.$slots.text?{name:"text",fn:h(()=>[M(n.$slots,"text",{isPassing:e.isPassing})]),key:"0"}:void 0]),1032,["content-style","is-passing","success-text","text"]),g(Ce,{ref_key:"actionRef",ref:$,"action-style":n.actionStyle,"is-passing":e.isPassing,"to-left":e.toLeft,onMousedown:C,onTouchstart:C},se({_:2},[n.$slots.actionIcon?{name:"icon",fn:h(()=>[M(n.$slots,"actionIcon",{isPassing:e.isPassing})]),key:"0"}:void 0]),1032,["action-style","is-passing","to-left"])],38))}}),je={class:"w-full sm:mx-auto md:max-w-md"},qe={class:"mt-4 flex items-center justify-between"},Ke={class:"text-muted-foreground text-center text-xs uppercase"},Ue={class:"mt-4 flex flex-wrap justify-center"},Xe=B({name:"ThirdPartyLogin",__name:"third-party-login",setup(i){return(o,s)=>(m(),_("div",je,[E("div",qe,[s[0]||(s[0]=E("span",{class:"border-input w-[35%] border-b dark:border-gray-600"},null,-1)),E("span",Ke,w(t(d)("authentication.thirdPartyLogin")),1),s[1]||(s[1]=E("span",{class:"border-input w-[35%] border-b dark:border-gray-600"},null,-1))]),E("div",Ue,[g(t(U),{class:"mb-3"},{default:h(()=>[g(t(Te))]),_:1}),g(t(U),{class:"mb-3"},{default:h(()=>[g(t(Re))]),_:1}),g(t(U),{class:"mb-3"},{default:h(()=>[g(t(xe))]),_:1}),g(t(U),{class:"mb-3"},{default:h(()=>[g(t(Ee))]),_:1})])]))}}),Oe=["onKeydown"],Qe={class:"text-muted-foreground"},ze={key:0,class:"mb-6 flex justify-between"},Ge={class:"flex-center"},Je={key:1,class:"mb-2 mt-4 flex items-center justify-between"},Ye={key:0,class:"mt-3 text-center text-sm"},He=B({name:"AuthenticationLogin",__name:"login",props:{formSchema:{default:()=>[]},codeLoginPath:{default:"/auth/code-login"},forgetPasswordPath:{default:"/auth/forget-password"},loading:{type:Boolean,default:!1},qrCodeLoginPath:{default:"/auth/qrcode-login"},registerPath:{default:"/auth/register"},showCodeLogin:{type:Boolean,default:!0},showForgetPassword:{type:Boolean,default:!0},showQrcodeLogin:{type:Boolean,default:!0},showRegister:{type:Boolean,default:!0},showRememberMe:{type:Boolean,default:!0},showThirdPartyLogin:{type:Boolean,default:!0},subTitle:{default:""},title:{default:""},submitButtonText:{default:""}},emits:["submit"],setup(i,{expose:o,emit:s}){const c=i,r=s,[u,e]=me(ne({commonConfig:{hideLabel:!0,hideRequiredMark:!0},schema:I(()=>c.formSchema),showDefaultActions:!1})),l=pe(),p=`REMEMBER_ME_USERNAME_${location.hostname}`,v=localStorage.getItem(p)||"",$=z(!!v);function W(){return ee(this,null,function*(){const{valid:a}=yield e.validate(),f=yield e.getValues();a&&(localStorage.setItem(p,$.value?f==null?void 0:f.username:""),r("submit",f))})}function C(a){l.push(a)}return Se(()=>{v&&e.setFieldValue("username",v)}),o({getFormApi:()=>e}),(a,f)=>(m(),_("div",{onKeydown:he(ye(W,["prevent"]),["enter"])},[M(a.$slots,"title",{},()=>[g(Le,null,{desc:h(()=>[E("span",Qe,[M(a.$slots,"subTitle",{},()=>[x(w(a.subTitle||t(d)("authentication.loginSubtitle")),1)])])]),default:h(()=>[M(a.$slots,"title",{},()=>[x(w(a.title||`${t(d)("authentication.welcomeBack")} 👋🏻`),1)])]),_:3})]),g(t(u)),a.showRememberMe||a.showForgetPassword?(m(),_("div",ze,[E("div",Ge,[a.showRememberMe?(m(),V(t(ge),{key:0,checked:$.value,"onUpdate:checked":f[0]||(f[0]=L=>$.value=L),name:"rememberMe"},{default:h(()=>[x(w(t(d)("authentication.rememberMe")),1)]),_:1},8,["checked"])):R("",!0)]),a.showForgetPassword?(m(),_("span",{key:0,class:"vben-link text-sm font-normal",onClick:f[1]||(f[1]=L=>C(a.forgetPasswordPath))},w(t(d)("authentication.forgetPassword")),1)):R("",!0)])):R("",!0),g(t(O),{class:j([{"cursor-wait":a.loading},"w-full"]),loading:a.loading,"aria-label":"login",onClick:W},{default:h(()=>[x(w(a.submitButtonText||t(d)("common.login")),1)]),_:1},8,["class","loading"]),a.showCodeLogin||a.showQrcodeLogin?(m(),_("div",Je,[a.showCodeLogin?(m(),V(t(O),{key:0,class:"w-1/2",variant:"outline",onClick:f[2]||(f[2]=L=>C(a.codeLoginPath))},{default:h(()=>[x(w(t(d)("authentication.mobileLogin")),1)]),_:1})):R("",!0),a.showQrcodeLogin?(m(),V(t(O),{key:1,class:"ml-4 w-1/2",variant:"outline",onClick:f[3]||(f[3]=L=>C(a.qrCodeLoginPath))},{default:h(()=>[x(w(t(d)("authentication.qrcodeLogin")),1)]),_:1})):R("",!0)])):R("",!0),M(a.$slots,"third-party-login",{},()=>[a.showThirdPartyLogin?(m(),V(Xe,{key:0})):R("",!0)]),M(a.$slots,"to-register",{},()=>[a.showRegister?(m(),_("div",Ye,[x(w(t(d)("authentication.accountTip"))+" ",1),E("span",{class:"vben-link text-sm font-normal",onClick:f[4]||(f[4]=L=>C(a.registerPath))},w(t(d)("authentication.createAccount")),1)])):R("",!0)])],40,Oe))}}),at=B({name:"Login",__name:"login",setup(i){const o=be(),s=[{label:"Super",value:"vben"},{label:"Admin",value:"admin"},{label:"User",value:"jack"}],c=I(()=>[{component:"VbenSelect",componentProps:{options:s,placeholder:d("authentication.selectAccount")},fieldName:"selectAccount",label:d("authentication.selectAccount"),rules:Q().min(1,{message:d("authentication.selectAccount")}).optional().default("vben")},{component:"VbenInput",componentProps:{placeholder:d("authentication.usernameTip")},dependencies:{trigger(r,u){if(r.selectAccount){const e=s.find(l=>l.value===r.selectAccount);e&&u.setValues({password:"123456",username:e.value})}},triggerFields:["selectAccount"]},fieldName:"username",label:d("authentication.username"),rules:Q().min(1,{message:d("authentication.usernameTip")})},{component:"VbenInputPassword",componentProps:{placeholder:d("authentication.password")},fieldName:"password",label:d("authentication.password"),rules:Q().min(1,{message:d("authentication.passwordTip")})},{component:Me(Fe),fieldName:"captcha",rules:ve().refine(r=>r,{message:d("authentication.verifyRequiredTip")})}]);return(r,u)=>(m(),V(t(He),{"form-schema":c.value,loading:t(o).loginLoading,onSubmit:t(o).authLogin},null,8,["form-schema","loading","onSubmit"]))}});export{at as _};
