<template>
	<view class="camera-container">
		<!-- 拍摄模式 -->
		<view v-if="!capturedImage" class="camera-mode">
			<!-- 相机预览区域 -->
			<!-- #ifdef MP-WEIXIN || MP-ALIPAY || MP-BAIDU || MP-TOUTIAO || MP-QQ -->
			<camera 
				class="camera-preview"
				:device-position="devicePosition"
				:flash="flashMode"
				@error="onCameraError"
				@initdone="onCameraInit"
			>
			</camera>
			<!-- #endif -->
			
			<!-- #ifdef H5 -->
			<!-- H5环境下显示替代预览区域 -->
			<view class="camera-preview h5-camera-placeholder">
				<view class="h5-camera-content">
					<text class="h5-camera-text">相机预览</text>
					<text class="h5-camera-tip">点击拍照按钮选择图片</text>
				</view>
			</view>
			<!-- #endif -->

			<!-- 顶部状态栏 -->
			<view class="status-bar">
				<view class="back-btn" @touchstart="goBack" @tap="goBack">
					<text class="back-icon">←</text>
				</view>
				<text class="page-title">面诊拍摄</text>
				<view class="flash-btn" :class="{ active: flashOn }" @touchstart="toggleFlash" @tap="toggleFlash">
					<text class="flash-icon">{{ flashOn ? '💡' : '🔦' }}</text>
				</view>
			</view>

			<!-- 拍摄指导区域 -->
			<view class="guide-overlay">
				<view class="guide-frame">
					<view class="frame-corner corner-tl"></view>
					<view class="frame-corner corner-tr"></view>
					<view class="frame-corner corner-bl"></view>
					<view class="frame-corner corner-br"></view>
					<view class="frame-center">
						<text class="guide-text">请将面部置于框内</text>
						<view class="pulse-dot"></view>
					</view>
				</view>
			</view>

			<!-- 拍摄提示区域 -->
			<view class="tips-section">
				<view class="tips-container">
					<view class="tip-item" v-for="(tip, index) in shootingTips" :key="index">
						<view class="tip-icon">{{ tip.icon }}</view>
						<text class="tip-text">{{ tip.text }}</text>
					</view>
				</view>
			</view>

			<!-- 控制区域 -->
			<view class="control-section">
				<view class="control-container">
					<!-- 选择图片 -->
					<view class="control-item">
						<view class="control-btn secondary" @touchstart="chooseImage" @tap="chooseImage">
							<image class="control-icon-img" :src="pre_url + '/static/img/xuanzetupian.png'" mode="aspectFit"></image>
						</view>
						<text class="control-label">选择图片</text>
					</view>
					
					<!-- 拍照按钮 -->
					<view class="control-item capture">
						<view class="control-btn primary" :class="{ capturing: captureLoading }" @touchstart="takePhoto" @tap="takePhoto">
							<view class="capture-ring" v-if="!captureLoading"></view>
							<view class="capture-dot"></view>
						</view>
						<text class="control-label">拍照</text>
					</view>
					
					<!-- 翻转相机 -->
					<view class="control-item">
						<view class="control-btn secondary" @touchstart="flipCamera" @tap="flipCamera" :class="{ 'active': isFlipping }">
							<image class="control-icon-img" :src="pre_url + '/static/img/fanzhuanxiangji.png'" mode="aspectFit"></image>
						</view>
						<text class="control-label">翻转相机</text>
					</view>
				</view>
			</view>
		</view>

		<!-- 预览模式 -->
		<view v-else class="preview-mode">
			<!-- 顶部标题 -->
			<view class="preview-header">
				<text class="preview-title">预览图片</text>
				<text class="preview-subtitle">请确认图片清晰度</text>
			</view>

			<!-- 图片预览区域 -->
			<view class="preview-container">
				<image :src="capturedImage" mode="aspectFit" class="preview-image"></image>
			</view>

			<!-- 预览操作区域 -->
			<view class="preview-actions">
				<view class="action-btn secondary" @touchstart="retakePhoto" @tap="retakePhoto">
					<text class="btn-text">重新拍摄</text>
				</view>
				<view class="action-btn primary" :style="{background:'linear-gradient(90deg,'+t('color1')+' 0%,rgba('+t('color1rgb')+',0.8) 100%)'}" @touchstart="confirmPhoto" @tap="confirmPhoto">
					<text class="btn-text">确认使用</text>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
export default {
	name: 'FaceCamera',
	data() {
		return {
			devicePosition: 'front', // 前置摄像头
			flashMode: 'off',
			flashOn: false,
			capturedImage: '',
			captureLoading: false,
			isFlipping: false,
			// 拍摄提示
			shootingTips: [
				{ icon: '💡', text: '保持光线充足' },
				{ icon: '😊', text: '表情自然' },
				{ icon: '📐', text: '正面拍摄' }
			]
		}
	},
	methods: {
		// 相机初始化完成
		onCameraInit() {
			console.log('2025-07-17 INFO-[face-camera] 相机初始化完成');
		},
		
		// 相机错误
		onCameraError(error) {
			console.error('2025-07-17 ERROR-[face-camera] 相机错误:', error);
			uni.showToast({
				title: '相机启动失败',
				icon: 'none'
			});
		},
		
		// 拍照
		takePhoto() {
			if (this.captureLoading) return;
			
			this.captureLoading = true;
			
			// #ifdef MP-WEIXIN || MP-ALIPAY || MP-BAIDU || MP-TOUTIAO || MP-QQ
			const ctx = uni.createCameraContext();
			ctx.takePhoto({
				quality: 'high',
				success: (res) => {
					console.log('2025-07-17 INFO-[face-camera] 拍照成功:', res.tempImagePath);
					this.capturedImage = res.tempImagePath;
					this.captureLoading = false;
				},
				fail: (error) => {
					console.error('2025-07-17 ERROR-[face-camera] 拍照失败:', error);
					this.captureLoading = false;
					uni.showToast({
						title: '拍照失败，请重试',
						icon: 'none'
					});
				}
			});
			// #endif
			
			// #ifdef H5
			// H5环境下直接选择图片
			this.chooseImage();
			// #endif
		},
		
		// 选择图片
		chooseImage() {
			uni.chooseImage({
				count: 1,
				sizeType: ['original', 'compressed'],
				sourceType: ['album'],
				success: (res) => {
					console.log('2025-07-17 INFO-[face-camera] 选择图片成功:', res.tempFilePaths[0]);
					this.capturedImage = res.tempFilePaths[0];
					this.captureLoading = false;
				},
				fail: (error) => {
					console.error('2025-07-17 ERROR-[face-camera] 选择图片失败:', error);
					this.captureLoading = false;
				}
			});
		},
		
		// 翻转相机
		flipCamera() {
			if (this.isFlipping) return;
			
			this.isFlipping = true;
			this.devicePosition = this.devicePosition === 'front' ? 'back' : 'front';
			
			setTimeout(() => {
				this.isFlipping = false;
			}, 500);
		},
		
		// 切换闪光灯
		toggleFlash() {
			this.flashOn = !this.flashOn;
			this.flashMode = this.flashOn ? 'on' : 'off';
		},
		
		// 重新拍摄
		retakePhoto() {
			this.capturedImage = '';
			this.captureLoading = false;
		},
		
		// 确认照片
		confirmPhoto() {
			if (!this.capturedImage) {
				uni.showToast({
					title: '请先拍摄照片',
					icon: 'none'
				});
				return;
			}
			
			console.log('2025-07-17 INFO-[face-camera] 确认使用照片:', this.capturedImage);
			
			// 跳转到面诊分析页面
			uni.navigateTo({
				url: `/pagesB/diagnosis/face/index?imageUrl=${encodeURIComponent(this.capturedImage)}`
			});
		},
		
		// 返回
		goBack() {
			uni.navigateBack();
		}
	}
}
</script>

<style>
/* 这里会添加完整的样式，参考舌诊拍摄页面的样式 */
.camera-container {
	position: relative;
	width: 100vw;
	height: 100vh;
	background: #000;
	overflow: hidden;
}

.camera-mode, .preview-mode {
	width: 100%;
	height: 100%;
	position: relative;
}

.camera-preview {
	width: 100%;
	height: 100%;
}

.h5-camera-placeholder {
	background: #333;
	display: flex;
	align-items: center;
	justify-content: center;
}

.h5-camera-content {
	text-align: center;
	color: #fff;
}

.h5-camera-text {
	font-size: 32rpx;
	display: block;
	margin-bottom: 20rpx;
}

.h5-camera-tip {
	font-size: 24rpx;
	opacity: 0.7;
}

.status-bar {
	position: absolute;
	top: 0;
	left: 0;
	right: 0;
	height: 120rpx;
	display: flex;
	align-items: center;
	justify-content: space-between;
	padding: 0 30rpx;
	background: linear-gradient(180deg, rgba(0,0,0,0.5) 0%, transparent 100%);
	z-index: 10;
}

.back-btn, .flash-btn {
	width: 80rpx;
	height: 80rpx;
	border-radius: 50%;
	background: rgba(0,0,0,0.3);
	display: flex;
	align-items: center;
	justify-content: center;
	color: #fff;
}

.back-icon, .flash-icon {
	font-size: 32rpx;
}

.page-title {
	color: #fff;
	font-size: 32rpx;
	font-weight: 600;
}

.guide-overlay {
	position: absolute;
	top: 50%;
	left: 50%;
	transform: translate(-50%, -50%);
	z-index: 5;
}

.guide-frame {
	position: relative;
	width: 500rpx;
	height: 600rpx;
}

.frame-corner {
	position: absolute;
	width: 60rpx;
	height: 60rpx;
	border: 4rpx solid #fff;
}

.corner-tl {
	top: 0;
	left: 0;
	border-right: none;
	border-bottom: none;
}

.corner-tr {
	top: 0;
	right: 0;
	border-left: none;
	border-bottom: none;
}

.corner-bl {
	bottom: 0;
	left: 0;
	border-right: none;
	border-top: none;
}

.corner-br {
	bottom: 0;
	right: 0;
	border-left: none;
	border-top: none;
}

.frame-center {
	position: absolute;
	bottom: -80rpx;
	left: 50%;
	transform: translateX(-50%);
	text-align: center;
}

.guide-text {
	color: #fff;
	font-size: 28rpx;
	text-shadow: 0 2rpx 4rpx rgba(0,0,0,0.5);
}

.pulse-dot {
	width: 20rpx;
	height: 20rpx;
	background: #fff;
	border-radius: 50%;
	margin: 20rpx auto 0;
	animation: pulse 2s infinite;
}

@keyframes pulse {
	0%, 100% { opacity: 0.3; transform: scale(1); }
	50% { opacity: 1; transform: scale(1.2); }
}

.tips-section {
	position: absolute;
	top: 200rpx;
	left: 30rpx;
	right: 30rpx;
	z-index: 5;
}

.tips-container {
	display: flex;
	justify-content: space-around;
}

.tip-item {
	background: rgba(0,0,0,0.5);
	border-radius: 30rpx;
	padding: 20rpx 30rpx;
	display: flex;
	align-items: center;
}

.tip-icon {
	font-size: 24rpx;
	margin-right: 10rpx;
}

.tip-text {
	color: #fff;
	font-size: 22rpx;
}

.control-section {
	position: absolute;
	bottom: 60rpx;
	left: 0;
	right: 0;
	z-index: 10;
}

.control-container {
	display: flex;
	align-items: center;
	justify-content: space-around;
	padding: 0 60rpx;
}

.control-item {
	display: flex;
	flex-direction: column;
	align-items: center;
}

.control-btn {
	width: 120rpx;
	height: 120rpx;
	border-radius: 50%;
	display: flex;
	align-items: center;
	justify-content: center;
	margin-bottom: 20rpx;
}

.control-btn.secondary {
	background: rgba(255,255,255,0.2);
	border: 2rpx solid rgba(255,255,255,0.3);
}

.control-btn.primary {
	background: #fff;
	position: relative;
}

.control-btn.capture {
	width: 140rpx;
	height: 140rpx;
}

.capture-ring {
	position: absolute;
	top: 10rpx;
	left: 10rpx;
	right: 10rpx;
	bottom: 10rpx;
	border: 4rpx solid #007aff;
	border-radius: 50%;
}

.capture-dot {
	width: 80rpx;
	height: 80rpx;
	background: #007aff;
	border-radius: 50%;
}

.control-btn.capturing .capture-dot {
	background: #ff3b30;
	animation: capturing 0.3s ease;
}

@keyframes capturing {
	0% { transform: scale(1); }
	50% { transform: scale(0.8); }
	100% { transform: scale(1); }
}

.control-icon-img {
	width: 60rpx;
	height: 60rpx;
}

.control-label {
	color: #fff;
	font-size: 22rpx;
	text-align: center;
}

.preview-header {
	text-align: center;
	padding: 60rpx 30rpx 40rpx;
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	color: #fff;
}

.preview-title {
	font-size: 36rpx;
	font-weight: 600;
	display: block;
	margin-bottom: 10rpx;
}

.preview-subtitle {
	font-size: 26rpx;
	opacity: 0.8;
}

.preview-container {
	flex: 1;
	display: flex;
	align-items: center;
	justify-content: center;
	padding: 40rpx;
	background: #f5f5f5;
}

.preview-image {
	width: 100%;
	max-height: 80vh;
	border-radius: 20rpx;
}

.preview-actions {
	display: flex;
	gap: 30rpx;
	padding: 40rpx;
	background: #fff;
}

.action-btn {
	flex: 1;
	height: 88rpx;
	border-radius: 44rpx;
	display: flex;
	align-items: center;
	justify-content: center;
}

.action-btn.secondary {
	background: #f5f5f5;
	color: #666;
}

.action-btn.primary {
	color: #fff;
}

.btn-text {
	font-size: 32rpx;
	font-weight: 600;
}
</style>
