<template>
	<view class="camera-container">
		<!-- 拍摄模式 -->
		<view v-if="!capturedImage" class="camera-mode">
			<!-- 相机预览区域 -->
			<!-- #ifdef MP-WEIXIN || MP-ALIPAY || MP-BAIDU || MP-TOUTIAO || MP-QQ -->
			<camera
				class="camera-preview"
				:device-position="devicePosition"
				:flash="flashMode"
				@error="onCameraError"
				@initdone="onCameraInit"
			>
			</camera>
			<!-- #endif -->

			<!-- #ifdef H5 -->
			<!-- H5环境下显示替代预览区域 -->
			<view class="camera-preview h5-camera-placeholder">
				<view class="h5-camera-content">
					<text class="h5-camera-text">相机预览</text>
					<text class="h5-camera-tip">点击拍照按钮选择图片</text>
				</view>
			</view>
			<!-- #endif -->

			<!-- 顶部状态栏 -->
			<view class="status-bar">
				<view class="back-btn" @touchstart="goBack" @tap="goBack">
					<text class="back-icon">←</text>
				</view>
				<text class="page-title">面诊拍摄</text>
				<view class="flash-btn" :class="{ active: flashOn }" @touchstart="toggleFlash" @tap="toggleFlash">
					<text class="flash-icon">{{ flashOn ? '💡' : '🔦' }}</text>
				</view>
			</view>

			<!-- 拍摄指导区域 -->
			<view class="guide-overlay">
				<view class="guide-frame">
					<view class="frame-corner corner-tl"></view>
					<view class="frame-corner corner-tr"></view>
					<view class="frame-corner corner-bl"></view>
					<view class="frame-corner corner-br"></view>
					<view class="frame-center">
						<text class="guide-text">请将面部置于框内</text>
						<view class="pulse-dot"></view>
					</view>
				</view>
			</view>

			<!-- 拍摄提示区域 -->
			<view class="tips-section">
				<view class="tips-container">
					<view class="tip-item" v-for="(tip, index) in shootingTips" :key="index">
						<view class="tip-icon">{{ tip.icon }}</view>
						<text class="tip-text">{{ tip.text }}</text>
					</view>
				</view>
			</view>

			<!-- 控制区域 -->
			<view class="control-section">
				<view class="control-container">
					<!-- 选择图片 -->
					<view class="control-item">
						<view class="control-btn secondary" @touchstart="chooseImage" @tap="chooseImage">
							<image class="control-icon-img" :src="pre_url + '/static/img/xuanzetupian.png'" mode="aspectFit"></image>
						</view>
						<text class="control-label">选择图片</text>
					</view>

					<!-- 拍照按钮 -->
					<view class="control-item capture">
						<view class="control-btn primary" :class="{ capturing: captureLoading }" @touchstart="takePhoto" @tap="takePhoto">
							<view class="capture-ring" v-if="!captureLoading"></view>
							<view class="capture-dot"></view>
						</view>
						<text class="control-label">拍照</text>
					</view>

					<!-- 翻转相机 -->
					<view class="control-item">
						<view class="control-btn secondary" @touchstart="flipCamera" @tap="flipCamera" :class="{ 'active': isFlipping }">
							<image class="control-icon-img" :src="pre_url + '/static/img/fanzhuanxiangji.png'" mode="aspectFit"></image>
						</view>
						<text class="control-label">翻转相机</text>
					</view>
				</view>
			</view>
		</view>

		<!-- 预览模式 -->
		<view v-else class="preview-mode">
			<!-- 顶部标题 -->
			<view class="preview-header">
				<text class="preview-title">预览图片</text>
				<text class="preview-subtitle">请确认图片清晰度</text>
			</view>

			<!-- 图片预览区域 -->
			<view class="preview-container">
				<view class="preview-image-wrapper">
					<image :src="capturedImage" mode="aspectFit" class="preview-image"></image>

					<!-- 分析点位 -->
					<view class="analysis-points">
						<view
							class="analysis-point"
							v-for="(point, index) in analysisPoints"
							:key="index"
							:style="{ left: point.x + '%', top: point.y + '%' }"
						>
							<view class="point-dot"></view>
							<view class="point-ripple"></view>
						</view>
					</view>
				</view>

				<!-- 质量检测结果 -->
				<view class="quality-check">
					<view class="quality-item" v-for="(check, index) in qualityChecks" :key="index">
						<view class="quality-icon" :class="check.status">
							<text>{{ check.status === 'pass' ? '✓' : '!' }}</text>
						</view>
						<text class="quality-text">{{ check.text }}</text>
					</view>
				</view>
			</view>

			<!-- 底部操作区域 -->
			<view class="preview-actions">
				<view class="action-btn secondary" @touchstart="retakePhoto" @tap="retakePhoto">
					<view class="btn-content">
						<text class="btn-icon">🔄</text>
						<text class="btn-text">重新拍摄</text>
					</view>
				</view>

				<view class="action-btn primary" @touchstart="confirmPhoto" @tap="confirmPhoto">
					<view class="btn-content">
						<text class="btn-icon">✨</text>
						<text class="btn-text">开始分析</text>
					</view>
					<view class="btn-glow"></view>
				</view>
			</view>
		</view>

		<!-- 加载遮罩 -->
		<view v-if="isLoading" class="loading-overlay">
			<view class="loading-content">
				<view class="loading-spinner">
					<view class="spinner-ring"></view>
					<view class="spinner-dot"></view>
				</view>
				<text class="loading-text">{{ loadingText }}</text>
			</view>
		</view>
	</view>
</template>

<script>
export default {
	name: 'FaceCamera',
	data() {
		return {
			// 云资源前缀URL
			pre_url: '',

			// 相机状态
			cameraReady: false,
			capturedImage: '',
			captureLoading: false,
			flashOn: false,
			flashMode: 'off',
			devicePosition: 'front', // 面诊使用前置摄像头
			isFlipping: false, // 是否正在切换相机

			// 加载状态
			isLoading: false,
			loadingText: '正在拍摄...',

			// 防重复调用标识
			isProcessingPayment: false,

			// 拍摄提示
			shootingTips: [
				{ icon: '💡', text: '保持充足光线' },
				{ icon: '📱', text: '手机保持稳定' },
				{ icon: '😊', text: '表情自然' },
				{ icon: '🎯', text: '对准拍摄框' }
			],

			// 分析点位（面部关键点）
			analysisPoints: [
				{ x: 25, y: 30 }, // 左眼
				{ x: 75, y: 30 }, // 右眼
				{ x: 50, y: 45 }, // 鼻子
				{ x: 35, y: 70 }, // 左嘴角
				{ x: 65, y: 70 }  // 右嘴角
			],

			// 质量检测结果
			qualityChecks: [
				{ text: '图片清晰度', status: 'pass' },
				{ text: '光线充足', status: 'pass' },
				{ text: '面部完整', status: 'pass' },
				{ text: '角度适宜', status: 'warning' }
			],

			// 面诊配置信息
			configData: {}
		}
	},
	onLoad() {
		console.log('2025-07-17 INFO-[face-camera][onLoad_001] 面诊拍摄页面加载完成');

		// 重置处理标识
		this.isProcessingPayment = false;

		// 初始化云资源前缀URL
		const app = getApp();
		this.pre_url = app.globalData.pre_url || '';

		// 初始化相机权限检查
		this.checkCameraPermission();

		// 获取面诊配置信息
		this.getFaceConfig();
	},

	// 页面卸载时重置状态
	onUnload() {
		console.log('2025-07-17 INFO-[face-camera][onUnload_002] 面诊拍摄页面卸载，重置处理标识');
		this.isProcessingPayment = false;
	},
	methods: {
		/**
		 * 检查相机权限
		 */
		checkCameraPermission() {
			console.log('2025-07-17 INFO-[face-camera][checkCameraPermission_001] 检查相机权限');

			// #ifdef MP-WEIXIN || MP-ALIPAY || MP-BAIDU || MP-TOUTIAO || MP-QQ
			// 只在小程序环境下检查相机权限
			uni.authorize({
				scope: 'scope.camera',
				success: () => {
					console.log('2025-07-17 INFO-[face-camera][checkCameraPermission_002] 相机权限获取成功');
				},
				fail: () => {
					console.log('2025-07-17 ERROR-[face-camera][checkCameraPermission_003] 相机权限获取失败');
					uni.showModal({
						title: '权限提示',
						content: '需要相机权限才能进行面诊拍摄',
						success: (res) => {
							if (res.confirm) {
								uni.openSetting();
							}
						}
					});
				}
			});
			// #endif

			// #ifdef H5
			// H5环境下直接设置相机就绪状态
			console.log('2025-07-17 INFO-[face-camera][checkCameraPermission_004] H5环境下跳过权限检查');
			this.cameraReady = true;
			// #endif
		},

		/**
		 * 相机初始化完成
		 */
		onCameraInit() {
			console.log('2025-07-17 INFO-[face-camera][onCameraInit_001] 相机初始化完成');
			this.cameraReady = true;
		},

		/**
		 * 相机错误处理
		 */
		onCameraError(error) {
			console.error('2025-07-17 ERROR-[face-camera][onCameraError_001] 相机错误:', error);
			uni.showToast({
				title: '相机启动失败',
				icon: 'none'
			});
		},

		/**
		 * 选择图片
		 */
		chooseImage() {
			console.log('2025-07-17 INFO-[face-camera][chooseImage_001] 选择图片按钮被点击');

			// 添加触觉反馈
			uni.vibrateShort();

			const app = getApp();
			if (app && app.chooseImage) {
				// 直接使用app的chooseImage方法，它会处理图片选择和上传
				app.chooseImage((imageUrls) => {
					console.log('2025-07-17 INFO-[face-camera][chooseImage_002] 选择并上传图片成功:', imageUrls);

					if (imageUrls && imageUrls.length > 0) {
						// 获取上传后的图片链接
						const imageUrl = imageUrls[0];

						// 先展示一下图片
						this.capturedImage = imageUrl;

						// 检查图片质量
						this.checkImageQuality();
					}
				});
			} else {
				// 备用方案：使用uni.chooseImage
				uni.chooseImage({
					count: 1,
					sizeType: ['original', 'compressed'],
					sourceType: ['album'],
					success: (res) => {
						console.log('2025-07-17 INFO-[face-camera][chooseImage_003] 选择图片成功:', res.tempFilePaths[0]);
						this.capturedImage = res.tempFilePaths[0];
						this.checkImageQuality();
					},
					fail: (error) => {
						console.error('2025-07-17 ERROR-[face-camera][chooseImage_004] 选择图片失败:', error);
					}
				});
			}
		},

		/**
		 * 拍照
		 */
		takePhoto() {
			console.log('2025-07-17 INFO-[face-camera][takePhoto_001] 拍照按钮被点击');

			if (this.captureLoading) {
				console.log('2025-07-17 WARNING-[face-camera][takePhoto_002] 正在拍照中，忽略重复点击');
				return;
			}

			// 添加触觉反馈
			uni.vibrateShort();

			this.captureLoading = true;
			this.loadingText = '正在拍摄...';

			// #ifdef MP-WEIXIN || MP-ALIPAY || MP-BAIDU || MP-TOUTIAO || MP-QQ
			const ctx = uni.createCameraContext();
			ctx.takePhoto({
				quality: 'high',
				success: (res) => {
					console.log('2025-07-17 INFO-[face-camera][takePhoto_003] 拍照成功:', res.tempImagePath);

					// 上传图片到云端
					this.uploadImage(res.tempImagePath);
				},
				fail: (error) => {
					console.error('2025-07-17 ERROR-[face-camera][takePhoto_004] 拍照失败:', error);
					this.captureLoading = false;
					uni.showToast({
						title: '拍照失败，请重试',
						icon: 'none'
					});
				}
			});
			// #endif

			// #ifdef H5
			// H5环境下直接选择图片
			this.chooseImage();
			// #endif
		},

		/**
		 * 上传图片
		 */
		uploadImage(imagePath) {
			console.log('2025-07-17 INFO-[face-camera][uploadImage_001] 开始上传图片:', imagePath);

			this.loadingText = '正在上传...';

			const app = getApp();
			if (app && app.uploadImage) {
				app.uploadImage(imagePath, (imageUrl) => {
					console.log('2025-07-17 INFO-[face-camera][uploadImage_002] 图片上传成功:', imageUrl);
					this.capturedImage = imageUrl;
					this.captureLoading = false;
					this.checkImageQuality();
				}, (error) => {
					console.error('2025-07-17 ERROR-[face-camera][uploadImage_003] 图片上传失败:', error);
					this.captureLoading = false;
					uni.showToast({
						title: '图片上传失败',
						icon: 'none'
					});
				});
			} else {
				// 备用方案：直接使用本地图片
				console.log('2025-07-17 WARNING-[face-camera][uploadImage_004] 未找到上传方法，使用本地图片');
				this.capturedImage = imagePath;
				this.captureLoading = false;
				this.checkImageQuality();
			}
		},

		/**
		 * 检查图片质量
		 */
		checkImageQuality() {
			console.log('2025-07-17 INFO-[face-camera][checkImageQuality_001] 开始检查图片质量');

			// 模拟质量检测
			setTimeout(() => {
				// 随机生成一些检测结果
				this.qualityChecks = [
					{ text: '图片清晰度', status: Math.random() > 0.2 ? 'pass' : 'warning' },
					{ text: '光线充足', status: Math.random() > 0.3 ? 'pass' : 'warning' },
					{ text: '面部完整', status: Math.random() > 0.1 ? 'pass' : 'warning' },
					{ text: '角度适宜', status: Math.random() > 0.4 ? 'pass' : 'warning' }
				];

				console.log('2025-07-17 INFO-[face-camera][checkImageQuality_002] 图片质量检测完成:', this.qualityChecks);
			}, 1000);
		},

		/**
		 * 翻转相机
		 */
		flipCamera() {
			console.log('2025-07-17 INFO-[face-camera][flipCamera_001] 翻转相机按钮被点击');

			if (this.isFlipping) {
				console.log('2025-07-17 WARNING-[face-camera][flipCamera_002] 正在翻转中，忽略重复点击');
				return;
			}

			// 添加触觉反馈
			uni.vibrateShort();

			this.isFlipping = true;
			this.devicePosition = this.devicePosition === 'front' ? 'back' : 'front';

			console.log('2025-07-17 INFO-[face-camera][flipCamera_003] 相机位置切换为:', this.devicePosition);

			setTimeout(() => {
				this.isFlipping = false;
			}, 500);
		},

		/**
		 * 切换闪光灯
		 */
		toggleFlash() {
			console.log('2025-07-17 INFO-[face-camera][toggleFlash_001] 闪光灯按钮被点击');

			// 添加触觉反馈
			uni.vibrateShort();

			this.flashOn = !this.flashOn;
			this.flashMode = this.flashOn ? 'on' : 'off';

			console.log('2025-07-17 INFO-[face-camera][toggleFlash_002] 闪光灯状态:', this.flashMode);
		},

		/**
		 * 重新拍摄
		 */
		retakePhoto() {
			console.log('2025-07-17 INFO-[face-camera][retakePhoto_001] 重新拍摄按钮被点击');

			// 添加触觉反馈
			uni.vibrateShort();

			this.capturedImage = '';
			this.captureLoading = false;
			this.isLoading = false;

			// 重置质量检测结果
			this.qualityChecks = [
				{ text: '图片清晰度', status: 'pass' },
				{ text: '光线充足', status: 'pass' },
				{ text: '面部完整', status: 'pass' },
				{ text: '角度适宜', status: 'warning' }
			];
		},

		/**
		 * 确认照片
		 */
		confirmPhoto() {
			console.log('2025-07-17 INFO-[face-camera][confirmPhoto_001] 确认照片按钮被点击');

			if (!this.capturedImage) {
				uni.showToast({
					title: '请先拍摄照片',
					icon: 'none'
				});
				return;
			}

			// 防止重复处理
			if (this.isProcessingPayment) {
				console.log('2025-07-17 WARNING-[face-camera][confirmPhoto_002] 正在处理中，忽略重复点击');
				return;
			}

			this.isProcessingPayment = true;

			// 添加触觉反馈
			uni.vibrateShort();

			console.log('2025-07-17 INFO-[face-camera][confirmPhoto_003] 确认使用照片:', this.capturedImage);

			// 跳转到面诊分析页面
			uni.navigateTo({
				url: `/pagesB/diagnosis/face/index?imageUrl=${encodeURIComponent(this.capturedImage)}`,
				success: () => {
					console.log('2025-07-17 INFO-[face-camera][confirmPhoto_004] 跳转到面诊分析页面成功');
				},
				fail: (error) => {
					console.error('2025-07-17 ERROR-[face-camera][confirmPhoto_005] 跳转失败:', error);
					this.isProcessingPayment = false;
				}
			});
		},

		/**
		 * 获取面诊配置信息
		 */
		getFaceConfig() {
			console.log('2025-07-17 INFO-[face-camera][getFaceConfig_001] 获取面诊配置信息');

			const app = getApp();
			app.post('ApiFaceAnalysis/getConfig', {}, (response) => {
				if (response && response.code === 1) {
					console.log('2025-07-17 INFO-[face-camera][getFaceConfig_002] 获取配置成功:', response.data);
					this.configData = response.data;
				} else {
					console.error('2025-07-17 ERROR-[face-camera][getFaceConfig_003] 获取配置失败:', response);
				}
			}, (error) => {
				console.error('2025-07-17 ERROR-[face-camera][getFaceConfig_004] 获取配置接口调用失败:', error);
			});
		},

		/**
		 * 返回上一页
		 */
		goBack() {
			console.log('2025-07-17 INFO-[face-camera][goBack_001] 返回按钮被点击');

			// 添加触觉反馈
			uni.vibrateShort();

			uni.navigateBack({
				success: () => {
					console.log('2025-07-17 INFO-[face-camera][goBack_002] 返回上一页成功');
				},
				fail: (error) => {
					console.error('2025-07-17 ERROR-[face-camera][goBack_003] 返回失败:', error);
				}
			});
		}
	}
}
</script>

<style>
/* 这里会添加完整的样式，参考舌诊拍摄页面的样式 */
.camera-container {
	position: relative;
	width: 100vw;
	height: 100vh;
	background: #000;
	overflow: hidden;
}

.camera-mode, .preview-mode {
	width: 100%;
	height: 100%;
	position: relative;
}

.camera-preview {
	width: 100%;
	height: 100%;
}

.h5-camera-placeholder {
	background: #333;
	display: flex;
	align-items: center;
	justify-content: center;
}

.h5-camera-content {
	text-align: center;
	color: #fff;
}

.h5-camera-text {
	font-size: 32rpx;
	display: block;
	margin-bottom: 20rpx;
}

.h5-camera-tip {
	font-size: 24rpx;
	opacity: 0.7;
}

.status-bar {
	position: absolute;
	top: 0;
	left: 0;
	right: 0;
	height: 120rpx;
	display: flex;
	align-items: center;
	justify-content: space-between;
	padding: 0 30rpx;
	background: linear-gradient(180deg, rgba(0,0,0,0.5) 0%, transparent 100%);
	z-index: 10;
}

.back-btn, .flash-btn {
	width: 80rpx;
	height: 80rpx;
	border-radius: 50%;
	background: rgba(0,0,0,0.3);
	display: flex;
	align-items: center;
	justify-content: center;
	color: #fff;
}

.back-icon, .flash-icon {
	font-size: 32rpx;
}

.page-title {
	color: #fff;
	font-size: 32rpx;
	font-weight: 600;
}

.guide-overlay {
	position: absolute;
	top: 50%;
	left: 50%;
	transform: translate(-50%, -50%);
	z-index: 5;
}

.guide-frame {
	position: relative;
	width: 500rpx;
	height: 600rpx;
}

.frame-corner {
	position: absolute;
	width: 60rpx;
	height: 60rpx;
	border: 4rpx solid #fff;
}

.corner-tl {
	top: 0;
	left: 0;
	border-right: none;
	border-bottom: none;
}

.corner-tr {
	top: 0;
	right: 0;
	border-left: none;
	border-bottom: none;
}

.corner-bl {
	bottom: 0;
	left: 0;
	border-right: none;
	border-top: none;
}

.corner-br {
	bottom: 0;
	right: 0;
	border-left: none;
	border-top: none;
}

.frame-center {
	position: absolute;
	bottom: -80rpx;
	left: 50%;
	transform: translateX(-50%);
	text-align: center;
}

.guide-text {
	color: #fff;
	font-size: 28rpx;
	text-shadow: 0 2rpx 4rpx rgba(0,0,0,0.5);
}

.pulse-dot {
	width: 20rpx;
	height: 20rpx;
	background: #fff;
	border-radius: 50%;
	margin: 20rpx auto 0;
	animation: pulse 2s infinite;
}

@keyframes pulse {
	0%, 100% { opacity: 0.3; transform: scale(1); }
	50% { opacity: 1; transform: scale(1.2); }
}

.tips-section {
	position: absolute;
	top: 200rpx;
	left: 30rpx;
	right: 30rpx;
	z-index: 5;
}

.tips-container {
	display: flex;
	justify-content: space-around;
}

.tip-item {
	background: rgba(0,0,0,0.5);
	border-radius: 30rpx;
	padding: 20rpx 30rpx;
	display: flex;
	align-items: center;
}

.tip-icon {
	font-size: 24rpx;
	margin-right: 10rpx;
}

.tip-text {
	color: #fff;
	font-size: 22rpx;
}

.control-section {
	position: absolute;
	bottom: 60rpx;
	left: 0;
	right: 0;
	z-index: 10;
}

.control-container {
	display: flex;
	align-items: center;
	justify-content: space-around;
	padding: 0 60rpx;
}

.control-item {
	display: flex;
	flex-direction: column;
	align-items: center;
}

.control-btn {
	width: 120rpx;
	height: 120rpx;
	border-radius: 50%;
	display: flex;
	align-items: center;
	justify-content: center;
	margin-bottom: 20rpx;
}

.control-btn.secondary {
	background: rgba(255,255,255,0.2);
	border: 2rpx solid rgba(255,255,255,0.3);
}

.control-btn.primary {
	background: #fff;
	position: relative;
}

.control-btn.capture {
	width: 140rpx;
	height: 140rpx;
}

.capture-ring {
	position: absolute;
	top: 10rpx;
	left: 10rpx;
	right: 10rpx;
	bottom: 10rpx;
	border: 4rpx solid #007aff;
	border-radius: 50%;
}

.capture-dot {
	width: 80rpx;
	height: 80rpx;
	background: #007aff;
	border-radius: 50%;
}

.control-btn.capturing .capture-dot {
	background: #ff3b30;
	animation: capturing 0.3s ease;
}

@keyframes capturing {
	0% { transform: scale(1); }
	50% { transform: scale(0.8); }
	100% { transform: scale(1); }
}

.control-icon-img {
	width: 60rpx;
	height: 60rpx;
}

.control-label {
	color: #fff;
	font-size: 22rpx;
	text-align: center;
}

.preview-header {
	text-align: center;
	padding: 60rpx 30rpx 40rpx;
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	color: #fff;
}

.preview-title {
	font-size: 36rpx;
	font-weight: 600;
	display: block;
	margin-bottom: 10rpx;
}

.preview-subtitle {
	font-size: 26rpx;
	opacity: 0.8;
}

.preview-container {
	flex: 1;
	display: flex;
	align-items: center;
	justify-content: center;
	padding: 40rpx;
	background: #f5f5f5;
}

.preview-image {
	width: 100%;
	max-height: 80vh;
	border-radius: 20rpx;
}

.preview-actions {
	display: flex;
	gap: 30rpx;
	padding: 40rpx;
	background: #fff;
}

.action-btn {
	flex: 1;
	height: 88rpx;
	border-radius: 44rpx;
	display: flex;
	align-items: center;
	justify-content: center;
}

.action-btn.secondary {
	background: #f5f5f5;
	color: #666;
}

.action-btn.primary {
	color: #fff;
}

.btn-text {
	font-size: 32rpx;
	font-weight: 600;
}
</style>
