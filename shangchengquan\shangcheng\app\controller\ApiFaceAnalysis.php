<?php
/**
 * 面诊分析API接口
 * 2025-07-17 独立的面诊接口，与舌诊分离
 */

namespace app\controller;
use think\facade\Db;
use think\facade\Log;

class ApiFaceAnalysis extends ApiCommon
{
    protected $bid = 0;
    
    public function initialize(){
        parent::initialize();
        $this->checklogin();
    }

    /**
     * 获取面诊配置信息
     */
    public function getConfig(){
        $config = Db::name('shezhen_set')->where('aid', $this->aid)->find();
        if(!$config){
            return $this->json(['code'=>0,'msg'=>'面诊功能未开启']);
        }
        
        if($config['is_open'] != 1){
            return $this->json(['code'=>0,'msg'=>'面诊功能已关闭']);
        }
        
        // 处理用户等级权限
        $userLevel = $this->member['levelid'] ?? 0;
        $freeLevel = explode(',', $config['free_level']);
        $isFree = in_array($userLevel, $freeLevel);
        
        // 检查今日免费次数
        $todayStart = strtotime(date('Y-m-d'));
        $todayCount = Db::name('shezhen_record')
            ->where('aid', $this->aid)
            ->where('mid', $this->mid)
            ->where('is_free', 1)
            ->where('diagnosis_type', 2) // 面诊
            ->where('createtime', '>=', $todayStart)
            ->count();
        
        $dailyFreeCount = intval($config['daily_free_count']) ?: 1;
        $canUseFree = $isFree && $todayCount < $dailyFreeCount;
        
        // 检查接口配置类型
        $hasOwnAliyun = !empty($config['aliyun_access_key']) && !empty($config['aliyun_secret_key']) && !empty($config['aliyun_endpoint']);
        $usePublicApi = !$hasOwnAliyun;
        
        // 如果使用公共接口，检查平台次数
        $platformCallsRemaining = 0;
        if($usePublicApi){
            $adminInfo = Db::name('admin')->where('id', $this->aid)->field('mianzhen_num')->find();
            $platformCallsRemaining = $adminInfo ? intval($adminInfo['mianzhen_num']) : 0;
        }
        
        $result = [
            'price' => $config['single_price'],
            'free_times' => $dailyFreeCount,
            'today_used' => $todayCount,
            'can_use_free' => $canUseFree,
            'is_vip' => $isFree,
            'description' => $config['description'] ?? '专业面诊分析，了解您的体质状况',
            'use_own_aliyun' => $hasOwnAliyun,
            'use_public_api' => $usePublicApi,
            'platform_calls_remaining' => $platformCallsRemaining,
            'recharge_url' => $config['recharge_url'] ?? '/pagesExb/money/recharge',
            'display_settings' => [
                'show_score' => isset($config['show_score']) ? intval($config['show_score']) : 1,
                'show_score_value' => isset($config['show_score_value']) ? intval($config['show_score_value']) : 1,
                'show_symptoms' => isset($config['show_symptoms']) ? intval($config['show_symptoms']) : 1,
                'show_face_analysis' => isset($config['show_tongue_analysis']) ? intval($config['show_tongue_analysis']) : 1, // 复用舌诊设置
                'show_care_advice' => isset($config['show_care_advice']) ? intval($config['show_care_advice']) : 1,
                'show_product_recommend' => isset($config['show_product_recommend']) ? intval($config['show_product_recommend']) : 1
            ]
        ];
        
        return $this->json(['code'=>1,'data'=>$result]);
    }

    /**
     * 面诊分析
     */
    public function analyze(){
        $imageUrl = input('post.image_url');
        $faceImageUrl = input('post.face_image_url'); // 兼容新参数
        $useFree = input('post.use_free/d', 0);
        
        // 兼容处理
        if (!$imageUrl && $faceImageUrl) {
            $imageUrl = $faceImageUrl;
        }
        
        if(!$imageUrl){
            return $this->json(['code'=>0,'msg'=>'请上传面部图片']);
        }
        
        // 获取配置信息
        $config = Db::name('shezhen_set')->where('aid', $this->aid)->find();
        if(!$config || $config['is_open'] != 1){
            return $this->json(['code'=>0,'msg'=>'面诊功能未开启']);
        }
        
        // 判断使用自有阿里云还是公共接口
        $hasOwnAliyun = !empty($config['aliyun_access_key']) && !empty($config['aliyun_secret_key']) && !empty($config['aliyun_endpoint']);
        $usePublicApi = !$hasOwnAliyun;
        
        // 如果使用公共接口，检查平台次数限制
        if($usePublicApi){
            $limitCheck = \app\common\SheZhen::checkCallLimit($this->aid);
            if ($limitCheck['status'] == 0) {
                return $this->json(['code'=>0,'msg'=>$limitCheck['msg']]);
            }
        }
        
        // 检查免费权限
        $price = $config['single_price'];
        $isFree = false;
        
        if($useFree){
            $userLevel = $this->member['levelid'] ?? 0;
            $freeLevel = explode(',', $config['free_level']);
            
            if(in_array($userLevel, $freeLevel)){
                $todayStart = strtotime(date('Y-m-d'));
                $todayCount = Db::name('shezhen_record')
                    ->where('aid', $this->aid)
                    ->where('mid', $this->mid)
                    ->where('is_free', 1)
                    ->where('diagnosis_type', 2) // 面诊
                    ->where('createtime', '>=', $todayStart)
                    ->count();
                
                if($todayCount < $config['daily_free_count']){
                    $isFree = true;
                    $price = 0;
                }
            }
        }
        
        // 检查用户余额
        if(!$isFree && $price > 0){
            if($this->member['money'] < $price){
                return $this->json(['code'=>0,'msg'=>'余额不足，请先充值']);
            }
        }
        
        // 调用面诊分析API
        $analysisResult = $this->callFaceAnalysisApi($imageUrl, $config, $hasOwnAliyun);
        
        if(!$analysisResult['success']){
            return $this->json(['code'=>0,'msg'=>$analysisResult['message']]);
        }
        
        // 生成订单号
        $orderNo = 'MZ' . date('YmdHis') . rand(1000, 9999); // MZ = 面诊
        
        // 保存分析记录
        $recordData = [
            'aid' => $this->aid,
            'bid' => $this->bid,
            'mid' => $this->mid,
            'order_no' => $orderNo,
            'tongue_image' => '', // 面诊不需要舌头图片
            'face_image' => $imageUrl,
            'sublingual_image' => '',
            'diagnosis_type' => 2, // 面诊
            'analysis_result' => json_encode($analysisResult['data'], JSON_UNESCAPED_UNICODE),
            'constitution_type' => $analysisResult['data']['physique_name'] ?? '未知',
            'constitution_score' => $analysisResult['data']['score'] ?? 0,
            'health_suggestions' => $analysisResult['data']['risk_warning'] ?? '',
            'diet_suggestions' => isset($analysisResult['data']['advices']['food']) ? 
                json_encode($analysisResult['data']['advices']['food'], JSON_UNESCAPED_UNICODE) : '',
            'exercise_suggestions' => isset($analysisResult['data']['advices']['sport']) ? 
                json_encode($analysisResult['data']['advices']['sport'], JSON_UNESCAPED_UNICODE) : '',
            'price' => $price,
            'is_free' => $isFree ? 1 : 0,
            'api_type' => $hasOwnAliyun ? 'own_aliyun' : 'public_api',
            'status' => 1,
            'createtime' => time(),
            'updatetime' => time()
        ];

        // 面诊特征数据处理
        if (isset($analysisResult['data']['features'])) {
            $faceFeatures = [];
            foreach ($analysisResult['data']['features'] as $feature) {
                if (isset($feature['feature_category']) && $feature['feature_category'] === '面部') {
                    $faceFeatures[] = $feature;
                }
            }
            $recordData['face_features'] = json_encode($faceFeatures, JSON_UNESCAPED_UNICODE);
        }
        
        Db::startTrans();
        try {
            $recordId = Db::name('shezhen_record')->insertGetId($recordData);
          
            // 扣除用户余额（如果不是免费）
            if(!$isFree && $price > 0){
                \app\common\Member::addmoney($this->aid, $this->mid, -$price, '面诊分析服务,订单号: '.$orderNo, $recordId);
            }
        
            // 如果使用公共接口，扣除站点面诊次数
            if($usePublicApi){
                $deductResult = \app\common\SheZhen::deductMianzhenNum($this->aid);
                if ($deductResult['status'] == 0) {
                    Db::rollback();
                    return $this->json(['code'=>0,'msg'=>$deductResult['msg']]);
                }
            }
            
            // 生成详细报告
            $this->generateFaceReport($recordId, $analysisResult['data']);
            
            Db::commit();
            
            // 添加操作日志
            \app\common\System::plog('面诊分析成功，订单号：' . $orderNo . '，记录ID：' . $recordId);
        
            return $this->json(['code'=>1,'data'=>['record_id'=>$recordId,'order_no'=>$orderNo,'api_type'=>$hasOwnAliyun ? 'own_aliyun' : 'public_api'],'msg'=>'分析完成']);
            
        } catch (\Exception $e) {
            Db::rollback();
            \app\common\System::plog('面诊分析异常：' . $e->getMessage());
            return $this->json(['code'=>0,'msg'=>'分析失败：'.$e->getMessage()]);
        }
    }

    /**
     * 调用面诊分析API
     */
    private function callFaceAnalysisApi($imageUrl, $config, $hasOwnAliyun){
        try {
            // 构建面诊图片数据
            $imageData = [
                'ff_image' => $imageUrl,  // 面部图片
                'gender' => '男'  // 默认性别，可以后续扩展
            ];

            // 第一步：调用检测API获取session_id
            $detectResult = \app\common\SheZhen::callDetectApi($imageData, $this->aid, 2); // 诊疗类型=2（面诊）

            if ($detectResult['status'] != 1) {
                return [
                    'success' => false,
                    'message' => $detectResult['msg'] ?? '面部图片检测失败'
                ];
            }

            // 检查是否获取到session_id
            $sessionId = $detectResult['session_id'] ?? '';
            if (empty($sessionId)) {
                Log::error('2025-07-17 ERROR-[ApiFaceAnalysis][callFaceAnalysisApi] 检测成功但未获取到session_id');
                return [
                    'success' => false,
                    'message' => '检测成功但未获取到会话ID，无法生成报告'
                ];
            }

            Log::info('2025-07-17 INFO-[ApiFaceAnalysis][callFaceAnalysisApi] 面诊检测成功，session_id: ' . $sessionId);

            // 第二步：调用报告生成API
            $answers = []; // 可以根据需要添加问题答案
            $reportResult = \app\common\SheZhen::callReportApi($sessionId, $answers, $this->aid);

            if ($reportResult['status'] == 1 && isset($reportResult['data']['data'])) {
                // 按照旧版本逻辑处理数据结构
                $reportData = $reportResult['data']['data'];
                Log::info('2025-07-17 INFO-[ApiFaceAnalysis][callFaceAnalysisApi] 面诊报告生成成功');

                // 直接返回API的原始数据，保持数据完整性
                $formattedResult = [
                    'api_source' => $hasOwnAliyun ? 'own_aliyun' : 'public_api',
                    'session_id' => $sessionId,
                    'session_record_id' => $detectResult['session_record_id'] ?? 0,
                    'report_url' => $reportResult['report_url'] ?? '',
                    // 直接保存完整的API返回数据
                    'raw_report_data' => $reportData,
                    // 为了兼容现有接口，提取一些常用字段
                    'physique_name' => $reportData['physique_name'] ?? '未知体质',
                    'score' => $reportData['score'] ?? 0,
                    'risk_warning' => $reportData['risk_warning'] ?? '',
                    'advices' => $reportData['advices'] ?? [],
                    'features' => $reportData['features'] ?? [],
                    'typical_symptom' => $reportData['typical_symptom'] ?? '',
                    'syndrome_name' => $reportData['syndrome_name'] ?? '',
                    'syndrome_introduction' => $reportData['syndrome_introduction'] ?? '',
                    'physique_analysis' => $reportData['physique_analysis'] ?? ''
                ];

                return [
                    'success' => true,
                    'data' => $formattedResult,
                    'message' => '面诊分析完成'
                ];
            } else {
                Log::warning('2025-07-17 WARNING-[ApiFaceAnalysis][callFaceAnalysisApi] 面诊报告生成失败: ' . $reportResult['msg']);

                // 返回基本检测结果和错误信息
                $formattedResult = [
                    'physique_name' => '检测中',
                    'score' => 0,
                    'risk_warning' => '检测成功，报告生成中，请稍后查看详细结果。',
                    'api_source' => $hasOwnAliyun ? 'own_aliyun' : 'public_api',
                    'session_id' => $sessionId,
                    'session_record_id' => $detectResult['session_record_id'] ?? 0,
                    'report_error' => $reportResult['msg'] ?? '报告生成失败'
                ];

                return [
                    'success' => true,
                    'data' => $formattedResult,
                    'message' => '检测完成，但报告生成失败: ' . $reportResult['msg']
                ];
            }

        } catch (\Exception $e) {
            Log::error('2025-07-17 ERROR-[ApiFaceAnalysis][callFaceAnalysisApi] ' . $e->getMessage());
            return [
                'success' => false,
                'message' => '面诊分析服务异常，请稍后重试'
            ];
        }
    }

    /**
     * 生成面诊详细报告
     */
    private function generateFaceReport($recordId, $analysisData){
        Log::info('ApiFaceAnalysis生成面诊详细报告开始，recordId:' . $recordId);

        // 获取基础用户信息
        $record = Db::name('shezhen_record')->where('id', $recordId)->find();
        if(!$record) return false;

        // 构建报告数据
        $reportData = [
            'aid' => $record['aid'],
            'bid' => $record['bid'],
            'mid' => $record['mid'],
            'record_id' => $recordId,
            'report_title' => '面诊分析报告 - ' . ($analysisData['physique_name'] ?? '未知'),
            'api_source' => $analysisData['api_source'] ?? 'unknown',
            'status' => 1,
            'createtime' => time(),
            'updatetime' => time()
        ];

        // 如果有完整的API原始数据，直接保存
        if (isset($analysisData['raw_report_data'])) {
            $rawData = $analysisData['raw_report_data'];

            // 保存完整的原始报告数据
            $reportData['raw_report_json'] = json_encode($rawData, JSON_UNESCAPED_UNICODE);

            // 提取关键字段用于查询和显示
            $reportData['constitution_type'] = $rawData['physique_name'] ?? '';
            $reportData['constitution_score'] = $rawData['score'] ?? 0;
            $reportData['health_status'] = $this->getHealthStatus($rawData['score'] ?? 0);
            $reportData['constitution_analysis'] = $rawData['physique_analysis'] ?? '';
            $reportData['typical_symptoms'] = $rawData['typical_symptom'] ?? '';
            $reportData['risk_assessment'] = $rawData['risk_warning'] ?? '';
            $reportData['syndrome_name'] = $rawData['syndrome_name'] ?? '';
            $reportData['syndrome_introduction'] = $rawData['syndrome_introduction'] ?? '';

            // 处理面部特征
            if (isset($rawData['features']) && is_array($rawData['features'])) {
                $faceFeatures = [];
                foreach ($rawData['features'] as $feature) {
                    if (isset($feature['feature_category']) && $feature['feature_category'] === '面部') {
                        $faceFeatures[] = [
                            'group' => $feature['feature_group'] ?? '',
                            'name' => $feature['feature_name'] ?? '',
                            'situation' => $feature['feature_situation'] ?? '',
                            'interpret' => $feature['feature_interpret'] ?? ''
                        ];
                    }
                }
                $reportData['face_features'] = json_encode($faceFeatures, JSON_UNESCAPED_UNICODE);
            }

            // 处理改善建议
            if (isset($rawData['advices']) && is_array($rawData['advices'])) {
                $improvementPlan = [];

                // 饮食建议
                if (isset($rawData['advices']['food'])) {
                    $dietAdvice = [];
                    foreach ($rawData['advices']['food'] as $food) {
                        $dietAdvice[] = $food['title'] . ': ' . $food['advice'];
                    }
                    $improvementPlan['diet'] = implode(' | ', $dietAdvice);
                }

                // 运动建议
                if (isset($rawData['advices']['sport'])) {
                    $sportAdvice = [];
                    foreach ($rawData['advices']['sport'] as $sport) {
                        $sportAdvice[] = $sport['title'] . ': ' . $sport['advice'];
                    }
                    $improvementPlan['exercise'] = implode(' | ', $sportAdvice);
                }

                // 生活建议
                if (isset($rawData['advices']['sleep'])) {
                    $lifestyleAdvice = [];
                    foreach ($rawData['advices']['sleep'] as $sleep) {
                        $lifestyleAdvice[] = $sleep['title'] . ': ' . $sleep['advice'];
                    }
                    $improvementPlan['lifestyle'] = implode(' | ', $lifestyleAdvice);
                }

                $reportData['improvement_plan'] = json_encode($improvementPlan, JSON_UNESCAPED_UNICODE);
            }

            // 保存所有建议的完整JSON
            if (isset($rawData['advices'])) {
                $reportData['all_advices'] = json_encode($rawData['advices'], JSON_UNESCAPED_UNICODE);
            }
        }

        $reportData['follow_up_advice'] = '建议定期进行面诊检查，关注体质变化，及时调整生活方式。';

        // 插入报告数据
        return Db::name('shezhen_report')->insert($reportData);
    }

    /**
     * 根据得分获取健康状态
     */
    private function getHealthStatus($score){
        if($score >= 90) return '健康状态良好';
        if($score >= 80) return '健康状态较好';
        if($score >= 70) return '健康状态一般';
        if($score >= 60) return '健康状态偏差';
        return '健康状态较差';
    }
}
