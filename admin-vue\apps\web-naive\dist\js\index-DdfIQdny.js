import{q as Me,g as q,r as X,l as Se,m as re,v as Te,Z as g,a0 as Pe,a1 as De,a2 as Z,a3 as J,y as I,o as Q,a4 as Y,a5 as Fe}from"./bootstrap-B_sue86n.js";import{N as Oe}from"./Input-B6dOr09O.js";import{u as Ce}from"./use-locale-zaiRAV2Y.js";import{u as Ae}from"./use-merged-state-lZNesr9e.js";import{d as le,h as u,g as B,E as ke,w as Ue,c as _e,z as Ee}from"../jse/index-index-UaL0SrHU.js";import{A as $e}from"./Add-GRFR-Jxi.js";import"./Suffix-B_0ZYbmE.js";import"./Eye-tfCY-2yO.js";const He=le({name:"Remove",render(){return u("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 512 512"},u("line",{x1:"400",y1:"256",x2:"112",y2:"256",style:`
        fill: none;
        stroke: currentColor;
        stroke-linecap: round;
        stroke-linejoin: round;
        stroke-width: 32px;
      `}))}}),je=Me([q("input-number-suffix",`
 display: inline-block;
 margin-right: 10px;
 `),q("input-number-prefix",`
 display: inline-block;
 margin-left: 10px;
 `)]);function Le(t){return t==null||typeof t=="string"&&t.trim()===""?null:Number(t)}function ze(t){return t.includes(".")&&(/^(-)?\d+.*(\.|0)$/.test(t)||/^-?\d*$/.test(t))||t==="-"||t==="-0"}function H(t){return t==null?!0:!Number.isNaN(t)}function ee(t,i){return typeof t!="number"?"":i===void 0?String(t):t.toFixed(i)}function j(t){if(t===null)return null;if(typeof t=="number")return t;{const i=Number(t);return Number.isNaN(i)?null:i}}const te=800,ne=100,Ge=Object.assign(Object.assign({},re.props),{autofocus:Boolean,loading:{type:Boolean,default:void 0},placeholder:String,defaultValue:{type:Number,default:null},value:Number,step:{type:[Number,String],default:1},min:[Number,String],max:[Number,String],size:String,disabled:{type:Boolean,default:void 0},validator:Function,bordered:{type:Boolean,default:void 0},showButton:{type:Boolean,default:!0},buttonPlacement:{type:String,default:"right"},inputProps:Object,readonly:Boolean,clearable:Boolean,keyboard:{type:Object,default:{}},updateValueOnInput:{type:Boolean,default:!0},round:{type:Boolean,default:void 0},parse:Function,format:Function,precision:Number,status:String,"onUpdate:value":[Function,Array],onUpdateValue:[Function,Array],onFocus:[Function,Array],onBlur:[Function,Array],onClear:[Function,Array],onChange:[Function,Array]}),et=le({name:"InputNumber",props:Ge,slots:Object,setup(t){const{mergedBorderedRef:i,mergedClsPrefixRef:p,mergedRtlRef:P}=Se(t),a=re("InputNumber","-input-number",je,Fe,t,p),{localeRef:v}=Ce("InputNumber"),R=Te(t),{mergedSizeRef:ie,mergedDisabledRef:ue,mergedStatusRef:ae}=R,d=B(null),L=B(null),z=B(null),D=B(t.defaultValue),oe=ke(t,"value"),f=Ae(oe,D),h=B(""),F=e=>{const n=String(e).split(".")[1];return n?n.length:0},se=e=>{const n=[t.min,t.max,t.step,e].map(r=>r===void 0?0:F(r));return Math.max(...n)},de=g(()=>{const{placeholder:e}=t;return e!==void 0?e:v.value.placeholder}),M=g(()=>{const e=j(t.step);return e!==null?e===0?1:Math.abs(e):1}),G=g(()=>{const e=j(t.min);return e!==null?e:null}),K=g(()=>{const e=j(t.max);return e!==null?e:null}),b=()=>{const{value:e}=f;if(H(e)){const{format:n,precision:r}=t;n?h.value=n(e):e===null||r===void 0||F(e)>r?h.value=ee(e,void 0):h.value=ee(e,r)}else h.value=String(e)};b();const y=e=>{const{value:n}=f;if(e===n){b();return}const{"onUpdate:value":r,onUpdateValue:l,onChange:s}=t,{nTriggerFormInput:c,nTriggerFormChange:x}=R;s&&I(s,e),l&&I(l,e),r&&I(r,e),D.value=e,c(),x()},o=({offset:e,doUpdateIfValid:n,fixPrecision:r,isInputing:l})=>{const{value:s}=h;if(l&&ze(s))return!1;const c=(t.parse||Le)(s);if(c===null)return n&&y(null),null;if(H(c)){const x=F(c),{precision:N}=t;if(N!==void 0&&N<x&&!r)return!1;let m=Number.parseFloat((c+e).toFixed(N!=null?N:se(c)));if(H(m)){const{value:E}=K,{value:$}=G;if(E!==null&&m>E){if(!n||l)return!1;m=E}if($!==null&&m<$){if(!n||l)return!1;m=$}return t.validator&&!t.validator(m)?!1:(n&&y(m),m)}}return!1},fe=g(()=>o({offset:0,doUpdateIfValid:!1,isInputing:!1,fixPrecision:!1})===!1),O=g(()=>{const{value:e}=f;if(t.validator&&e===null)return!1;const{value:n}=M;return o({offset:-n,doUpdateIfValid:!1,isInputing:!1,fixPrecision:!1})!==!1}),C=g(()=>{const{value:e}=f;if(t.validator&&e===null)return!1;const{value:n}=M;return o({offset:+n,doUpdateIfValid:!1,isInputing:!1,fixPrecision:!1})!==!1});function ce(e){const{onFocus:n}=t,{nTriggerFormFocus:r}=R;n&&I(n,e),r()}function me(e){var n,r;if(e.target===((n=d.value)===null||n===void 0?void 0:n.wrapperElRef))return;const l=o({offset:0,doUpdateIfValid:!0,isInputing:!1,fixPrecision:!0});if(l!==!1){const x=(r=d.value)===null||r===void 0?void 0:r.inputElRef;x&&(x.value=String(l||"")),f.value===l&&b()}else b();const{onBlur:s}=t,{nTriggerFormBlur:c}=R;s&&I(s,e),c(),Ee(()=>{b()})}function ve(e){const{onClear:n}=t;n&&I(n,e)}function A(){const{value:e}=C;if(!e){_();return}const{value:n}=f;if(n===null)t.validator||y(W());else{const{value:r}=M;o({offset:r,doUpdateIfValid:!0,isInputing:!1,fixPrecision:!0})}}function k(){const{value:e}=O;if(!e){U();return}const{value:n}=f;if(n===null)t.validator||y(W());else{const{value:r}=M;o({offset:-r,doUpdateIfValid:!0,isInputing:!1,fixPrecision:!0})}}const he=ce,ge=me;function W(){if(t.validator)return null;const{value:e}=G,{value:n}=K;return e!==null?Math.max(0,e):n!==null?Math.min(0,n):0}function pe(e){ve(e),y(null)}function be(e){var n,r,l;!((n=z.value)===null||n===void 0)&&n.$el.contains(e.target)&&e.preventDefault(),!((r=L.value)===null||r===void 0)&&r.$el.contains(e.target)&&e.preventDefault(),(l=d.value)===null||l===void 0||l.activate()}let V=null,w=null,S=null;function U(){S&&(window.clearTimeout(S),S=null),V&&(window.clearInterval(V),V=null)}let T=null;function _(){T&&(window.clearTimeout(T),T=null),w&&(window.clearInterval(w),w=null)}function xe(){U(),S=window.setTimeout(()=>{V=window.setInterval(()=>{k()},ne)},te),Q("mouseup",document,U,{once:!0})}function Ie(){_(),T=window.setTimeout(()=>{w=window.setInterval(()=>{A()},ne)},te),Q("mouseup",document,_,{once:!0})}const ye=()=>{w||A()},Ve=()=>{V||k()};function we(e){var n,r;if(e.key==="Enter"){if(e.target===((n=d.value)===null||n===void 0?void 0:n.wrapperElRef))return;o({offset:0,doUpdateIfValid:!0,isInputing:!1,fixPrecision:!0})!==!1&&((r=d.value)===null||r===void 0||r.deactivate())}else if(e.key==="ArrowUp"){if(!C.value||t.keyboard.ArrowUp===!1)return;e.preventDefault(),o({offset:0,doUpdateIfValid:!0,isInputing:!1,fixPrecision:!0})!==!1&&A()}else if(e.key==="ArrowDown"){if(!O.value||t.keyboard.ArrowDown===!1)return;e.preventDefault(),o({offset:0,doUpdateIfValid:!0,isInputing:!1,fixPrecision:!0})!==!1&&k()}}function Ne(e){h.value=e,t.updateValueOnInput&&!t.format&&!t.parse&&t.precision===void 0&&o({offset:0,doUpdateIfValid:!0,isInputing:!0,fixPrecision:!1})}Ue(f,()=>{b()});const Be={focus:()=>{var e;return(e=d.value)===null||e===void 0?void 0:e.focus()},blur:()=>{var e;return(e=d.value)===null||e===void 0?void 0:e.blur()},select:()=>{var e;return(e=d.value)===null||e===void 0?void 0:e.select()}},Re=Pe("InputNumber",P,p);return Object.assign(Object.assign({},Be),{rtlEnabled:Re,inputInstRef:d,minusButtonInstRef:L,addButtonInstRef:z,mergedClsPrefix:p,mergedBordered:i,uncontrolledValue:D,mergedValue:f,mergedPlaceholder:de,displayedValueInvalid:fe,mergedSize:ie,mergedDisabled:ue,displayedValue:h,addable:C,minusable:O,mergedStatus:ae,handleFocus:he,handleBlur:ge,handleClear:pe,handleMouseDown:be,handleAddClick:ye,handleMinusClick:Ve,handleAddMousedown:Ie,handleMinusMousedown:xe,handleKeyDown:we,handleUpdateDisplayedValue:Ne,mergedTheme:a,inputThemeOverrides:{paddingSmall:"0 8px 0 10px",paddingMedium:"0 8px 0 12px",paddingLarge:"0 8px 0 14px"},buttonThemeOverrides:_e(()=>{const{self:{iconColorDisabled:e}}=a.value,[n,r,l,s]=De(e);return{textColorTextDisabled:`rgb(${n}, ${r}, ${l})`,opacityDisabled:`${s}`}})})},render(){const{mergedClsPrefix:t,$slots:i}=this,p=()=>u(Z,{text:!0,disabled:!this.minusable||this.mergedDisabled||this.readonly,focusable:!1,theme:this.mergedTheme.peers.Button,themeOverrides:this.mergedTheme.peerOverrides.Button,builtinThemeOverrides:this.buttonThemeOverrides,onClick:this.handleMinusClick,onMousedown:this.handleMinusMousedown,ref:"minusButtonInstRef"},{icon:()=>J(i["minus-icon"],()=>[u(Y,{clsPrefix:t},{default:()=>u(He,null)})])}),P=()=>u(Z,{text:!0,disabled:!this.addable||this.mergedDisabled||this.readonly,focusable:!1,theme:this.mergedTheme.peers.Button,themeOverrides:this.mergedTheme.peerOverrides.Button,builtinThemeOverrides:this.buttonThemeOverrides,onClick:this.handleAddClick,onMousedown:this.handleAddMousedown,ref:"addButtonInstRef"},{icon:()=>J(i["add-icon"],()=>[u(Y,{clsPrefix:t},{default:()=>u($e,null)})])});return u("div",{class:[`${t}-input-number`,this.rtlEnabled&&`${t}-input-number--rtl`]},u(Oe,{ref:"inputInstRef",autofocus:this.autofocus,status:this.mergedStatus,bordered:this.mergedBordered,loading:this.loading,value:this.displayedValue,onUpdateValue:this.handleUpdateDisplayedValue,theme:this.mergedTheme.peers.Input,themeOverrides:this.mergedTheme.peerOverrides.Input,builtinThemeOverrides:this.inputThemeOverrides,size:this.mergedSize,placeholder:this.mergedPlaceholder,disabled:this.mergedDisabled,readonly:this.readonly,round:this.round,textDecoration:this.displayedValueInvalid?"line-through":void 0,onFocus:this.handleFocus,onBlur:this.handleBlur,onKeydown:this.handleKeyDown,onMousedown:this.handleMouseDown,onClear:this.handleClear,clearable:this.clearable,inputProps:this.inputProps,internalLoadingBeforeSuffix:!0},{prefix:()=>{var a;return this.showButton&&this.buttonPlacement==="both"?[p(),X(i.prefix,v=>v?u("span",{class:`${t}-input-number-prefix`},v):null)]:(a=i.prefix)===null||a===void 0?void 0:a.call(i)},suffix:()=>{var a;return this.showButton?[X(i.suffix,v=>v?u("span",{class:`${t}-input-number-suffix`},v):null),this.buttonPlacement==="right"?p():null,P()]:(a=i.suffix)===null||a===void 0?void 0:a.call(i)}}))}});export{et as NInputNumber,Ge as inputNumberProps};
