import{_ as P,b as B,c as T,d as w,a as A,f as b,e as y,N as S,h as V,g as v,i as C}from"./layout.vue_vue_type_script_setup_true_lang-juJTdT6m.js";import{A as I,_ as N,a as O}from"./authentication-Ct6p5qmb.js";import{_ as D,a as E}from"./theme-toggle.vue_vue_type_script_setup_true_lang-0F8smxyW.js";import{_ as o}from"./bootstrap-B_sue86n.js";import{g as a,k as t,l as s}from"../jse/index-index-UaL0SrHU.js";import"./avatar.vue_vue_type_script_setup_true_lang-BjMx0Zs0.js";import"./use-modal-DH4BF1xL.js";import"./use-preferences-D9nCK1i-.js";import"./TabsList.vue_vue_type_script_setup_true_lang-DMrMu-Xs.js";import"./rotate-cw-weNjtwdP.js";import"./index-C-lrPiV1.js";const r=a(!1);function h(){function e(){r.value=!0}return{handleOpenPreference:e,openPreferences:r}}const n={};function c(e,i){return s(),t("div")}const k=o(n,[["render",c]]);export{I as AuthPageLayout,N as AuthenticationColorToggle,O as AuthenticationLayoutToggle,P as BasicLayout,B as Breadcrumb,T as CheckUpdates,w as GlobalSearch,A as IFrameRouterView,k as IFrameView,D as LanguageToggle,b as LockScreen,y as LockScreenModal,S as Notification,V as Preferences,v as PreferencesButton,E as ThemeToggle,C as UserDropdown,h as useOpenPreferences};
