import{G as b,u as f,H as C,I as r,J as n}from"./bootstrap-B_sue86n.js";import{_ as A}from"./modal.vue_vue_type_script_setup_true_lang-D9vNRyC6.js";import{_ as v}from"./page.vue_vue_type_script_setup_true_lang-BLEI3TAF.js";import{d as _,A as x,l as B,B as a,C as o,s as l,I as s}from"../jse/index-index-UaL0SrHU.js";import{u as N}from"./use-modal-DH4BF1xL.js";const D=_({__name:"basic",setup(g){const i=b(),[u,p]=f({commonConfig:{componentProps:{class:"w-full"}},layout:"horizontal",wrapperClass:"grid-cols-1 md:grid-cols-2 lg:grid-cols-3",handleSubmit:t=>{i.success(`表单数据：${JSON.stringify(t)}`)},schema:[{component:"ApiSelect",componentProps:{afterFetch:t=>t.map(e=>({label:e.name,value:e.path})),api:n},fieldName:"api",label:"ApiSelect",rules:"required"},{component:"ApiTreeSelect",componentProps:{api:n,childrenField:"children",labelField:"name",valueField:"path"},fieldName:"apiTree",label:"ApiTreeSelect",rules:"required"},{component:"Input",fieldName:"string",label:"String",rules:"required"},{component:"InputNumber",fieldName:"number",label:"Number",rules:"required"},{component:"RadioGroup",fieldName:"radio",label:"Radio",componentProps:{options:[{value:"A",label:"A"},{value:"B",label:"B"},{value:"C",label:"C"},{value:"D",label:"D"},{value:"E",label:"E"}]},rules:"selectRequired"},{component:"RadioGroup",fieldName:"radioButton",label:"RadioButton",componentProps:{isButton:!0,class:"flex flex-wrap",options:[{value:"A",label:"选项A"},{value:"B",label:"选项B"},{value:"C",label:"选项C"},{value:"D",label:"选项D"},{value:"E",label:"选项E"}]},rules:"selectRequired"},{component:"CheckboxGroup",fieldName:"checkbox",label:"Checkbox",componentProps:{options:[{value:"A",label:"选项A"},{value:"B",label:"选项B"},{value:"C",label:"选项C"}]},rules:"selectRequired"},{component:"DatePicker",fieldName:"date",label:"Date",rules:"required"},{component:"Input",fieldName:"textArea",label:"TextArea",componentProps:{type:"textarea"},rules:"required"}]});function m(){p.setValues({string:"string",number:123,radio:"B",radioButton:"C",checkbox:["A","C"],date:Date.now()})}const[d,c]=N({connectedComponent:A});return(t,e)=>(B(),x(l(v),{description:"表单适配器重新包装了CheckboxGroup和RadioGroup，可以通过options属性传递选项数据（选项数据将作为子组件的属性）",title:"表单演示"},{default:a(()=>[o(l(C),{title:"基础表单"},{"header-extra":a(()=>[o(l(r),{type:"primary",onClick:m},{default:a(()=>e[1]||(e[1]=[s("设置表单值")])),_:1,__:[1]}),o(l(r),{type:"primary",onClick:e[0]||(e[0]=h=>l(c).open()),class:"ml-2"},{default:a(()=>e[2]||(e[2]=[s(" 打开弹窗 ")])),_:1,__:[2]})]),default:a(()=>[o(l(u))]),_:1}),o(l(d))]),_:1}))}});export{D as default};
