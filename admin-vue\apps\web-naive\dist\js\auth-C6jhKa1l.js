import{$ as a}from"./bootstrap-B_sue86n.js";import{A as r}from"./authentication-Ct6p5qmb.js";import{d as s,c as t,J as o,A as c,l as i,s as e}from"../jse/index-index-UaL0SrHU.js";import"./theme-toggle.vue_vue_type_script_setup_true_lang-0F8smxyW.js";import"./use-preferences-D9nCK1i-.js";const A=s({__name:"auth",setup(m){const p=t(()=>o.app.name),n=t(()=>o.logo.source);return(l,u)=>(i(),c(e(r),{"app-name":p.value,logo:n.value,"page-description":e(a)("authentication.pageDesc"),"page-title":e(a)("authentication.pageTitle")},null,8,["app-name","logo","page-description","page-title"]))}});export{A as default};
