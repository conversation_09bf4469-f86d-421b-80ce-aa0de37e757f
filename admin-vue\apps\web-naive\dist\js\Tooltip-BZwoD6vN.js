import{N as i,p}from"./Popover-ulf1mwTf.js";import{l as a,m as r,D as d}from"./bootstrap-B_sue86n.js";import{d as l,h as c,g as m,c as h}from"../jse/index-index-UaL0SrHU.js";function u(e,o){if(!e)return;const t=document.createElement("a");t.href=e,o!==void 0&&(t.download=o),document.body.appendChild(t),t.click(),document.body.removeChild(t)}function T(e,o){u(e,o)}const f=Object.assign(Object.assign({},p),r.props),P=l({name:"Tooltip",props:f,slots:Object,__popover__:!0,setup(e){const{mergedClsPrefixRef:o}=a(e),t=r("Tooltip","-tooltip",void 0,d,e,o),s=m(null);return Object.assign(Object.assign({},{syncPosition(){s.value.syncPosition()},setShow(n){s.value.setShow(n)}}),{popoverRef:s,mergedTheme:t,popoverThemeOverrides:h(()=>t.value.self)})},render(){const{mergedTheme:e,internalExtraClass:o}=this;return c(i,Object.assign(Object.assign({},this.$props),{theme:e.peers.Popover,themeOverrides:e.peerOverrides.Popover,builtinThemeOverrides:this.popoverThemeOverrides,internalExtraClass:o.concat("tooltip"),ref:"popoverRef"}),this.$slots)}});export{P as N,u as d,T as p};
